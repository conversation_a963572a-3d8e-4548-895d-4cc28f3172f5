<?xml version="1.0" encoding="utf-8"?>

<root>
    <data name="server_running">
        <value>Server is running...</value>
    </data>
    <data name="duplicated_employee_number_found_please_login_with_email">
        <value>Duplicated employee number was found please login with email</value>
    </data>
    <data name="employee_number_was_not_found_please_login_with_email">
        <value>Username or password is incorrect</value>
    </data>
    <data name="employee_number_already_exists">
        <value>Employee number is already existed</value>
    </data>
    <data name="file_is_not_found">
        <value>Failed to access the file. Please re-upload it to the library.</value>
    </data>
    <data name="update_number_0">
        <value>Update number {0}</value>
    </data>
    <data name="user_has_standard_subtask">
        <value>The member cannot be removed because there are assigned tasks.</value>
    </data>
    <data name="admin">
        <value>Admin</value>
    </data>
    <data name="user">
        <value>User</value>
    </data>
    <data name="service_data">
        <value>Service data</value>
    </data>
    <data name="service_identification_card">
        <value>Service identification card</value>
    </data>
    <data name="service_classification">
        <value>Service classification</value>
    </data>
    <data name="concerned_departments">
        <value>Concerned departments</value>
    </data>
    <data name="main_service_name">
        <value>Main service name</value>
    </data>
    <data name="sub_service">
        <value>Sub service</value>
    </data>
    <data name="supplementary_service">
        <value>Supplementary service</value>
    </data>
    <data name="general_administration">
        <value>General administration</value>
    </data>
    <data name="service_description">
        <value>Service description</value>
    </data>
    <data name="service_type">
        <value>Service type</value>
    </data>
    <data name="service_limitation">
        <value>Service limitation</value>
    </data>
    <data name="package">
        <value>Package</value>
    </data>
    <data name="target_audience">
        <value>Customer categories (target groups)</value>
    </data>
    <data name="no_categories">
        <value>No categories</value>
    </data>
    <data name="service_provider_channels">
        <value>Service delivery channels</value>
    </data>
    <data name="no_service_channels">
        <value>No service delivery channels</value>
    </data>
    <data name="service_delivery_channels">
        <value>Service handover channels</value>
    </data>
    <data name="average_completion_time">
        <value>Average service completion time</value>
    </data>
    <data name="service_fees">
        <value>Service fees</value>
    </data>
    <data name="service_hours">
        <value>Service hours</value>
    </data>
    <data name="payment_method">
        <value>Payment method</value>
    </data>
    <data name="is_service_related">
        <value>Is the service connected</value>
    </data>
    <data name="related_agencies">
        <value>Connected/integrated entities with the service</value>
    </data>
    <data name="username_and_password_are_mandatory">
        <value>Username and password are mandatory.</value>
    </data>
    <data name="username_or_password_is_incorrect">
        <value>Username or password is incorrect.</value>
    </data>
    <data name="item_not_found">
        <value>Item not found.</value>
    </data>
    <data name="operation_successful">
        <value>Operation successful.</value>
    </data>
    <data name="you_cant_delete_yourself">
        <value>You cannot delete yourself.</value>
    </data>
    <data name="you_have_to_enter_your_parents_email_to_continue">
        <value>You have to enter your parent's email to continue.</value>
    </data>
    <data name="you_have_to_acknowledge_that_your_parents_are_aware_of_your_registration_at_the_platform">
        <value>You have to acknowledge that your parents are aware of your registration at the platform.</value>
    </data>
    <data name="your_account_has_been_created_check_your_email_for_further_instructions">
        <value>Your account has been created. Check your email for further instructions</value>
    </data>
    <data name="recaptcha_code_is_invalid">
        <value>Recaptcha code is invalid.</value>
    </data>
    <data name="email_is_not_registered">
        <value>Email is not registered.</value>
    </data>
    <data name="follow_the_instructions_sent_to_your_email">
        <value>Follow the instructions sent to your email.</value>
    </data>
    <data name="password_has_been_reset_successfully">
        <value>Password has been reset successfully.</value>
    </data>
    <data
        name="your_account_has_been_created_successfully_you_wont_be_able_to_access_your_account_until_after_your_interview">
        <value>Your account has been created successfully. You won't be able to access your account until after your
            interview.
        </value>
    </data>
    <data name="error_creating_the_student_account">
        <value>Error creating the student account.</value>
    </data>
    <data name="error_creating_the_teacher_account">
        <value>Error creating the teacher account.</value>
    </data>
    <data name="team_leader">
        <value>Team Leader</value>
    </data>
    <data name="strategic_department">
        <value>Strategic Department</value>
    </data>
    <data name="guest">
        <value>Guest</value>
    </data>
    <data name="data_entry">
        <value>Data Entry</value>
    </data>
    <data name="kpi_management">
        <value>Kpi Management</value>
    </data>
    <data name="kpi_data_entry">
        <value>Kpi Data Entry</value>
    </data>
    <data name="late">
        <value>Behind plan</value>
    </data>
    <data name="close">
        <value>Close to fulfill plan</value>
    </data>
    <data name="achieved">
        <value>Achieved target</value>
    </data>
    <data name="exceeded">
        <value>Beyond target</value>
    </data>
    <data name="not_yet">
        <value>Not yet</value>
    </data>
    <data name="strategic">
        <value>Strategic</value>
    </data>
    <data name="operational">
        <value>Operational</value>
    </data>
    <data name="number">
        <value>Number</value>
    </data>
    <data name="percentage">
        <value>Percentage</value>
    </data>
    <data name="average">
        <value>Average</value>
    </data>
    <data name="rate">
        <value>Rate</value>
    </data>
    <data name="currency">
        <value>Currency</value>
    </data>
    <data name="time">
        <value>Time</value>
    </data>
    <data name="minute">
        <value>Minute</value>
    </data>
    <data name="hour">
        <value>Hour</value>
    </data>
    <data name="decrease">
        <value>Decrease</value>
    </data>
    <data name="increase">
        <value>Increase</value>
    </data>
    <data name="annual">
        <value>Annual</value>
    </data>
    <data name="semi_annual">
        <value>Semi Annual</value>
    </data>
    <data name="quarter">
        <value>Quarter</value>
    </data>
    <data name="month">
        <value>Month</value>
    </data>
    <data name="kpi_data_entry_method_upon_request">
        <value>Upon request</value>
    </data>
    <data name="kpi_data_entry_method_dynamic">
        <value>Dynamic</value>
    </data>
    <data name="kpi_calculation_method_last">
        <value>Last</value>
    </data>
    <data name="kpi_calculation_method_repeat">
        <value>Repeat</value>
    </data>
    <data name="kpi_calculation_method_split">
        <value>Split</value>
    </data>
    <data name="internal">
        <value>Internal</value>
    </data>
    <data name="external">
        <value>External</value>
    </data>
    <data name="name">
        <value>Name</value>
    </data>
    <data name="units">
        <value>Units</value>
    </data>
    <data name="formula">
        <value>Formula</value>
    </data>
    <data name="creation_year">
        <value>Creation Year</value>
    </data>
    <data name="cycle">
        <value>Cycle</value>
    </data>
    <data name="balanced_behavior_card">
        <value>Balanced Behavior Card</value>
    </data>
    <data name="measuring_department">
        <value>Measuring Department</value>
    </data>
    <data name="formula_description_a">
        <value>Formula Description A</value>
    </data>
    <data name="formula_description_b">
        <value>Formula Description B</value>
    </data>
    <data name="source">
        <value>Source</value>
    </data>
    <data name="direction">
        <value>Direction</value>
    </data>
    <data name="tags">
        <value>Tags</value>
    </data>
    <data name="type">
        <value>Type</value>
    </data>
    <data name="result">
        <value>Result</value>
    </data>
    <data name="target">
        <value>Target</value>
    </data>
    <data name="last_value">
        <value>Last value</value>
    </data>
    <data name="sum_values">
        <value>Sum</value>
    </data>
    <data name="item_removed_successfully">
        <value>Removed successfully</value>
    </data>
    <data name="employee">
        <value>Employee</value>
    </data>
    <data name="customer">
        <value>Customer</value>
    </data>
    <data name="department">
        <value>Department</value>
    </data>
    <data name="public">
        <value>Public</value>
    </data>
    <data name="private">
        <value>private</value>
    </data>
    <data name="contractor">
        <value>Contractor</value>
    </data>
    <data name="partner">
        <value>Partner</value>
    </data>
    <data name="electronic">
        <value>Electronic</value>
    </data>
    <data name="smart">
        <value>Smart</value>
    </data>
    <data name="manual">
        <value>Manual</value>
    </data>
    <data name="silver">
        <value>Silver</value>
    </data>
    <data name="bronze">
        <value>Bronze</value>
    </data>
    <data name="golden">
        <value>Golden</value>
    </data>
    <data name="0_is_already_exists_before">
        <value>{0} is already exists before, please make sure its not duplicate entry!</value>
    </data>
    <data name="full_access">
        <value>Full access</value>
    </data>
    <data name="auto">
        <value>Auto</value>
    </data>
    <data name="overall_target_cannot_be_less_than_0">
        <value>Overall target cannot be less than {0}</value>
    </data>
    <data name="overall_target_cannot_be_more_than_0">
        <value>Overall target cannot be more than {0}.</value>
    </data>
    <data name="sum">
        <value>Sum</value>
    </data>
    <data name="last">
        <value>Last</value>
    </data>
    <data name="you_must_select_the_strategic_goal">
        <value>You must select the strategic goal!</value>
    </data>
    <data name="you_must_select_the_sustainability_impacts">
        <value>You must select the sustainability impacts.</value>
    </data>
    <data name="program">
        <value>Program</value>
    </data>
    <data name="initiative">
        <value>Initiative</value>
    </data>
    <data name="operation">
        <value>Operation</value>
    </data>
    <data name="other">
        <value>Other</value>
    </data>
    <data name="system">
        <value>System</value>
    </data>
    <data name="project">
        <value>Project</value>
    </data>
    <data name="department_operational_plan">
        <value>An operational plan for a department</value>
    </data>
    <data name="economical">
        <value>Economical</value>
    </data>
    <data name="environmental">
        <value>Environmental</value>
    </data>
    <data name="social">
        <value>Social</value>
    </data>
    <data name="administrative_operation">
        <value>Administrative operation</value>
    </data>
    <data name="field_operation">
        <value>Field operation</value>
    </data>
    <data name="task_operation">
        <value>Task operation</value>
    </data>
    <data name="the_code_is_already_used_for_another_operation">
        <value>The code is already used for another operation.</value>
    </data>
    <!-- Permission Translations -->
    <data name="permission_name_full_access">
        <value>Full access</value>
    </data>
    <data name="permission_description_full_access">
        <value>The user would have the full authority to manage every aspect of the system and its resources.</value>
    </data>
    <data name="permission_name_kpi">
        <value>Manage KPIs</value>
    </data>
    <data name="permission_description_kpi">
        <value>The user would have the ability to view, create, update, and, delete KPIs (provided the user's department
            has access to the KPI).
        </value>
    </data>
    <data name="permission_name_kpi:read">
        <value>View KPIs</value>
    </data>
    <data name="permission_description_kpi:read">
        <value>The user would have the ability to view KPIs (provided the user's department has access to the KPI).
        </value>
    </data>
    <data name="permission_name_kpi:write">
        <value>Create/update KPIs</value>
    </data>
    <data name="permission_description_kpi:write">
        <value>The user would have the ability to view, create, and update KPIs (provided the user's department has
            access to the KPI).
        </value>
    </data>
    <data name="permission_name_kpi:delete">
        <value>Delete KPIs</value>
    </data>
    <data name="permission_description_kpi:delete">
        <value>The user would have the ability to delete KPIs (provided the user's department has access to the KPI).
        </value>
    </data>
    <data name="permission_name_kpi:evaluate">
        <value>Evaluate KPIs</value>
    </data>
    <data name="permission_description_kpi:evaluate">
        <value>The user would have the ability to evaluate KPIs.
        </value>
    </data>
    <data name="permission_name_kpi_result">
        <value>Manage KPI results</value>
    </data>
    <data name="permission_description_kpi_result">
        <value>The user would have the ability to view, create, update, approve, disapprove, and delete KPI results
            (provided the user's department has access to the result).
        </value>
    </data>
    <data name="permission_name_kpi_result:read">
        <value>View KPI results</value>
    </data>
    <data name="permission_description_kpi_result:read">
        <value>The user would have the ability to view KPI results (provided the user's department has access to the
            result).
        </value>
    </data>
    <data name="permission_name_kpi_result:entry">
        <value>Kpi results entry</value>
    </data>
    <data name="permission_description_kpi_result:entry">
        <value>
            The user would have the ability to modify the values of KPI results (provided the user's department has
            access to the result).
        </value>
    </data>
    <data name="permission_name_kpi_result:flow">
        <value>KPI result entry by flow</value>
    </data>
    <data name="permission_description_kpi_result:flow">
        <value>
            The user would have the ability to update the values of KPI results through KPI flows (provided the user's
            department has access to the result).
        </value>
    </data>
    <data name="permission_name_kpi_result:write">
        <value>Create/update KPI results</value>
    </data>
    <data name="permission_description_kpi_result:write">
        <value>The user would have the ability to view, create, and update KPI results (provided the user's department
            has access to the result).
        </value>
    </data>
    <data name="permission_name_kpi_result:period_evaluate">
        <value>Evaluate KPI result periods</value>
    </data>
    <data name="permission_description_kpi_result:period_evaluate">
        <value>The user would have the ability to evaluate periods of KPI results.</value>
    </data>
    <data name="permission_name_kpi_result:period_export_evaluation">
        <value>Export KPI result period evaluations</value>
    </data>
    <data name="permission_description_kpi_result:period_export_evaluation">
        <value>The user would have the ability to export evaluations of periods of KPI results.</value>
    </data>
    <data name="permission_name_kpi_result:delete">
        <value>Delete KPI results</value>
    </data>
    <data name="permission_description_kpi_result:delete">
        <value>The user would have the ability to delete KPI results (provided the user's department has access to the
            results).
        </value>
    </data>
    <data name="permission_name_kpi_result:approve">
        <value>Approve KPI results</value>
    </data>
    <data name="permission_description_kpi_result:approve">
        <value>The user would have the ability to approve KPI results (provided the user's department has access to the
            results).
        </value>
    </data>
    <data name="permission_name_kpi_result:dynamic">
        <value>Submit dynamic kpi result data entry requests</value>
    </data>
    <data name="permission_description_kpi_result:dynamic">
        <value>
            The user would have the ability to submit requests to update the `A` and `B` values of kpi results. The
            requests are going to go through an approval process before they are written to kpi results.
        </value>
    </data>

    <data name="permission_name_kpi_result:dynamic_approve">
        <value>
            Approve dynamic kpi result data entry requests.
        </value>
    </data>
    <data name="permission_description_kpi_result:dynamic_approve">
        The user would have the ability to approve pending dynamic data entry requests given that the user is part of
        the result's department and is authorized for signing.
    </data>
    <data name="permission_name_kpi_result:direct_approve">
        <value>Direct approval of KPI results</value>
    </data>
    <data name="permission_description_kpi_result:direct_approve">
        <value>
            The user will have the ability to approve the KPI result directly and transfer it to the final stage for
            review and strategic approval.
        </value>
    </data>
    <data name="permission_name_kpi_result:response_approval_transfer">
        <value>
            Transfer approval for updating KPI result requests (default path)
        </value>
    </data>
    <data name="permission_description_kpi_result:response_approval_transfer">
        <value>
            The user will have the ability to access all requests that are in the first or second approval stage and
            transfer the approval for updating KPI result requests to another user
        </value>
    </data>
    <data name="permission_name_operation">
        <value>Manage operations</value>
    </data>
    <data name="permission_description_operation">
        <value>The user would have the ability to view, create, update, and, delete operations.</value>
    </data>
    <data name="permission_name_operation:read">
        <value>View operations</value>
    </data>
    <data name="permission_description_operation:read">
        <value>The user would have the ability to view operations.</value>
    </data>
    <data name="permission_name_operation:write">
        <value>Create/update operations</value>
    </data>
    <data name="permission_description_operation:write">
        <value>The user would have the ability to view, create, and update operations.</value>
    </data>
    <data name="permission_name_operation:delete">
        <value>Delete operations</value>
    </data>
    <data name="permission_description_operation:delete">
        <value>The user would have the ability to delete operations.</value>
    </data>
    <data name="permission_name_operation:approve_update_request">
        <value>Approve update request</value>
    </data>
    <data name="permission_description_operation:approve_update_request">
        <value>The user would have the ability to approve update requests.</value>
    </data>
    <data name="permission_name_plan">
        <value>Manage plans</value>
    </data>
    <data name="permission_description_plan">
        <value>The user would have the ability to view, create, update, and, delete plans.</value>
    </data>
    <data name="permission_name_plan:read">
        <value>View plans</value>
    </data>
    <data name="permission_description_plan:read">
        <value>The user would have the ability to view plans.</value>
    </data>
    <data name="permission_name_plan:write">
        <value>Create/update plans</value>
    </data>
    <data name="permission_description_plan:write">
        <value>The user would have the ability to view, create, and update plans as well as updating and deleting tasks
            and subtasks.
        </value>
    </data>
    <data name="permission_name_plan:delete">
        <value>Delete plans</value>
    </data>
    <data name="permission_description_plan:delete">
        <value>The user would have the ability to delete plans.</value>
    </data>
    <data name="permission_name_plan:intermediate_initial_approval">
        <value>Intermediate initial approval</value>
    </data>
    <data name="permission_description_plan:intermediate_initial_approval">
        <value>The user would have the ability to approve plans in their departments given that they have a signatory
            authority in that department.
        </value>
    </data>
    <data name="permission_name_plan:immediate_intermediate_initial_approval">
        <value>Immediate intermediate initial approval</value>
    </data>
    <data name="permission_description_plan:immediate_intermediate_initial_approval">
        <value>The user would have the ability to approve plans in their departments given that they have a signatory
            authority in that department and send them immediately to the final approval stage.
        </value>
    </data>
    <data name="permission_name_plan:initial_approval">
        <value>Initial approval</value>
    </data>
    <data name="permission_description_plan:initial_approval">
        <value>The user would have the ability to initially approve operational plans.</value>
    </data>
    <data name="permission_name_plan:subsubtask_final_approval">
        <value>Subsubtask final approval</value>
    </data>
    <data name="permission_description_plan:subsubtask_final_approval">
        <value>The user would have the ability to finalize the approval of operational plan subsubtasks (given that the
            user is the leader of assigned).
        </value>
    </data>
    <data name="permission_name_benchmark">
        <value>Manage benchmarks</value>
    </data>
    <data name="permission_description_benchmark">
        <value>The user would have the ability to view, create, update, and, delete benchmarks.</value>
    </data>
    <data name="permission_name_benchmark:read">
        <value>View benchmarks</value>
    </data>
    <data name="permission_description_benchmark:read">
        <value>The user would have the ability to view benchmarks.</value>
    </data>
    <data name="permission_name_benchmark:write">
        <value>Create/update benchmarks</value>
    </data>
    <data name="permission_description_benchmark:write">
        <value>The user would have the ability to view, create, and update benchmarks.</value>
    </data>
    <data name="permission_name_benchmark:delete">
        <value>Delete benchmarks</value>
    </data>
    <data name="permission_description_benchmark:delete">
        <value>The user would have the ability to delete benchmarks.</value>
    </data>
    <data name="permission_name_user">
        <value>Manage users</value>
    </data>
    <data name="permission_description_user">
        <value>The user would have the ability to view, create, update, and, delete users.</value>
    </data>
    <data name="permission_name_user:read">
        <value>View users</value>
    </data>
    <data name="permission_description_user:read">
        <value>The user would have the ability to view operations.</value>
    </data>
    <data name="permission_name_user:write">
        <value>Create/update users</value>
    </data>
    <data name="permission_description_user:write">
        <value>The user would have the ability to view, create, and update users.</value>
    </data>
    <data name="permission_name_user:delete">
        <value>The user would have the ability to delete users.</value>
    </data>
    <data name="permission_description_user:delete">
        <value>Delete users</value>
    </data>
    <data name="permission_name_department">
        <value>Manage departments</value>
    </data>
    <data name="permission_description_department">
        <value>The user would have the ability to view, create, update, and, delete departments.</value>
    </data>
    <data name="permission_name_department:read">
        <value>View departments</value>
    </data>
    <data name="permission_description_department:read">
        <value>The user would have the ability to view departments.</value>
    </data>
    <data name="permission_name_department:write">
        <value>Create/update departments</value>
    </data>
    <data name="permission_description_department:write">
        <value>The user would have the ability to view, create, and update departments.</value>
    </data>
    <data name="permission_name_department:delete">
        <value>Delete departments</value>
    </data>
    <data name="permission_description_department:delete">
        <value>The user would have the ability to delete departments.</value>
    </data>
    <data name="permission_name_report:department">
        <value>Manage department reports</value>
    </data>
    <data name="permission_description_report:department">
        <value>The user would have the ability to create department reports.</value>
    </data>
    <data name="permission_name_report:kpi">
        <value>Manage KPI reports</value>
    </data>
    <data name="permission_description_report:kpi">
        <value>The user would have the ability to create KPI reports.</value>
    </data>
    <data name="permission_name_capability_type">
        <value>Manage capability types</value>
    </data>
    <data name="permission_description_capability_type">
        <value>The user would have the ability to view, create, update, and, delete capability types.</value>
    </data>
    <data name="permission_name_kpi_tag">
        <value>Manage KPI tags</value>
    </data>
    <data name="permission_name_kpi_balanced_behavior_card">
        <value>KPI balanced behavior card</value>
    </data>
    <data name="permission_description_kpi_balanced_behavior_card">
        <value>The user would have the ability to view, create, update, and, delete KPI balanced behavior card.</value>
    </data>
    <data name="permission_description_kpi_tag">
        <value>The user would have the ability to view, create, update, and, delete KPI tags.</value>
    </data>
    <data name="permission_name_library_tag">
        <value>Manage library tags</value>
    </data>
    <data name="permission_description_library_tag">
        <value>The user would have the ability to view, create, update, and, delete library tags.</value>
    </data>
    <data name="permission_name_policy">
        <value>Manage policies</value>
    </data>
    <data name="permission_description_policy">
        <value>The user would have the ability to view, create, update, and, delete policies.</value>
    </data>
    <data name="permission_name_operation_rule_and_regulation">
        <value>Manage operation rules and regulations</value>
    </data>
    <data name="permission_description_operation_rule_and_regulation">
        <value>The user would have the ability to view, create, update, and, delete operation rules and regulations.
        </value>
    </data>
    <data name="permission_name_operation_specification">
        <value>Manage operation specifications</value>
    </data>
    <data name="permission_description_operation_specification">
        <value>The user would have the ability to view, create, update, and, delete operation specifications.</value>
    </data>
    <data name="permission_description_innovation">
        <value>The user would have the ability to view, create, update, and, delete innovations and innovators.</value>
    </data>
    <data name="permission_name_innovation">
        <value>Manage innovations and innovators</value>
    </data>
    <data name="permission_description_innovator">
        <value>The user would have the ability to create it's own innovations.</value>
    </data>
    <data name="permission_name_innovator">
        <value>Innovators</value>
    </data>
    <data name="permission_name_risk">
        <value>Manage risks</value>
    </data>
    <data name="permission_description_risk">
        <value>The user would have the ability to view, create, update, and, delete RISKs (provided the user's
            department
            has access to the risk).
        </value>
    </data>
    <data name="permission_name_risk:read">
        <value>View risks</value>
    </data>
    <data name="permission_description_risk:read">
        <value>The user would have the ability to view RISKs (provided the user's department has access to the risk).
        </value>
    </data>
    <data name="permission_name_risk:write">
        <value>Create/update risks</value>
    </data>
    <data name="permission_description_risk:write">
        <value>The user would have the ability to view, create, and update risks (provided the user's department has
            access to the risk).
        </value>
    </data>
    <data name="permission_name_risk:delete">
        <value>Delete risks</value>
    </data>
    <data name="permission_description_risk:delete">
        <value>The user would have the ability to delete RISKs (provided the user's department has access to the risk).
        </value>
    </data>
    <data name="permission_name_risk:evaluate">
        <value>Evaluate risks</value>
    </data>
    <data name="permission_description_risk:evaluate">
        <value>The user would have the ability to evaluate risks.
        </value>
    </data>
    <data name="operational_main">
        <value>Operational main</value>
    </data>
    <data name="operational_supporting">
        <value>Operational supporting</value>
    </data>
    <data name="operational_statistical">
        <value>Statistical</value>
    </data>
    <data name="permission_name_partner">
        <value>Manage partners</value>
    </data>
    <data name="permission_description_partner">
        <value>The user would have the ability to view, create, update, and, delete partners.</value>
    </data>
    <data name="permission_name_partner:export_evaluation">
        <value>Export partner evaluation</value>
    </data>
    <data name="permission_description_partner:export_evaluation">
        <value>The user would have the ability to export partner evaluations.</value>
    </data>
    <data name="permission_name_partnership">
        <value>Manage partnerships</value>
    </data>
    <data name="permission_description_partnership">
        <value>The user would have the ability to view, create, update, and, delete partnerships.</value>
    </data>
    <data name="permission_name_partnership:read">
        <value>View partnerships</value>
    </data>
    <data name="permission_description_partnership:read">
        <value>The user would have the ability to view partnerships.</value>
    </data>
    <data name="permission_name_partnership:write">
        <value>Create/update partnerships</value>
    </data>
    <data name="permission_description_partnership:write">
        <value>The user would have the ability to view, create, and update partnerships.</value>
    </data>
    <data name="permission_name_partnership:delete">
        <value>Delete partnerships</value>
    </data>
    <data name="permission_description_partnership:delete">
        <value>The user would have the ability to delete partnerships.</value>
    </data>
    <data name="permission_name_success_factor">
        <value>Manage success factors</value>
    </data>
    <data name="permission_description_success_factor">
        <value>The user would have the ability to view, create, update, and, delete success factors.</value>
    </data>
    <data name="permission_name_kpi_result_capability_type">
        <value>Manage kpi result capability types</value>
    </data>
    <data name="permission_description_kpi_result_capability_type">
        <value>The user would have the ability to view, create, update, and, delete kpi result capability types.</value>
    </data>
    <data name="permission_name_kpi_result_target_setting_method">
        <value>Manage kpi result target setting methods</value>
    </data>
    <data name="permission_description_kpi_result_target_setting_method">
        <value>The user would have the ability to view, create, update, and, delete kpi result target setting methods.
        </value>
    </data>
    <data name="permission_name_capability">
        <value>Manage capabilities</value>
    </data>
    <data name="permission_description_capability">
        <value>The user would have the ability to view, create, update, and, delete capabilities.</value>
    </data>
    <data name="permission_name_tournament">
        <value>Manage tournaments</value>
    </data>
    <data name="permission_description_tournament">
        <value>The user would have the ability to view, create, update, and, delete tournaments.</value>
    </data>
    <data name="permission_name_tournament:read">
        <value>View tournaments</value>
    </data>
    <data name="permission_description_tournament:read">
        <value>The user would have the ability to view tournaments.</value>
    </data>
    <data name="permission_name_operation_enhancement_type">
        <value>Manage operation enhancement types</value>
    </data>
    <data name="permission_description_operation_enhancement_type">
        <value>The user would have the ability to view, create, update, and delete operation enhancement types.</value>
    </data>
    <data name="permission_name_team">
        <value>Manage teams</value>
    </data>
    <data name="permission_description_team">
        <value>The user would have the ability to view, create, update, and, delete teams.</value>
    </data>
    <data name="permission_name_plan_input">
        <value>Manage plans inputs</value>
    </data>
    <data name="permission_description_plan_input">
        <value>The user would have the ability to view, create, update, and, delete plans inputs.</value>
    </data>
    <data name="permission_name_service">
        <value>Manage services</value>
    </data>
    <data name="permission_description_service">
        <value>The user would have the ability to view, create, update, and, delete services.</value>
    </data>
    <data name="permission_name_improvement_opportunity">
        <value>Manage improvement opportunities</value>
    </data>
    <data name="permission_description_improvement_opportunity">
        <value>The user would have the ability to view, create, update, and, delete improvement opportunities.</value>
    </data>
    <data name="permission_name_improvement_opportunity_input_source">
        <value>Manage improvement opportunity input sources</value>
    </data>
    <data name="permission_description_improvement_opportunity_input_source">
        <value>The user would have the ability to view, create, update, and, delete improvement opportunity input
            sources.
        </value>
    </data>
    <data name="permission_name_improvement_opportunity_input_category">
        <value>Manage improvement opportunity input categories</value>
    </data>
    <data name="permission_description_improvement_opportunity_input_category">
        <value>The user would have the ability to view, create, update, and, delete improvement opportunity input
            categories.
        </value>
    </data>
    <data name="permission_name_notification">
        <value>Notification</value>
    </data>
    <data name="permission_description_notification">
        <value>The user would have the ability to send notifications.</value>
    </data>
    <data name="permission_name_strategic_goal">
        <value>Strategic goals</value>
    </data>
    <data name="permission_description_strategic_goal">
        <value>The user would have the ability to view, create, update, and, delete strategic goals.</value>
    </data>
    <data name="permission_name_government_strategic_goal">
        <value>Government Strategic goals</value>
    </data>
    <data name="permission_description_government_strategic_goal">
        <value>The user would have the ability to view, create, update, and, delete government strategic goals.</value>
    </data>
    <data name="permission_name_plan_category">
        <value>Plan categories</value>
    </data>
    <data name="permission_description_plan_category">
        <value>The user would have the ability to view, create, update, and, delete plan categories.</value>
    </data>
    <data name="permission_name_kpi_result_category">
        <value>Kpi result categories</value>
    </data>
    <data name="permission_description_kpi_result_category">
        <value>The user would have the ability to view, create, update, and, delete kpi result categories.</value>
    </data>
    <data name="system_event_resource_type_name_kpi">
        <value>Kpis</value>
    </data>
    <data name="system_event_resource_type_name_result">
        <value>Results</value>
    </data>
    <data name="system_event_resource_type_name_operation">
        <value>Operations</value>
    </data>
    <data name="system_event_resource_type_name_file">
        <value>Library files</value>
    </data>
    <data name="system_event_resource_type_name_plan">
        <value>Plans</value>
    </data>
    <data name="system_event_resource_type_name_partner">
        <value>Partners</value>
    </data>
    <data name="system_event_resource_type_name_improvement_opportunity">
        <value>Improvement opportunities</value>
    </data>
    <data name="system_event_resource_type_name_service">
        <value>Services</value>
    </data>
    <data name="system_event_resource_type_name_benchmark">
        <value>Benchmarks</value>
    </data>
    <data name="system_event_resource_type_name_tournament">
        <value>Tournaments</value>
    </data>
    <data name="system_event_resource_type_name_capability">
        <value>Capabilities</value>
    </data>
    <data name="system_event_resource_type_name_plan_task">
        <value>Plan tasks</value>
    </data>
    <data name="system_event_resource_type_name_plan_subtask">
        <value>Plan subtasks</value>
    </data>
    <data name="system_event_resource_type_name_operation_procedure">
        <value>Operation procedures</value>
    </data>
    <data name="system_event_resource_type_name_pillar">
        <value>Pillars</value>
    </data>
    <data name="system_event_resource_type_name_standard">
        <value>Standards</value>
    </data>
    <data name="system_event_type_name_add">
        <value>Addition</value>
    </data>
    <data name="system_event_type_name_mod">
        <value>Modification</value>
    </data>
    <data name="system_event_type_name_rmv">
        <value>Removal</value>
    </data>
    <data name="kpi_result_request_type_edit">
        <value>Edit request</value>
    </data>
    <data name="kpi_result_request_type_delete">
        <value>Delete request</value>
    </data>
    <data name="national">
        <value>National</value>
    </data>
    <data name="segmental">
        <value>Segmental</value>
    </data>
    <data name="kpi_result_request_type_cancel">
        <value>Cancel Request</value>
    </data>
    <data name="request_opened">
        <value>Request is activated</value>
    </data>
    <data name="request_closed">
        <value>Request is closed</value>
    </data>
    <data name="you_cant_edit_item">
        <value>You cannot edit item.</value>
    </data>
    <data name="kpi_initial_result_source_baseline">
        <value>Baseline</value>
    </data>
    <data name="kpi_initial_result_source_historical_data">
        <value>Historical data</value>
    </data>
    <data name="kpi_initial_result_source_read">
        <value>Read</value>
    </data>
    <data name="you_need_to_add_initial_result">
        <value>You need to add initial result</value>
    </data>
    <data name="kpi_result_data_entry_request_path_default">
        <value>Default</value>
    </data>
    <data name="kpi_result_data_entry_request_path_immediate">
        <value>Immediate</value>
    </data>
    <data name="user_request_type_file">
        <value>File</value>
    </data>
    <data name="user_request_type_kpi">
        <value>Kpi</value>
    </data>
    <data name="user_request_type_operation">
        <value>operations</value>
    </data>
    <data name="you_are_not_authorized_to_add_request">
        <value>You can't add request</value>
    </data>
    <data name="you_are_not_authorized_to_delete_request">
        <value>You can't remove this request</value>
    </data>
    <data name="you_cant_edit_request">
        <value>You can't edit this request</value>
    </data>
    <data name="you_are_not_authorized_to_add_comment">
        <value>You can't add comment</value>
    </data>
    <data name="you_are_not_authorized_to_edit_comment">
        <value>You can't edit comment</value>
    </data>
    <data name="you_are_not_authorized_to_delete_comment">
        <value>You can't delete comment</value>
    </data>
    <data name="plan_assigned_department">
        <value>Department</value>
    </data>
    <data name="plan_assigned_team">
        <value>Team</value>
    </data>
    <data name="plan_assigned_user">
        <value>User</value>
    </data>
    <data name="plan_assigned_other">
        <value>Other</value>
    </data>
    <data name="plan_subsubtask_approval_type_pending">
        <value>Pending</value>
    </data>
    <data name="plan_subsubtask_approval_type_submitted">
        <value>Submitted</value>
    </data>
    <data name="plan_subsubtask_approval_type_rejected">
        <value>Rejected</value>
    </data>
    <data name="plan_subsubtask_approval_type_approved">
        <value>Approved</value>
    </data>
    <data name="plan_subsubtask_approval_type_final">
        <value>Final</value>
    </data>
    <data name="strategic_goal_for_ministry_of_interior">
        <value>Strategic goal for ministry of interior</value>
    </data>
    <data name="main_operation">
        <value>Main operation</value>
    </data>
    <data name="plan_inputs">
        <value>Plan inputs</value>
    </data>
    <data name="task">
        <value>Task</value>
    </data>
    <data name="task_weight">
        <value>Task weight</value>
    </data>
    <data name="procedures">
        <value>Procedures</value>
    </data>
    <data name="procedure_weight">
        <value>Procedure weight</value>
    </data>
    <data name="partners">
        <value>Partners</value>
    </data>
    <data name="risks_record">
        <value>Risks record</value>
    </data>
    <data name="plan_kpis">
        <value>Plan KPIs</value>
    </data>
    <data name="required_resources">
        <value>Required resources</value>
    </data>
    <data name="other_links_if_exists">
        <value>Other links if exists</value>
    </data>
    <data name="assigned">
        <value>Assigned</value>
    </data>
    <data name="time_frame">
        <value>Time frame</value>
    </data>
    <data name="performance_follow_up">
        <value>Performance follow up</value>
    </data>
    <data name="set_time">
        <value>Set time</value>
    </data>
    <data name="done">
        <value>Done</value>
    </data>
    <data name="not_done">
        <value>Not done</value>
    </data>
    <data name="reason">
        <value>Reason</value>
    </data>
    <data name="final_output">
        <value>Final output</value>
    </data>
    <data name="service_ownership_federal">
        <value>Federal</value>
    </data>
    <data name="service_ownership_local">
        <value>Local</value>
    </data>
    <data name="service_category_control">
        <value>Control</value>
    </data>
    <data name="service_category_informative">
        <value>Informative</value>
    </data>
    <data name="service_category_procedural">
        <value>Procedural</value>
    </data>
    <data name="service_category_social">
        <value>Social</value>
    </data>
    <data name="service_client_female">
        <value>Female</value>
    </data>
    <data name="service_client_male">
        <value>Male</value>
    </data>
    <data name="service_client_government">
        <value>Government</value>
    </data>
    <data name="service_client_people_of_determination">
        <value>People of determination</value>
    </data>
    <data name="service_client_private">
        <value>Private</value>
    </data>
    <data name="service_client_resident">
        <value>Resident</value>
    </data>
    <data name="service_client_visitor">
        <value>Visitor</value>
    </data>
    <data name="service_client_senior_citizen">
        <value>Senior citizens</value>
    </data>
    <data name="service_provider_channel_call_center">
        <value>Call center</value>
    </data>
    <data name="service_provider_channel_contractors_centers">
        <value>Contractors Centers</value>
    </data>
    <data name="service_provider_channel_self_service_machine">
        <value>Self service machine</value>
    </data>
    <data name="service_provider_channel_smart_apps">
        <value>Smart apps</value>
    </data>
    <data name="service_provider_channel_website">
        <value>Websites</value>
    </data>
    <data name="service_delivery_channel_contractor">
        <value>Contractor</value>
    </data>
    <data name="service_delivery_channel_electronic">
        <value>Electronic</value>
    </data>
    <data name="service_delivery_channel_email">
        <value>Email</value>
    </data>
    <data name="service_delivery_channel_message">
        <value>Message</value>
    </data>
    <data name="service_delivery_channel_prompt">
        <value>Prompt</value>
    </data>
    <data name="service_payment_cash">
        <value>Cash</value>
    </data>
    <data name="service_payment_electronic">
        <value>Electronic</value>
    </data>
    <data name="service_transformation_complete">
        <value>Complete transformation</value>
    </data>
    <data name="service_transformation_partial">
        <value>Partial transformation</value>
    </data>
    <data name="seconds">
        <value>Seconds</value>
    </data>
    <data name="minutes">
        <value>Minutes</value>
    </data>
    <data name="hours">
        <value>Hours</value>
    </data>
    <data name="days">
        <value>Days</value>
    </data>
    <data name="months">
        <value>Months</value>
    </data>
    <data name="years">
        <value>Years</value>
    </data>
    <data name="service_entrance_complaint">
        <value>Complaint</value>
    </data>
    <data name="service_entrance_governmental_orientation">
        <value>Governmental orientation</value>
    </data>
    <data name="service_entrance_other">
        <value>other</value>
    </data>
    <data name="service_entrance_process_development">
        <value>process development and service</value>
    </data>
    <data name="service_entrance_reconnaissance">
        <value>Reconnaissance</value>
    </data>
    <data name="service_entrance_secret_shopper">
        <value>Secret shopper</value>
    </data>
    <data name="service_entrance_suggestion">
        <value>Suggestion</value>
    </data>
    <data name="benchmark_language_arabic">
        <value>Arabic</value>
    </data>
    <data name="benchmark_language_english">
        <value>English</value>
    </data>
    <data name="benchmark_type_operation">
        <value>Operation</value>
    </data>
    <data name="benchmark_type_service">
        <value>Service</value>
    </data>
    <data name="benchmark_type_best_practice">
        <value>Best practice</value>
    </data>
    <data name="benchmark_type_kpi">
        <value>Kpi</value>
    </data>
    <data name="benchmark_method_visit">
        <value>Visit</value>
    </data>
    <data name="benchmark_method_remote">
        <value>Remote</value>
    </data>
    <data name="benchmark_method_research">
        <value>Research</value>
    </data>
    <data name="benchmark_management_type_operation">
        <value>Operation</value>
    </data>
    <data name="benchmark_management_type_other">
        <value>Other</value>
    </data>
    <data name="benchmark_entity_type_country">
        <value>Country</value>
    </data>
    <data name="benchmark_entity_type_partner">
        <value>Partner</value>
    </data>
    <data name="benchmark_entity_type_other">
        <value>Other</value>
    </data>
    <data name="benchmark_other_management_type_initiative">
        <value>Initiative</value>
    </data>
    <data name="benchmark_other_management_type_project">
        <value>Project</value>
    </data>
    <data name="benchmark_other_management_type_system">
        <value>System</value>
    </data>
    <data name="benchmark_report_type_internal">
        <value>Internal benchmark (report for internal visit)</value>
    </data>
    <data name="benchmark_report_type_external">
        <value>External benchmark (report for standard benchmark)</value>
    </data>
    <data name="provider_site_back">
        <value>Back site</value>
    </data>
    <data name="provider_site_front">
        <value>Front Site</value>
    </data>
    <data name="you_should_either_pass_an_existing_partner_or_other_partner">
        <value>You should either pass an existing partner or other partner</value>
    </data>
    <data name="service_fee_value_is_required">
        <value>Service fee value is required</value>
    </data>
    <data name="commentary_reports_for_the_award_of_the_owner_of_the_minister_of_interior">
        <value>Commentary Reports for the Leading Entity Award - Minister of Interior Award</value>
    </data>
    <data name="evaluation_report_ajman_award">
        <value>Evaluation Report Ajman Award</value>
    </data>
    <data name="self_assessment_feedback_reports">
        <value>Self-assessment feedback reports</value>
    </data>
    <data name="commentary_reports_for_the_international_prize">
        <value>Feedback Reports for the International Awards</value>
    </data>
    <data name="global_star_system_reports_7_stars">
        <value>Global Star System Reports (7 stars)</value>
    </data>
    <data name="internal_audit_reports_on_quality_systems">
        <value>Internal audit reports on quality systems</value>
    </data>
    <data name="benchmarking_reports">
        <value>Benchmarking Reports</value>
    </data>
    <data name="secret_shopper_reports">
        <value>Secret Shopper Reports</value>
    </data>
    <data name="customer_happiness_reports">
        <value>Customer happiness reports</value>
    </data>
    <data name="employee_happiness_reports">
        <value>Employee happiness reports</value>
    </data>
    <data name="partner_happiness_reports">
        <value>Partner happiness reports</value>
    </data>
    <data name="community_happiness_reports">
        <value>Community Happiness Reports</value>
    </data>
    <data name="supplier_happiness_reports">
        <value>Supplier Happiness Reports</value>
    </data>
    <data name="internal_audit_reports_on_performance">
        <value>Internal audit reports on performance indicators</value>
    </data>
    <data name="external_audit_reports_on_performance">
        <value>External audit reports on performance indicators</value>
    </data>
    <data name="internal_Office_reports">
        <value>Internal Audit Office Reports</value>
    </data>
    <data name="risk_records">
        <value>risk records</value>
    </data>
    <data name="customer_complaints">
        <value>Customer complaints</value>
    </data>
    <data name="customer_suggestions">
        <value>Customer Suggestions</value>
    </data>
    <data name="staff_suggestions">
        <value>Staff suggestions/innovations</value>
    </data>
    <data name="audit_bureau_reports">
        <value>Audit Bureau reports</value>
    </data>
    <data name="senior_leadership_directives">
        <value>Senior leadership directives</value>
    </data>
    <data name="happiness_meter_results">
        <value>Happiness meter results</value>
    </data>
    <data name="performance_results">
        <value>Performance Indicator Results</value>
    </data>
    <data name="practices">
        <value>practices</value>
    </data>
    <data name="periodic_and_sudden_inspection_reports">
        <value>Periodic and sudden inspection reports</value>
    </data>
    <data name="need_quick_action">
        <value>Need quick action</value>
    </data>
    <data name="need_to_launch_an_initiative_or_project">
        <value>Need to launch an initiative/project</value>
    </data>
    <data name="evaluation_note_that_is_not_a_function_of_the_leadership">
        <value>Evaluation note that is not a function of the leadership</value>
    </data>
    <data name="high">
        <value>High</value>
    </data>
    <data name="medium">
        <value>Medium</value>
    </data>
    <data name="low">
        <value>Low</value>
    </data>
    <data name="very_low">
        <value>Very low</value>
    </data>
    <data name="orientation_focus_and_alignment_with_quality_of_life">
        <value>Orientation, focus and alignment with quality of life</value>
    </data>
    <data name="quality_of_life_apps">
        <value>quality of life apps</value>
    </data>
    <data name="interdependence_integration_and_partnership_to_achieve_quality_of_life">
        <value>Interdependence, integration and partnership to achieve quality of life</value>
    </data>
    <data name="foreseeing_the_future">
        <value>Foreseeing the future</value>
    </data>
    <data name="risk_assessment_and_adaptation">
        <value>Risk assessment and adaptation</value>
    </data>
    <data name="forecasting_and_analysis">
        <value>Forecasting and analysis</value>
    </data>
    <data name="scenarios">
        <value>Scenarios</value>
    </data>
    <data name="national_agenda">
        <value>National Agenda (centennial)</value>
    </data>
    <data name="leading_position_and_competitiveness">
        <value>Leading position and competitiveness</value>
    </data>
    <data name="strategies">
        <value>Strategies</value>
    </data>
    <data name="managing_initiatives_and_plans">
        <value>Managing initiatives and plans</value>
    </data>
    <data name="policies_laws_and_regulations">
        <value>Policies, laws and regulations</value>
    </data>
    <data name="organizational_structures_and_responsibilities">
        <value>organizational structures and responsibilities</value>
    </data>
    <data name="governance">
        <value>Governance</value>
    </data>
    <data name="business_model">
        <value>Business model</value>
    </data>
    <data name="accelerate_achievement">
        <value>Accelerate achievement</value>
    </data>
    <data name="the_customer_first">
        <value>The customer first</value>
    </data>
    <data name="involve_customers_in_the_design_and_development_of_services">
        <value>Involve the stakeholders involved in the design and development of services</value>
    </data>
    <data name="aerodynamics_in_services">
        <value>Aerodynamics in services</value>
    </data>
    <data name="a_seamless_and_interconnected_experience_for_customers">
        <value>A seamless and interconnected experience for customers</value>
    </data>
    <data name="digital_services_as_a_foundation">
        <value>Digital services as a foundation</value>
    </data>
    <data name="employment_of_advanced_technology">
        <value>Employment of advanced technology (artificial intelligence, digital transactions ....)</value>
    </data>
    <data name="digital_infrastructure">
        <value>Digital infrastructure</value>
    </data>
    <data name="human_capital">
        <value>Human capital</value>
    </data>
    <data name="future_jobs_and_skills">
        <value>future jobs and skills</value>
    </data>
    <data name="quality_of_life_in_the_work_environment">
        <value>Quality of life in the work environment</value>
    </data>
    <data name="lifelong_learning">
        <value>lifelong learning</value>
    </data>
    <data name="empowering_talent">
        <value>Empowering Talent</value>
    </data>
    <data name="human_resource_management">
        <value>Human Resource Management</value>
    </data>
    <data name="optimum_utilization_of_resources">
        <value>Optimum utilization of resources (financial and non-financial)</value>
    </data>
    <data name="property_preservation">
        <value>property preservation</value>
    </data>
    <data name="entity_data">
        <value>Entity data</value>
    </data>
    <data name="big_data">
        <value>Big data</value>
    </data>
    <data name="open_data">
        <value>Open data</value>
    </data>
    <data name="optimization_of_data">
        <value>Optimization of data</value>
    </data>
    <data name="knowledge_management">
        <value>knowledge management</value>
    </data>
    <data name="sharing_experiences_and_practices">
        <value>Sharing experiences and practices</value>
    </data>
    <data name="continuity_of_relationship">
        <value>Continuity of relationship</value>
    </data>
    <data name="building_confidence">
        <value>Building confidence</value>
    </data>
    <data name="mutual_benefit">
        <value>Mutual benefit</value>
    </data>
    <data name="government_promotion">
        <value>Government promotion</value>
    </data>
    <data name="social_media">
        <value>Social Media</value>
    </data>
    <data name="media_messages">
        <value>Media messages</value>
    </data>
    <data name="media_crisis_management">
        <value>Media crisis management</value>
    </data>
    <data name="logo_has_been_added">
        <value>Innovation logo has been added to innovator</value>
    </data>
    <data name="logo_has_been_removed">
        <value>Innovation logo has been removed from innovator</value>
    </data>
    <data name="there_is_already_an_innovator_with_that_employee_number">
        <value>There is already an innovator with that employee number</value>
    </data>
    <data name="cannot_access_this_resource">
        <value>cannot access this resource</value>
    </data>
    <data name="plan_subtask_or_library_file_does_not_exist">
        <value>Procedure or library file does not exist</value>
    </data>
    <data name="plan_subtask_is_already_using_the_file">
        <value>Procedure is already using the file</value>
    </data>
    <data name="innovation_or_activity_does_not_exist">
        <value>Innovation or activity does not exist</value>
    </data>
    <data name="innovation_is_already_using_the_activity">
        <value>Innovation is already using the activity</value>
    </data>
    <data name="proactive_standard_time">
        <value>Time standard</value>
    </data>
    <data name="proactive_standard_status_change">
        <value>Status change standard</value>
    </data>
    <data name="proactive_standard_interdependence">
        <value>Interdependence and dependency standards</value>
    </data>
    <data name="proactive_standard_regulations">
        <value>Regulations and decisions</value>
    </data>
    <data name="proactive_standard_changes_in_laws">
        <value>Changes in laws standard</value>
    </data>
    <data name="proactive_standard_spatial">
        <value>Spatial standard</value>
    </data>
    <data name="proactive_amenable">
        <value>Able to be proactive</value>
    </data>
    <data name="proactive_not_amenable">
        <value>Unable to be proactive</value>
    </data>
    <data name="proactive_approved_package">
        <value>Within an approved package</value>
    </data>
    <data name="periods">
        <value>Periods</value>
    </data>
    <data name="period_0">
        <value>Period {0}</value>
    </data>
    <data name="overall">
        <value>Overall</value>
    </data>
    <data name="owning_department">
        <value>Owning department</value>
    </data>
    <data name="operation_level_parentheses">
        <value>Operation (level)</value>
    </data>
    <data name="month_0">
        <value>Month {0}</value>
    </data>
    <data name="quarter_0">
        <value>Quarter {0}</value>
    </data>
    <data name="year_0">
        <value>Year half {0}</value>
    </data>
    <data name="regional">
        <value>Regional</value>
    </data>
    <data name="agreement">
        <value>Agreement</value>
    </data>
    <data name="continuous">
        <value>Continuous</value>
    </data>
    <data name="federal">
        <value>Federal</value>
    </data>
    <data name="international">
        <value>International</value>
    </data>
    <data name="limited">
        <value>Limited</value>
    </data>
    <data name="local">
        <value>Local</value>
    </data>
    <data name="memorandum_of_understanding">
        <value>Memorandum of understanding</value>
    </data>
    <data name="participating_teams">
        <value>Participating teams</value>
    </data>
    <data name="strategic_goal_category_main">
        <value>Main</value>
    </data>
    <data name="strategic_goal_category_support">
        <value>Support</value>
    </data>
    <data name="file_is_being_used_by_other_elements">
        <value>This file is mapped with another records!</value>
    </data>
    <data name="linked_operations">
        <value>Linked operations</value>
    </data>
    <data name="service_duration_type_is_required">
        <value>Service duration type is required</value>
    </data>
    <data name="duration_type_is_required">
        <value>Duration type is required</value>
    </data>
    <data name="opportunity_or_kpi_does_not_exist">
        <value>Error: either the kpi does not exist, or the improvement opportunity does not exist</value>
    </data>
    <data name="opportunity_is_already_linked_with_kpi">
        <value>Error: improvement opportunity is already linked with kpi</value>
    </data>
    <data name="statistical_report_cycle_annual">
        <value>Annual</value>
    </data>
    <data name="statistical_report_cycle_semi_annual">
        <value>Semi annual</value>
    </data>
    <data name="statistical_report_cycle_quarter">
        <value>Quarter</value>
    </data>
    <data name="statistical_report_cycle_month">
        <value>Month</value>
    </data>
    <data name="kpi_result_data_entry_response_transfer_data_entry">
        <value>Data entry</value>
    </data>
    <data name="kpi_result_data_entry_response_transfer_level_1">
        <value>First approval</value>
    </data>
    <data name="kpi_result_data_entry_response_transfer_level_2">
        <value>Second approval</value>
    </data>
    <data name="kpi_result_data_entry_response_transfer_kpi_manager">
        <value>ِAudit and review</value>
    </data>
    <data name="kpi_result_data_entry_response_transfer_done">
        <value>Done</value>
    </data>
    <data name="user_status_active">
        <value>Active</value>
    </data>
    <data name="user_status_suspended">
        <value>Suspended</value>
    </data>
    <data name="user_status_deleted">
        <value>Deleted</value>
    </data>
    <data name="standard_subtask_approval_draft">
        <value>Draft</value>
    </data>
    <data name="standard_subtask_approval_initially_submitted">
        <value>Initially submitted</value>
    </data>
    <data name="standard_subtask_approval_initially_approved">
        <value>Initially approved</value>
    </data>
    <data name="standard_subtask_approval_initially_rejected">
        <value>Initially rejected</value>
    </data>
    <data name="standard_subtask_approval_submitted">
        <value>Submitted</value>
    </data>
    <data name="standard_subtask_approval_approved">
        <value>Approved</value>
    </data>
    <data name="standard_subtask_approval_rejected">
        <value>Rejected</value>
    </data>
    <data name="standard_subtask_approval_final">
        <value>Final</value>
    </data>
    <data name="resource_type_kpi_result_capability">
        <value>Kpi result capability</value>
    </data>
    <data name="resource_type_capability">
        <value>Capability</value>
    </data>
    <data name="resource_type_kpi_benchmark">
        <value>Kpi benchmark</value>
    </data>
    <data name="resource_type_operation">
        <value>Operation</value>
    </data>
    <data name="resource_type_operation_enhancement">
        <value>Operation enhancement</value>
    </data>
    <data name="resource_type_plan_subsubtask">
        <value>Plan subsubtask</value>
    </data>
    <data name="resource_type_benchmark">
        <value>Benchmark</value>
    </data>
    <data name="resource_type_tournament">
        <value>Tournament</value>
    </data>
    <data name="resource_type_standard">
        <value>Standard</value>
    </data>
    <data name="resource_type_operation_procedure">
        <value>Operation procedure</value>
    </data>
    <data name="resource_type_improvement_opportunity">
        <value>Improvement opportunity</value>
    </data>
    <data name="resource_type_standard_subtask">
        <value>Standard subtask</value>
    </data>
    <data name="resource_type_operation_procedure_steps">
        <value>Operation procedure steps</value>
    </data>
    <data name="kpi_result_category_type_center">
        <value>Center</value>
    </data>
    <data name="kpi_result_category_type_categorization">
        <value>Categorization</value>
    </data>
    <data name="department_hierarchy_users_excel_user">
        <value>User</value>
    </data>
    <data name="department_hierarchy_users_excel_department">
        <value>Department</value>
    </data>
    <data name="department_hierarchy_users_excel_yes">
        <value>Yes</value>
    </data>
    <data name="department_hierarchy_users_have_the_authority_to_approve">
        <value>have the authority to approve</value>
    </data>
    <data name="department_hierarchy_users_excel_no">
        <value>No</value>
    </data>
    <data name="department_hierarchy_users_excel_is_authorized_for_sign_in">
        <value>Is authorized for sign in?</value>
    </data>
    <data name="partner_standard_type_government">
        <value>Government</value>
    </data>
    <data name="partner_standard_type_private">
        <value>Private</value>
    </data>
    <data name="is_trend">
        <value>Is trend</value>
    </data>
    <data name="is_special">
        <value>Is special</value>
    </data>
    <data name="yes">
        <value>Yes</value>
    </data>
    <data name="no">
        <value>No</value>
    </data>
    <data name="partnership_contract">
        <value>Partnership contract</value>
    </data>
    <data name="title">
        <value>Title</value>
    </data>
    <data name="partner_standards">
        <value>Partner standards</value>
    </data>
    <data name="the_purpose_of_the_partnership_and_desired_results">
        <value>The purpose of the partnership and desired results</value>
    </data>
    <data name="partner_capabilities">
        <value>Partner capabilities</value>
    </data>
    <data name="partnership_scopes">
        <value>Partnership scopes</value>
    </data>
    <data name="partnership_frameworks">
        <value>Partnership frameworks</value>
    </data>
    <data name="roles_and_responsibilities">
        <value>Roles and responsibilities</value>
    </data>
    <data name="resources">
        <value>Resources</value>
    </data>
    <data name="field">
        <value>Field</value>
    </data>
    <data name="membership">
        <value>Membership</value>
    </data>
    <data name="current_situation">
        <value>Current situation</value>
    </data>
    <data name="arrangements">
        <value>Arrangements</value>
    </data>
    <data name="dealing_with_conflicts">
        <value>Dealing with conflicts</value>
    </data>
    <data name="partnership_plan">
        <value>Partnership plan</value>
    </data>
    <data name="goal">
        <value>Goal</value>
    </data>
    <data name="operations">
        <value>Operations</value>
    </data>
    <data name="services">
        <value>Services</value>
    </data>
    <data name="activities">
        <value>Activities</value>
    </data>
    <data name="benefits">
        <value>Benefits</value>
    </data>
    <data name="responsibilities">
        <value>Responsibilities</value>
    </data>
    <data name="end_date">
        <value>End date</value>
    </data>
    <data name="kpis">
        <value>KPIs</value>
    </data>
    <data name="targets">
        <value>Targets</value>
    </data>
    <data name="risks">
        <value>Risks</value>
    </data>
    <data name="partner_communication">
        <value>Partner communication</value>
    </data>
    <data name="communication_plan">
        <value>Communication plan</value>
    </data>
    <data name="purpose_of_the_activity">
        <value>Purpose of the activity</value>
    </data>
    <data name="communication_tool">
        <value>Communication tool</value>
    </data>
    <data name="first_quarter">
        <value>First quarter</value>
    </data>
    <data name="second_quarter">
        <value>Second quarter</value>
    </data>
    <data name="third_quarter">
        <value>Third quarter</value>
    </data>
    <data name="fourth_quarter">
        <value>Fourth quarter</value>
    </data>
    <data name="period_target">
        <value>Target</value>
    </data>
    <data name="period_achieved">
        <value>Achieved</value>
    </data>
    <data name="partner_evaluation">
        <value>Partner evaluation</value>
    </data>
    <data name="partnership_contract_evaluation">
        <value>Partnership contract evaluation</value>
    </data>
    <data name="standards">
        <value>Standards</value>
    </data>
    <data name="total">
        <value>Total</value>
    </data>
    <data name="partner_name">
        <value>Partner name</value>
    </data>
    <data name="value_type">
        <value>Value type</value>
    </data>
    <data name="breakdown_category">
        <value>Breakdown category</value>
    </data>
    <data name="breakdown_subcategory">
        <value>Breakdown subcategory</value>
    </data>
    <data name="operation_procedure_type_manual">
        <value>Manual</value>
    </data>
    <data name="operation_procedure_type_electronic">
        <value>Electronic</value>
    </data>
    <data name="position_team_president">
        <value>Team president</value>
    </data>
    <data name="position_team_vice_president">
        <value>Team vice president</value>
    </data>
    <data name="position_reporter">
        <value>Reporter</value>
    </data>
    <data name="occurring_probability_high">
        <value>High</value>
    </data>
    <data name="occurring_probability_medium">
        <value>Medium</value>
    </data>
    <data name="occurring_probability_low">
        <value>Low</value>
    </data>
    <data name="impact_high">
        <value>High</value>
    </data>
    <data name="impact_medium">
        <value>Medium</value>
    </data>
    <data name="impact_low">
        <value>Low</value>
    </data>
    <data name="after">
        <value>After</value>
    </data>
    <data name="before">
        <value>Before</value>
    </data>
    <data name="evaluation_type_risk">
        <value>Risk</value>
    </data>
    <data name="evaluation_type_kpi">
        <value>Kpi</value>
    </data>
    <data name="evaluation_type_kpi_result_period">
        <value>Kpi Result Period</value>
    </data>
    <data name="unable_to_send_password_reset_link_message">
        <value>Unable to send password reset link. Please contact an administrator to configure email settings or the
            application URL.
        </value>
    </data>
    <data name="this_user_is_not_eligible_for_password_reset_message">
        <value>This user is not eligible for password reset. Please contact an administrator.</value>
    </data>
    <data name="this_user_is_not_found">
        <value>This user is not found</value>
    </data>
    <data name="password_reset_link_sent_please_check_your_email">
        <value>Password reset link sent. Please check your email.</value>
    </data>
    <data name="invalid_or_expired_password_reset_link_please_request_a_new_one">
        <value>Invalid or expired password reset link. Please request a new one.</value>
    </data>
    <data name="passwords_do_not_match_please_try_again">
        <value>Passwords do not match. Please try again.</value>
    </data>
    <data name="password_reset_successful">
        <value>Password reset successful.</value>
    </data>
    <data name="partnership_time_frame_annual">
        <value>Annual</value>
    </data>
    <data name="partnership_time_frame_semi_annual">
        <value>Semi-annual</value>
    </data>
    <data name="partnership_time_frame_quarter">
        <value>Quarter</value>
    </data>
    <data name="partnership_time_frame_month">
        <value>Month</value>
    </data>
    <data name="please_provide_a_reason_for_rejection">
        <value>Please provide a reason for rejection</value>
    </data>
    <data name="permission_name_partnership:reviewer">
        <value>Legal affairs review</value>
    </data>
    <data name="permission_description_partnership:reviewer">
        <value>The user will be able to review partnerships, comment on them, and approve or reject them.</value>
    </data>
    <data name="supplier_name_is_required">
        <value>Supplier name is required.</value>
    </data>
    <data name="local_code_is_required">
        <value>Local code is required.</value>
    </data>
    <data name="ministerial_code_is_required">
        <value>Ministerial code is required.</value>
    </data>
    <data name="supplier_category_is_required">
        <value>Supplier category is required.</value>
    </data>
    <data name="main_operation_owner_is_required">
        <value>Main operation owner is required.</value>
    </data>
    <data name="sustainability_impact_is_required">
        <value>Sustainability impact is required.</value>
    </data>
    <data name="operation_purpose_is_required">
        <value>Operation purpose is required.</value>
    </data>
    <data name="operation_duration_is_required">
        <value>Operation duration is required.</value>
    </data>
    <data name="first_month">
        <value>First month</value>
    </data>
    <data name="second_month">
        <value>Second month</value>
    </data>
    <data name="third_month">
        <value>Third month</value>
    </data>
    <data name="fourth_month">
        <value>Fourth month</value>
    </data>
    <data name="fifth_month">
        <value>Fifth month</value>
    </data>
    <data name="sixth_month">
        <value>Sixth month</value>
    </data>
    <data name="seventh_month">
        <value>Seventh month</value>
    </data>
    <data name="eighth_month">
        <value>Eighth month</value>
    </data>
    <data name="ninth_month">
        <value>Ninth month</value>
    </data>
    <data name="tenth_month">
        <value>Tenth month</value>
    </data>
    <data name="eleventh_month">
        <value>Eleventh month</value>
    </data>
    <data name="twelfth_month">
        <value>Twelfth month</value>
    </data>
    <data name="first_half">
        <value>First half</value>
    </data>
    <data name="second_half">
        <value>Second half</value>
    </data>
    <data name="there_should_be_at_least_one_future_plan">
        <value>There should be at least one future plan</value>
    </data>
    <data name="description_is_required">
        <value>Description is required</value>
    </data>
    <data name="work_scope_is_required">
        <value>Work scope is required</value>
    </data>
    <data name="implementation_requirement_is_required">
        <value>Implementation requirement is required</value>
    </data>
    <data name="other_partners_is_required">
        <value>Other partners field is required</value>
    </data>
    <data name="firstly_approved_benchmarks">
        <value>Firstly approved benchmarks</value>
    </data>
    <data name="secondly_approved_benchmarks">
        <value>Secondly approved benchmarks</value>
    </data>
    <data name="the_total">
        <value>The total</value>
    </data>
    <data name="benchmark_type_statistics">
        <value>Benchmark type statistics</value>
    </data>
    <data name="general_statistics">
        <value>General statistics</value>
    </data>
    <data name="strategic_goals_statistics">
        <value>Strategic goals statistics</value>
    </data>
    <data name="benchmark_method_statistics">
        <value>Benchmark method statistics</value>
    </data>
    <data name="completed">
        <value>Completed</value>
    </data>
    <data name="in_progress">
        <value>In progress</value>
    </data>
    <data name="operational_plans">
        <value>Operational plans</value>
    </data>
    <data name="tasks">
        <value>Tasks</value>
    </data>
    <data name="level_1_operations">
        <value>Level 1 operations</value>
    </data>
    <data name="level_2_operations">
        <value>Level 2 operations</value>
    </data>
    <data name="level_3_operations">
        <value>Level 3 operations</value>
    </data>
    <data name="level_4_operations">
        <value>Level 4 operations</value>
    </data>
    <data name="total_partners">
        <value>Total partners</value>
    </data>
    <data name="total_partners_per_year">
        <value>Total partners per year</value>
    </data>
    <data name="total_partners_evaluations">
        <value>Total partnership contracts evaluations</value>
    </data>
    <data name="total_partnership_contracts">
        <value>Total partnership contracts</value>
    </data>
    <data name="active_partnership_contracts">
        <value>Active partnership contracts</value>
    </data>
    <data name="partnership_contracts_types">
        <value>Partnership contracts types</value>
    </data>
    <data name="service_ownership_statistics">
        <value>Service ownership statistics</value>
    </data>
    <data name="service_category_statistics">
        <value>Service category statistics</value>
    </data>
    <data name="all_partners">
        <value>All</value>
    </data>
    <data name="partners_with_partnership_contract">
        <value>Partners with partnership contract</value>
    </data>
    <data name="partners_without_partnership_contract">
        <value>Partners without partnership contract</value>
    </data>
    <data name="service_type_statistics">
        <value>Service type statistics</value>
    </data>
    <data name="client_category_statistics">
        <value>Client category statistics</value>
    </data>
    <data name="provider_channel_statistics">
        <value>Provider channel statistics</value>
    </data>
    <data name="active_services">
        <value>Active services</value>
    </data>
    <data name="inactive_services">
        <value>Inactive services</value>
    </data>
    <data name="linked_kpis">
        <value>Linked KPIs</value>
    </data>
    <data name="linked_files">
        <value>Linked files</value>
    </data>
    <data name="statistics_by_year">
        <value>Statistics by year</value>
    </data>
    <data name="closed_opportunities">
        <value>Closed opportunities</value>
    </data>
    <data name="total_initiatives">
        <value>Total initiatives</value>
    </data>
    <data name="improvement_opportunity_inputs_sources">
        <value>Improvement opportunity inputs sources</value>
    </data>
    <data name="kpi_results_data_entry_responses">
        <value>KPI results data entry responses</value>
    </data>
    <data name="year">
        <value>Year</value>
    </data>
    <data name="kpi">
        <value>KPI</value>
    </data>
    <data name="status">
        <value>Status</value>
    </data>
    <data name="from">
        <value>From</value>
    </data>
    <data name="to">
        <value>To</value>
    </data>
    <data name="statistical_reports">
        <value>Statistical reports</value>
    </data>
    <data name="active_statistical_reports">
        <value>Active statistical reports</value>
    </data>
    <data name="locked_statistical_reports">
        <value>Locked statistical reports</value>
    </data>
    <data name="not_published_statistical_reports">
        <value>Not published statistical reports</value>
    </data>
    <data name="alternative">
        <value>Alternative</value>
    </data>
    <data name="not_performing_reason">
        <value>Not performing reason</value>
    </data>
    <data name="risk_type_strategic">
        <value>Strategic</value>
    </data>
    <data name="risk_type_operational">
        <value>Operational</value>
    </data>
    <data name="impact_details_is_required">
        <value>Required impact details.</value>
    </data>
    <data name="probability_details_is_required">
        <value>Required probability details.</value>
    </data>
    <data name="annually">
        <value>Annually</value>
    </data>
    <data name="semi_annually">
        <value>Semi annually</value>
    </data>
    <data name="quarterly">
        <value>Quarterly</value>
    </data>
    <data name="monthly">
        <value>Monthly</value>
    </data>
    <data name="daily">
        <value>Daily</value>
    </data>
    <data name="weekly">
        <value>Weekly</value>
    </data>
    <data name="plan_code">
        <value>Plan code</value>
    </data>
    <data name="plan_year">
        <value>Year</value>
    </data>
    <data name="plan_name">
        <value>Plan name</value>
    </data>
    <data name="plan_description">
        <value>Description</value>
    </data>
    <data name="plan_category">
        <value>Plan category</value>
    </data>
    <data name="plan_date_from">
        <value>Plan date from</value>
    </data>
    <data name="plan_date_to">
        <value>Plan date to</value>
    </data>
    <data name="plan_assigned">
        <value>Plan assigned</value>
    </data>
    <data name="plan_government_strategic_goal">
        <value>Government strategic goal</value>
    </data>
    <data name="plan_strategic_goals">
        <value>Plan strategic goals</value>
    </data>
    <data name="plan_ministry_strategic_goals">
        <value>Ministry strategic goals</value>
    </data>
    <data name="plan_flow_state">
        <value>Plan flow state</value>
    </data>
    <data name="plan_progress">
        <value>Plan progress</value>
    </data>
    <data name="plan_tasks_count">
        <value>Tasks count</value>
    </data>
    <data name="plan_subtasks_count">
        <value>Subtasks count</value>
    </data>
    <data name="plan_linked_operations">
        <value>Linked operations</value>
    </data>
    <data name="plan_linked_partners">
        <value>Linked partners</value>
    </data>
    <data name="plan_linked_kpis">
        <value>Linked kpis</value>
    </data>
    <data name="operation_code">
        <value>Operation code</value>
    </data>
    <data name="operation_number">
        <value>Operation number</value>
    </data>
    <data name="operation_name">
        <value>Operation name</value>
    </data>
    <data name="operation_description">
        <value>Operation description</value>
    </data>
    <data name="operation_level">
        <value>Operation level</value>
    </data>
    <data name="operation_owner">
        <value>Operation owner</value>
    </data>
    <data name="operation_weight">
        <value>Weight</value>
    </data>
    <data name="operation_strategic_goals">
        <value>Operation strategic goals</value>
    </data>
    <data name="operation_sustainability_impact">
        <value>Operation sustainability impact</value>
    </data>
    <data name="operation_purpose">
        <value>Operation purpose</value>
    </data>
    <data name="operation_policies">
        <value>Operation policies</value>
    </data>
    <data name="operation_specification">
        <value>Operation specification</value>
    </data>
    <data name="operation_partners">
        <value>Partners</value>
    </data>
    <data name="operation_procedures_count">
        <value>Operation procedures count</value>
    </data>
    <data name="operation_total_enhancements">
        <value>Total enhancements</value>
    </data>
    <data name="operation_last_enhancement_date">
        <value>Last enhancement date</value>
    </data>
    <data name="operation_kpis_count">
        <value>kpis count</value>
    </data>
    <data name="operation_last_modification_date">
        <value>Last modification date</value>
    </data>
    <data name="operation_modification_count">
        <value>Modifications count</value>
    </data>
    <data name="order_is_already_used">
        <value>Order is already used</value>
    </data>
    <data name="code_is_already_used">
        <value>Code is already used</value>
    </data>
    <data name="partnership_termination_request">
        <value>Partnership termination request</value>
    </data>
    <data name="partnership_contract_purpose">
        <value>Partnership contract purpose</value>
    </data>
    <data name="partnership_contract_department_name">
        <value>Partnership contract department name</value>
    </data>
    <data name="partnership_contract_start_date">
        <value>Partnership contract start date</value>
    </data>
    <data name="partnership_termination_request_progress_summary">
        <value>Partnership termination request progress summary</value>
    </data>
    <data name="partnership_termination_request_successes">
        <value>Partnership termination request successes</value>
    </data>
    <data name="partnership_termination_request_lessons_learned">
        <value>Partnership termination request lessons learned"</value>
    </data>
    <data name="partnership_termination_request_partner_notes">
        <value>Partnership termination request partner notes</value>
    </data>
    <data name="partnership_termination_request_termination_date">
        <value>Partnership termination request termination date</value>
    </data>
    <data name="partnership_termination_request_termination_reason">
        <value>Partnership termination request termination reason</value>
    </data>
    <data name="partnership_termination_request_approval">
        <value>Partnership termination request approval</value>
    </data>
    <data name="draft">
        <value>Draft</value>
    </data>
    <data name="submitted">
        <value>Submitted</value>
    </data>
    <data name="approved">
        <value>Approved</value>
    </data>
    <data name="approved:immediate">
        <value>Approved immediate</value>
    </data>
    <data name="rejected">
        <value>Rejected</value>
    </data>
    <data name="rejected:final">
        <value>Rejected final</value>
    </data>
    <data name="approved:final">
        <value>Approved final</value>
    </data>
    <data name="0_invalid_file_type">
        <value>{0} Invalid file type!</value>
    </data>
    <data name="please_add_new_evaluation_type_from_system_setting">
        <value>Please add new evaluation type from system setting.</value>
    </data>
    <data name="evaluation_standards_are_required">
        <value>Evaluation standards are required.</value>
    </data>
    <data name="evaluation_standards_target_greater_than_zero">
        <value>Evaluation standards target should be greater than zero.</value>
    </data>
    <data name="evaluation_values_are_invalid">
        <value>Evaluation values are invalid.</value>
    </data>
    <data name="kpi_code">
        <value>KPI code</value>
    </data>
    <data name="final_years_result">
        <value>Final years result</value>
    </data>
    <data name="notes">
        <value>Notes</value>
    </data>
    <data name="final_result">
        <value>Final result</value>
    </data>
    <data name="evaluation_locked">
        <value>Evaluation is locked</value>
    </data>
    <data name="permission_name_statistical_report">
        <value>Manage statistical reports</value>
    </data>
    <data name="permission_description_statistical_report">
        <value>
            The user will have the ability to view, create, modify, or delete statistical reports.
            Additionally, they can enter results, activate/deactivate, publish, or export the reports.
        </value>
    </data>

    <data name="permission_name_statistical_report:data_entry">
        <value>Enter statistical report results</value>
    </data>
    <data name="permission_description_statistical_report:data_entry">
        <value>
            The user will have the ability to modify the results of statistical reports.
            (provided that the user's department has the right to access the result).
        </value>
    </data>

    <data name="permission_name_statistical_report:read">
        <value>View statistical reports</value>
    </data>
    <data name="permission_description_statistical_report:read">
        <value>
            The user will have the ability to view the statistical reports screen
            in addition to the list of statistical reports.
        </value>
    </data>

    <data name="permission_name_statistical_report:write">
        <value>Create/modify statistical reports</value>
    </data>
    <data name="permission_name_statistical_report:copy">
        <value>Copy statistical reports</value>
    </data>

    <data name="permission_description_statistical_report:write">
        <value>
            The user will have the ability to create or modify statistical reports.
            (provided that the user's department has the right to access the result).
        </value>
    </data>

    <data name="permission_description_statistical_report:copy">
        <value>
            The user will have the ability to copy statistical reports.
            (provided that the user's department has the right to access the result).
        </value>
    </data>

    <data name="permission_name_statistical_report:delete">
        <value>Delete statistical reports</value>
    </data>
    <data name="permission_description_statistical_report:delete">
        <value>
            The user will have the ability to delete statistical reports.
        </value>
    </data>

    <data name="permission_name_statistical_report:export">
        <value>Export statistical reports</value>
    </data>
    <data name="permission_description_statistical_report:export">
        <value>
            The user will have the ability to export statistical reports.
        </value>
    </data>

    <data name="permission_name_statistical_report:lock_and_unlock">
        <value>Activate/deactivate statistical reports</value>
    </data>
    <data name="permission_description_statistical_report:lock_and_unlock">
        <value>
            The user will have the ability to activate or deactivate statistical reports.
        </value>
    </data>

    <data name="permission_name_statistical_report:publish">
        <value>Publish statistical reports</value>
    </data>
    <data name="permission_description_statistical_report:publish">
        <value>
            The user will have the ability to publish statistical reports.
        </value>
    </data>

    <data name="category_type_initiative">
        <value>Initiative</value>
    </data>
    <data name="category_type_plan">
        <value>Plan</value>
    </data>
    <data name="category_type_project">
        <value>Project</value>
    </data>
    <data name="kpi_tag">
        <value>Kpi tag</value>
    </data>
    <data name="kpi_year_basis">
        <value>Kpi year basis</value>
    </data>
    <data name="you_can_forward_to_kpi_manager_from_only_level1">
        <value>You can forward to kpi manager from only level1</value>
    </data>
    <data name="you_can_only_transfer_approval_at_level1_or_level2">
        <value>you can only transfer approval at level 1 or level 2</value>
    </data>
    <data name="some_or_all_of_the_provided_signatories_are_already_assigned_to_the_transfer">
        <value>Some or all of the provided signatories are already assigned to the transfer.</value>
    </data>
    <data name="cannot_delete_plan_0">
        <value>Can't Delete Plan ( {0} )</value>
    </data>
    <data name="is_connected_to_other_resources_with_risk_0">
        <value>Is Connected To Other Resources With Risk ( {0} )</value>
    </data>
    <data name="the_opportunity_should_be_assigned_to_either_a_department_team_or_user">
        <value>the opportunity should be assigned to either a department or team or user</value>
    </data>
    <data name="The_kpi_result_year_cannot_be_deleted_because_there_are_kpi_result_breakdowns">
        <value>The kpi result year cannot be deleted, because there are kpi result breakdowns.</value>
    </data>
    <data name="the_kpi_has_been_approved_hence_adding_results_has_been_disabled">
        <value>There are results that have been approved and locked, so they cannot be deleted.</value>
    </data>

    <data name="kpis_period_not_found">
        <value>There are no kpi's available according to the settings selected.</value>
    </data>
    <data name="cannot_delete_department_0">
        <value>Error: You cannot delete the department ({0}) because there are existing links</value>
    </data>
    <data name="is_connected_to_other_resources_with_benchmarks">
        <value>,with benchmarks</value>
    </data>
    <data name="is_connected_to_other_resources_with_services">
        <value>,with services</value>
    </data>
    <data name="is_connected_to_other_resources_with_opportunities">
        <value>,with improvement opportunities</value>
    </data>
    <data name="is_connected_with_risks">
        <value>,with risks</value>
    </data>
    <data name="is_connected_with_partnership_contracts">
        <value>,with partnership contracts</value>
    </data>
    <data name="is_connected_with_statistical_report_categories">
        <value>,with statistical report categories</value>
    </data>
    <data name="is_connected_with_subtasks">
        <value>,with subtasks</value>
    </data>
    <data name="is_connected_with_tasks">
        <value>,with tasks</value>
    </data>
    <data name="is_connected_with_plans">
        <value>,with plans</value>
    </data>
    <data name="is_connected_with_kpis">
        <value>,with kpis</value>
    </data>
    <data name="is_connected_with_kpi_results">
        <value>,with kpi results</value>
    </data>
    <data name="is_connected_with_operations">
        <value>,with operations</value>
    </data>
    <data name="is_connected_with_children_departments">
        <value>,with children departments</value>
    </data>
    <data name="the_library_item_does_not_have_a_file">
        <value>The file could not be accessed, please re-upload it to the library</value>
    </data>
    <data name="start_time_cannot_come_after_end_time">
        <value>Start date cannot come after end date.</value>
    </data>
    <data name="ensure_that_all_a_values_have_been_filled">
        <value>Ensure that all "a" values have been filled.</value>
    </data>
    <data name="ensure_that_all_b_values_have_been_filled">
        <value>Ensure that all "b" values have been filled.</value>
    </data>
    <data name="ensure_that_all_result_analyses_have_been_filled">
        <value>Ensure that all result analyses have been filled.</value>
    </data>
    <data name="ensure_that_all_improvement_procedures_have_been_filled">
        <value>Ensure that all improvement procedures have been filled.</value>
    </data>
    <data name="ensure_that_at_least_one_attachment_has_been_added">
        <value>Ensure that at least one attachment has been added.</value>
    </data>
    <data name="signatories_have_not_been_specified">
        <value>The person responsible for approving the application must be identified.</value>
    </data>
    <data name="the_subtask_period_should_be_within_the_parent_task">
        <value>The duration and date of subtasks must not exceed the time frame specified for the main task!</value>
    </data>
    <data name="the_task_period_should_be_within_the_parent_plan">
        <value>The period of implementation of the tasks must be within the time frame for implementing the master
            plan.
        </value>
    </data>
    <data name="is_statistical_report_attachment_required">
        <value>Please attach an attachment</value>
    </data>
    <data name="delete_statistical_report_attachment_old">
        <value>To complete the deletion process, you must first add a new attachment and then delete the old one.
        </value>
    </data>
    <data name="department_hierarchy_users_excel_permission_Screen">
        <value>Permissions Screens</value>
    </data>
    <data name="department_hierarchy_users_excel_permission_name">
        <value>Permission name</value>
    </data>
    <data name="department_hierarchy_users_excel_permission_description">
        <value>Permission description</value>
    </data>
    <data name="excel_kpi">
        <value>Kpi</value>
    </data>
    <data name="excel_plan">
        <value>Plan</value>
    </data>
    <data name="excel_category_full_access">
        <value>Full Access</value>
    </data>
    <data name="excel_kpi_result">
        <value>Kpi Result</value>
    </data>
    <data name="excel_operation">
        <value>Operations</value>
    </data>
    <data name="excel_category_benchmark">
        <value>Categories Benchmarks</value>
    </data>
    <data name="excel_user">
        <value>Users</value>
    </data>
    <data name="excel_category_department">
        <value>Departments</value>
    </data>
    <data name="excel_report">
        <value>Reports</value>
    </data>
    <data name="excel_partner">
        <value>Partners</value>
    </data>
    <data name="excel_partnership">
        <value>Partnerships</value>
    </data>
    <data name="excel_success_factor">
        <value>Success Factors</value>
    </data>
    <data name="excel_category_innovation">
        <value>Categories Innovations</value>
    </data>
    <data name="excel_innovator">
        <value>Innovators</value>
    </data>
    <data name="excel_category_statistical_report">
        <value>Statistical Reports</value>
    </data>
    <data name="excel_award">
        <value>Awards</value>
    </data>
    <data name="excel_risk">
        <value>Risks</value>
    </data>
    <data name="excel_category_kpi_result_target_setting_method">
        <value>Kpi Result Target Setting Method</value>
    </data>
    <data name="excel_category_kpi_result_capability_type">
        <value>Kpi Result capabilities Types</value>
    </data>
    <data name="excel_category_kpi_category">
        <value>Kpi categories</value>
    </data>
    <data name="excel_category_kpi_type">
        <value>Kpi categories Types</value>
    </data>
    <data name="excel_category_kpi_balanced_behavior_card">
        <value>Kpi Balanced Behavior Cards</value>
    </data>
    <data name="excel_policy">
        <value>Polices</value>
    </data>
    <data name="excel_category_operation_rule_and_regulation">
        <value>Operations Rules And Regulations</value>
    </data>
    <data name="excel_category_operation_specification">
        <value>Operations Specifications</value>
    </data>
    <data name="excel_capability_type">
        <value>Capabilities Types</value>
    </data>
    <data name="excel_capability">
        <value>Capabilities</value>
    </data>
    <data name="excel_operation_enhancement_type">
        <value>Operations Enhancements Types</value>
    </data>
    <data name="excel_plan_input">
        <value>Plans Inputs</value>
    </data>
    <data name="excel_team">
        <value>Teams</value>
    </data>
    <data name="excel_service">
        <value>Services</value>
    </data>
    <data name="excel_opportunity">
        <value>Opportunities</value>
    </data>
    <data name="excel_improvement_opportunity_input_category">
        <value>Improvements Opportunities Inputs Categories</value>
    </data>
    <data name="excel_improvement_opportunity_input_source">
        <value>Improvements Opportunities Inputs Source</value>
    </data>
    <data name="excel_notification">
        <value>Notifications</value>
    </data>
    <data name="excel_strategic_goal">
        <value>Strategics Goals</value>
    </data>
    <data name="excel_government_strategic_goal">
        <value>Governments Strategics Goals</value>
    </data>
    <data name="excel_category_plan_category">
        <value>Categories Plans Categories</value>
    </data>
    <data name="excel_category_service_category">
        <value>Categories Services Categories</value>
    </data>
    <data name="excel_category_kpi_result_category">
        <value>Categories Kpi Result Categories</value>
    </data>
    <data name="excel_benchmark">
        <value>Benchmarks</value>
    </data>
    <data name="excel_kpi_result_category">
        <value>KPI's results categories</value>
    </data>
    <data name="excel_kpi_result_target_setting_method">
        <value>Kpi result target setting methods</value>
    </data>
    <data name="excel_kpi_tag">
        <value>Kpi Tags</value>
    </data>
    <data name="excel_kpi_type">
        <value>Kpi Types</value>
    </data>
    <data name="excel_operation_specification">
        <value>Operation specification</value>
    </data>
    <data name="excel_operation_rule_and_regulation">
        <value>Operation rules and regulations</value>
    </data>
    <data name="excel_plan_category">
        <value>Plan categories</value>
    </data>
    <data name="excel_kpi_result_capability_type">
        <value>Result Capability Types</value>
    </data>
    <data name="you_are_not_authorized_to_perform_this_action">
        <value>you are not authorized to perform this action</value>
    </data>
    <data name="excel_full_access">
        <value>Full Access</value>
    </data>
    <data name="excel_department">
        <value>Department</value>
    </data>
    <data name="excel_statistical_report">
        <value>Statistical Reports</value>
    </data>
    <data name="excel_improvement_opportunity">
        <value>Improvements Opportunities</value>
    </data>
    <data name="excel_innovation">
        <value>Innovations</value>
    </data>
    <data name="excel_kpi_balanced_behavior_card">
        <value>Kpi Balanced Behavior Cards</value>
    </data>
    <data name="excel_service_category">
        <value>Services Categories</value>
    </data>
    <data name="excel_tournament">
        <value>Tournament</value>
    </data>
    <data name="permission_name_capacity_planning">
        <value>Manage capacity planning</value>
    </data>
    <data name="permission_name_capacity_planning:write">
        <value>Add and edit records</value>
    </data>
    <data name="permission_name_capacity_planning:read">
        <value>Read details and reports</value>
    </data>
    <data name="permission_name_capacity_planning:delete">
        <value>Delete records</value>
    </data>
    <data name="permission_description_capacity_planning">
        <value>Admin of capacity planning</value>
    </data>
    <data name="permission_description_capacity_planning:read">
        <value>Browse reports</value>
    </data>
    <data name="permission_description_capacity_planning:write">
        <value>Add and update records</value>
    </data>
    <data name="permission_description_capacity_planning:delete">
        <value>Delete records</value>
    </data>
    <data name="permission_name_kpi_result:period_evaluate_read">
        <value>Read evaluations</value>
    </data>
    <data name="permission_description_kpi_result:period_evaluate_read">
        <value>Allow user to browse the evaluations list and dashboard within his department scope</value>
    </data>
    <data name="permission_name_kpi_result:period_evaluate_global_read">
        <value>Read evaluations (globally)</value>
    </data>
    <data name="permission_description_kpi_result:period_evaluate_global_read">
        <value>Allow user to browse the evaluations list and dashboard.</value>
    </data>
    <data name="time_dimension_year_month_already_exists">
        <value>A record with Year {0} and Month {1} already exists.</value>
    </data>
    <data name="month_number_must_be_between_1_and_12">
        <value>Month number must be between 1 and 12.</value>
    </data>
    <data name="year_must_be_between_2000_and_0">
        <value>Year must be between 2000 and {0}.</value>
    </data>
</root>
