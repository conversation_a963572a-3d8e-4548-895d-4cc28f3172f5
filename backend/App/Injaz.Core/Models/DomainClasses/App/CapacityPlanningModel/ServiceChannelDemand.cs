using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel;

[Table("capacity_service_channel_demands")]
public class ServiceChannelDemand : ModifiableModel<User>
{
    [Column("department_id")]
    public Guid DepartmentId { get; set; } // this will represent the Police station as an item of departments

    [Column("time_id")]
    public Guid TimeId { get; set; }

    //  I added the service channels as static columns instead of separate table as it mostly never change,
    // and it will be easier to query

    [Column("happiness_center_volume")]
    public int HappinessCenterVolume { get; set; }

    [Column("moi_app_volume")]
    public int MoiAppVolume { get; set; }

    [Column("ajp_app_volume")]
    public int AjpAppVolume { get; set; }

    [Column("web_site_volume")]
    public int WebsiteVolume { get; set; }



    [ForeignKey(nameof(DepartmentId))]
    [InverseProperty(nameof(App.Department.ServiceChannelDemands))]
    public virtual Department Department { get; set; }

    [ForeignKey(nameof(TimeId))]
    [InverseProperty(nameof(TimeDimension.ServiceChannelDemands))]
    public virtual TimeDimension Time { get; set; }
}
