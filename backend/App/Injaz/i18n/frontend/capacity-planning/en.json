{"translate_happiness_center_volume": "Happiness center volume", "translate_service_volumes": "Services volumes", "translate_regular_service_volume": "Regular service volume", "translate_fast_service_volume": "Fast service volume", "translate_mobile_application_volume": "Mobile application volume", "translate_call_center_volume": "Call center volume", "translate_website_volume": "Website volume", "translate_service_channel_demands": "Service channels demand", "translate_search_by_years": "Search by years", "translate_search_by_month": "Search by month", "translate_police_station": "Police station", "translate_time_dimension": "Time dimension", "translate_category_capacity_planning": "Capacity planning", "translate_report_service_channels_demand": "Service channels demand report", "translate_report_center_monthly_demand": "Monthly demand report", "translate_update_center_monthly_demand": "Update monthly demand report", "translate_update_center_services_actual_demand": "Update services actual demand", "translate_report_center_services_actual_demand": "Services actual demand report", "translate_report_center_services_foresight_demand": "Services foresight demand report", "translate_select_the_police_station_first": "Select the police station first!", "translate_auto_generate_the_months": "Add the remains months automatically?", "translate_available_stations_count": "Available stations count", "translate_station_utilization_percentage": "Station utilization percentage", "translate_ideal_station_hourly_productivity": "Ideal station hourly productivity", "translate_available_seats_count": "Available seats count", "translate_available_parking_spaces": "Available parking spaces", "translate_water_cycles_count": "Water cycles count", "translate_sick_leave_days_per_employee_yearly": "Percent of Sick leave days per employee yearly", "translate_training_days_per_employee_yearly": "Training days per employee yearly", "translate_official_work_days_per_employee_yearly": "Official work days per employee yearly", "translate_personal_permission_days_yearly": "Personal permission days yearly", "translate_effective_work_days_yearly": "Effective work days yearly", "translate_work_hours_per_employee_yearly": "Work hours per employee yearly", "translate_available_minutes_per_employee_yearly": "Available minutes per employee yearly", "translate_available_minutes_per_employee_monthly": "Available minutes per employee monthly", "translate_reception_employees_count": "Reception employees count", "translate_average_employee_actual_hourly_productivity": "Average employee actual hourly productivity", "translate_employee_efficiency_percentage": "Employee efficiency percentage", "translate_annual_employee_leaves": "Annual employee leaves", "translate_fast_service_target_time": "Fast service target time", "translate_fast_services_count": "Fast services count", "translate_normal_service_target_time": "Normal service target time", "translate_normal_services_count": "Normal services count", "translate_complex_service_target_time": "Complex service target time", "translate_complex_services_count": "Complex services count", "translate_average_target_time_all_services": "Average target time all services", "translate_total_center_services": "Total center services", "translate_working_minutes_per_day": "Working minutes per day", "translate_center_work_days_per_month": "Center work days per month", "translate_religious_national_holidays_yearly": "Religious national holidays yearly", "translate_station_availability_days_yearly": "Station availability days yearly", "translate_official_working_hours": "Official working hours", "translate_station_availability_hours_daily": "Station availability hours daily", "translate_actual_working_hours_per_employee_daily": "Actual working hours per employee daily", "translate_shifts_count": "Shifts count", "translate_remarks": "Remarks", "translate_station_parameters": "Station parameters", "translate_facility_parameters": "Facility parameters", "translate_employee_parameters": "Employee parameters", "translate_service_parameters": "Service parameters", "translate_time_parameters": "Time parameters", "translate_daily_working_minutes": "Daily Working Minutes", "translate_total_actual_employee_working_hours_per_day": "Total Actual Employee Working Hours per Day", "translate_computed_time_parameters": "Computed Time Parameters", "translate_total_station_availability_days_per_year": "Total Station Availability Days per Year", "translate_total_official_working_hours_for_station": "Total Official Working Hours for Station", "translate_total_station_availability_hours_per_day": "Total Station Availability Hours per Day", "translate_total_station_shifts_computed": "Total Station Shifts Computed", "translate_total_station_working_days_per_month": "Total Station Working Days per Month", "translate_total_religious_and_national_event_days_per_year": "Total Religious and National Event Days per Year", "translate_value_will_be_calculated_automatically": "Value will be calculated automatically", "translate_parameter_name": "Parameter name", "translate_add_new_center_parameters": "Add new police station parameters", "translate_delete_this_item_question_mark": "Are you sure you want to delete this item?", "translate_fast_service_time": "Fast Service Time", "translate_regular_services_count": "Regular Services Count", "translate_regular_service_time": "Regular Service Time", "translate_complex_service_time": "Complex Service Time", "translate_employee_and_work_config": "Employee and Work Configuration", "translate_employees_available": "Employees Available", "translate_daily_work_hours_per_employee": "Daily Work Hours per Employee", "translate_work_days_per_month": "Work Days per Month", "translate_work_hours_per_day": "Work Hours per Day", "translate_religious_and_national_occasions_days_per_month": "Religious and National Occasions Days per Month", "translate_actual_usage": "Actual Usage", "translate_actual_transactions_per_month": "Actual Transactions per Month", "translate_actual_customers_per_month": "Actual Customers per Month", "translate_center_capacity": "Center Capacity", "translate_available_seats_in_center": "Available Seats in Center", "translate_available_parking_at_center": "Available Parking at Center", "translate_total_service_count": "Total Service Count", "translate_total_service_time_minutes": "Total Service Time (Minutes)", "translate_average_service_time_minutes": "Average Service Time (Minutes)", "translate_total_working_minutes_per_month": "Total Working Minutes per Month", "translate_capacity_utilization_minutes": "Capacity Utilization (Minutes)", "translate_capacity_utilization_percentage": "Capacity Utilization (%)", "translate_services_per_employee": "Services per Employee", "translate_services_per_working_day": "Services per Working Day", "translate_customers_per_service": "Customers per Service", "translate_transactions_per_service": "Transactions per Service", "translate_transactions_per_customer": "Transactions per Customer", "translate_center_monthly_demand": "Center monthly demand", "translate_demand_volume_report": "Demand volume report", "translate_forecasting_accuracy": "Forecasting accuracy", "translate_list_updated_successfully": "Police stations list updated", "translate_service_demands": "Services demands", "translate_update_list": "‘Update police stations list", "translate_toggle_enable_all": "Enable all", "translate_toggle_disable_all": "Disable all", "translate_total_peak_hours": "Total peak hours", "translate_ajp_app_volume": "AJP mobile app", "translate_moi_app_volume": "MOI mobile app", "translate_add_center_monthly_demand": "Add police station monthly demand", "translate_edit_center_monthly_demand": "Edit police station monthly demand", "translate_year_and_month": "Year and month", "translate_total_linked_services": "Total linked services", "translate_center_linked_services": "Center linked services", "translate_add_new_time_dimension": "Add new time dimension", "translate_edit_time_dimension": "Edit time dimension"}