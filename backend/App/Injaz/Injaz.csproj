<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <Version>1.12.0</Version>
        <TargetFramework>net6.0</TargetFramework>
        <LangVersion>10</LangVersion>
        <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
        <ApplicationIcon/>
        <Win32Resource/>
        <ImplicitUsings>disable</ImplicitUsings>
        <NoWarn>AD0001</NoWarn>
        <DisableImplicitNamespaceImports>true</DisableImplicitNamespaceImports>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="DotnetOmar.Translation" Version="1.0.9"/>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.0-preview.7.21378.6"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.0" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.1"/>
        <PackageReference Include="QuestPDF" Version="2025.1.5" />
        <PackageReference Include="Sentry.AspNetCore" Version="3.11.0"/>
        <PackageReference Include="Sentry.EntityFramework" Version="3.16.0"/>
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.1.4"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Injaz.Core\Injaz.Core.csproj"/>
        <ProjectReference Include="..\..\Modules\GovernmentStrategicGoal\Injaz.ModuleGovernmentStrategicGoal\Injaz.ModuleGovernmentStrategicGoal.csproj" />
        <ProjectReference Include="..\..\Modules\KpiResultDataEntryRequest\Injaz.ModuleKpiResultDataEntryRequest\Injaz.ModuleKpiResultDataEntryRequest.csproj" />
        <ProjectReference Include="..\..\Modules\MinistryStrategicGoal\Injaz.ModuleMinistryStrategicGoal\Injaz.ModuleMinistryStrategicGoal.csproj" />
        <ProjectReference Include="..\..\Modules\NationalAgenda\Injaz.ModuleNationalAgenda\Injaz.ModuleNationalAgenda.csproj" />
        <ProjectReference Include="..\..\Modules\Policy\Injaz.ModulePolicy\Injaz.ModulePolicy.csproj" />
        <ProjectReference Include="..\..\Modules\Search\Injaz.ModuleSearch\Injaz.ModuleSearch.csproj" />
        <ProjectReference Include="..\..\Modules\StrategicGoal\Injaz.ModuleStrategicGoal\Injaz.ModuleStrategicGoal.csproj" />
        <ProjectReference Include="..\..\Modules\SuccessFactor\Injaz.ModuleSuccessFactor\Injaz.ModuleSuccessFactor.csproj" />
        <ProjectReference Include="..\..\Modules\SystemEvent\Injaz.ModuleSystemEvent\Injaz.ModuleSystemEvent.csproj" />
        <ProjectReference Include="..\..\Modules\UserRequest\Injaz.ModuleUserRequest\Injaz.ModuleUserRequest.csproj" />
        <ProjectReference Include="..\..\Modules\Activity\Injaz.ModuleActivity\Injaz.ModuleActivity.csproj" />
        <ProjectReference Include="..\..\Modules\Benchmark\Injaz.ModuleBenchmark\Injaz.ModuleBenchmark.csproj" />
        <ProjectReference Include="..\..\Modules\Dashboard\Injaz.ModuleDashboard\Injaz.ModuleDashboard.csproj" />
        <ProjectReference Include="..\..\Modules\ImprovementOpportunity\Injaz.ModuleImprovementOpportunity\Injaz.ModuleImprovementOpportunity.csproj" />
        <ProjectReference Include="..\..\Modules\Innovation\Injaz.ModuleInnovation\Injaz.ModuleInnovation.csproj" />
        <ProjectReference Include="..\..\Modules\Innovator\Injaz.ModuleInnovator\Injaz.ModuleInnovator.csproj" />
        <ProjectReference Include="..\..\Modules\KpiResultCapability\Injaz.ModuleKpiResultCapability\Injaz.ModuleKpiResultCapability.csproj" />
        <ProjectReference Include="..\..\Modules\KpiResultDataEntryResponse\Injaz.ModuleKpiResultDataEntryResponse\Injaz.ModuleKpiResultDataEntryResponse.csproj" />
        <ProjectReference Include="..\..\Modules\Team\Injaz.ModuleTeam\Injaz.ModuleTeam.csproj" />
        <ProjectReference Include="..\..\Modules\Operation\Injaz.ModuleOperation\Injaz.ModuleOperation.csproj" />
        <ProjectReference Include="..\..\Modules\Setting\Injaz.ModuleSetting\Injaz.ModuleSetting.csproj" />
        <ProjectReference Include="..\..\Modules\Capability\Injaz.ModuleCapability\Injaz.ModuleCapability.csproj"/>
        <ProjectReference Include="..\..\Modules\Department\Injaz.ModuleDepartment\Injaz.ModuleDepartment.csproj"/>
        <ProjectReference Include="..\..\Modules\Email\Injaz.ModuleEmail\Injaz.ModuleEmail.csproj"/>
        <ProjectReference Include="..\..\Modules\KpiResultCategory\Injaz.ModuleKpiResultCategory\Injaz.ModuleKpiResultCategory.csproj"/>
        <ProjectReference Include="..\..\Modules\KpiResult\Injaz.ModuleKpiResult\Injaz.ModuleKpiResult.csproj"/>
        <ProjectReference Include="..\..\Modules\Kpi\Injaz.ModuleKpi\Injaz.ModuleKpi.csproj"/>
        <ProjectReference Include="..\..\Modules\FileLibrary\Injaz.ModuleLibrary\Injaz.ModuleLibrary.csproj"/>
        <ProjectReference Include="..\..\Modules\LinkedApplication\Injaz.ModuleLinkedApplication\Injaz.ModuleLinkedApplication.csproj"/>
        <ProjectReference Include="..\..\Modules\Notification\Injaz.ModuleNotification\Injaz.ModuleNotification.csproj"/>
        <ProjectReference Include="..\..\Modules\Partnership\Injaz.ModulePartnership\Injaz.ModulePartnership.csproj"/>
        <ProjectReference Include="..\..\Modules\StatisticalReport\Injaz.ModuleStatisticalReport\Injaz.ModuleStatisticalReport.csproj"/>
        <ProjectReference Include="..\..\Modules\Tournament\Injaz.ModuleTournament\Injaz.ModuleTournament.csproj"/>
        <ProjectReference Include="..\..\Modules\User\Injaz.ModuleUser\Injaz.ModuleUser.csproj"/>
        <ProjectReference Include="..\..\Modules\Partner\Injaz.ModulePartner\Injaz.ModulePartner.csproj"/>
        <ProjectReference Include="..\..\Modules\Plan\Injaz.ModulePlan\Injaz.ModulePlan.csproj"/>
        <ProjectReference Include="..\..\Modules\Service\Injaz.ModuleService\Injaz.ModuleService.csproj"/>
        <ProjectReference Include="..\..\CapacityPlanning/Injaz.ModuleCapacityPlanning/Injaz.ModuleCapacityPlanning.csproj"/>

    </ItemGroup>
</Project>
