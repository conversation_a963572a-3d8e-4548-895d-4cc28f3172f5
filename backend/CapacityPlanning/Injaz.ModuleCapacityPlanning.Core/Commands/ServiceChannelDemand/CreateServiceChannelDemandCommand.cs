using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.CapacityPlanning.ServiceChannelDemands;
using Injaz.Core.Dtos.CapacityPlanning.TimeDimension;
using Injaz.Core.Dtos.Department;
using MediatR;

namespace Injaz.ModuleCapacityPlanning.Core.Commands.ServiceChannelDemand;

public class CreateServiceChannelDemandCommand : IRequest<ServiceChannelDemandGetDto>
{
    [Required(ErrorMessage = "0_is_required")]
    public DepartmentSimpleDto? Department { get; set; }

    [Required(ErrorMessage = "0_is_required")]
    public TimeDimensionSimpleDto? Time { get; set; }
    public int HappinessCenterVolume { get; set; }

    public int MoiAppVolume { get; set; }

    public int AjpAppVolume { get; set; }

    public int WebsiteVolume { get; set; }

}
