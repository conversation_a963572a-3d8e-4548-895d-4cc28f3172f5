using Injaz.Core.Exceptions;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleCapacityPlanning.Core.Commands.ServiceChannelDemand;
using Injaz.ModuleCapacityPlanning.Core.Queries.ServiceChannelDemand;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleCapacityPlanning.Controllers;

public class ServiceChannelDemandsController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public ServiceChannelDemandsController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpGet]
    [Authorize(Policy = PermissionNameList.CapacityPlanningRead)]
    [Route("/capacity-planning/service-channel-demand")]
    public async Task<IActionResult> List(
        Guid? department = null,
        int[]? years = null,
        int[]? months = null,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        var query = new GetServiceChannelDemandListQuery
        {
            Department = department,
            Years = years ?? new []{ DateTime.Now.Year },
            Months = months,
            PageSize = pageSize,
            PageNumber = pageNumber
        };

        return this.GetResponseObject(extra: await _mediator.Send(query));
    }

    [HttpGet]
    [Authorize(Policy = PermissionNameList.CapacityPlanningRead)]
    [Route("/capacity-planning/service-channel-demand/{id:guid}")]
    public async Task<IActionResult> Get(Guid id, bool forEdit = false)
    {
        var item = forEdit
            ? await _mediator.Send(new GetServiceChannelDemandForEditByIdQuery { Id = id }) as object
            : await _mediator.Send(new GetServiceChannelDemandByIdQuery { Id = id });

        return this.GetResponseObject(extra: item);
    }

    [HttpGet]
    [Authorize(Policy = PermissionNameList.CapacityPlanningRead)]
    [Route("/capacity-planning/service-channel-demand/report")]
    public async Task<IActionResult> ForecastingReport(GetServiceChannelDemandInsightReportQuery query)
    {
        // Guid.TryParse("11abab8d-0373-419e-98c7-07031f3aa366", out var departmentId);
        // query.DepartmentId = departmentId;
        // query.Year = 2025;
        var items = await _mediator.Send(query);

        return this.GetResponseObject(extra: items);
    }

    [HttpPost]
    [Authorize(Policy = PermissionNameList.CapacityPlanningWrite)]
    [Route("/capacity-planning/service-channel-demand")]
    public IActionResult Create([BindBodySingleJson] CreateServiceChannelDemandCommand serviceChannelDemand)
    {
        // Manual creation of ServiceChannelDemand is disabled
        // ServiceChannelDemand records are now automatically created/updated when CenterMonthlyDemand is created/updated
        throw new GenericException()
        {
            Messages = new[] { "Manual creation of Service Channel Demand is disabled. Service Channel Demand records are automatically managed through Center Monthly Demand." }
        };
    }

    [HttpPut]
    [Authorize(Policy = PermissionNameList.CapacityPlanningWrite)]
    [Route("/capacity-planning/service-channel-demand")]
    public IActionResult Update([BindBodySingleJson] UpdateServiceChannelDemandCommand serviceChannelDemand)
    {
        // Manual update of ServiceChannelDemand is disabled
        // ServiceChannelDemand records are now automatically created/updated when CenterMonthlyDemand is created/updated
        throw new GenericException()
        {
            Messages = new[] { "Manual update of Service Channel Demand is disabled. Service Channel Demand records are automatically managed through Center Monthly Demand." }
        };
    }

    [HttpDelete]
    [Authorize(Policy = PermissionNameList.CapacityPlanningDelete)]
    [Route("/capacity-planning/service-channel-demand/{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await _mediator.Send(new DeleteServiceChannelDemandCommand
        {
            Id = id
        });

        return this.GetResponseObject(messages: new string[] { _localizer["item_removed_successfully"] });
    }
}
