using Injaz.Core;
using Injaz.Core.Dtos.CapacityPlanning.ServiceChannelDemands;
using Injaz.Core.Exceptions;
using Injaz.Core.Permission;
using Injaz.Core.Services;
using Injaz.ModuleCapacityPlanning.Core.Commands.ServiceChannelDemand;
using Injaz.ModuleCapacityPlanning.Services;
using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Injaz.ModuleCapacityPlanning.Handlers.ServiceChannelDemands;

public class CreateOrUpdateServiceChannelDemandHandler :
    IRequestHandler<CreateServiceChannelDemandCommand, ServiceChannelDemandGetDto>,
    IRequestHandler<UpdateServiceChannelDemandCommand, ServiceChannelDemandGetDto>
{
    private readonly DbContext _db;
    private readonly ValidationService _validationService;
    private readonly CurrentUserService _currentUserService;
    private readonly PermissionEnsurerService _permissionEnsurerService;
    private readonly DataEntryPermissionChecker _dataEntryPermissionChecker;

    public CreateOrUpdateServiceChannelDemandHandler(
        DbContext db,
        ValidationService validationService,
        CurrentUserService currentUserService,
        PermissionEnsurerService permissionEnsurerService,
        DataEntryPermissionChecker dataEntryPermissionChecker)
    {
        _db = db;
        _validationService = validationService;
        _currentUserService = currentUserService;
        _permissionEnsurerService = permissionEnsurerService;
        _dataEntryPermissionChecker = dataEntryPermissionChecker;
    }

    public Task<ServiceChannelDemandGetDto> Handle(CreateServiceChannelDemandCommand command,
        CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command, cancellationToken);
    }

    public Task<ServiceChannelDemandGetDto> Handle(UpdateServiceChannelDemandCommand command,
        CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command, cancellationToken);
    }

    private async Task<ServiceChannelDemandGetDto> CreateOrUpdate(CreateServiceChannelDemandCommand command,
        CancellationToken cancellationToken)
    {

        if (!_validationService.IsValid(command, out var errors))
        {
            throw new GenericException
            {
                Messages = errors.Select(x => x.ErrorMessage).ToArray()
            };
        }

        // Check if user has access to the specified department
        var userId = _currentUserService.GetId();
        var hasFullAccessPermission = await _permissionEnsurerService.Ensure(PermissionNameList.FullAccess);

        if (command.Department == null)
        {
            throw new GenericException()
            {
                Messages = new[] { "Department is required" }
            };
        }

        if (command.Time == null)
        {
            throw new GenericException()
            {
                Messages = new[] { "Time dimension is required" }
            };
        }

        // Check if we're allowed to create or update this entry
        await _dataEntryPermissionChecker.IsServiceChannelDemandEntryAllowed(
            command.Department.Id,
            command.Time.Id,
            cancellationToken);

        // Check if user has permission for this department
        AppHelperFunctions.AssertUserInvolvedWithDepartment(
            userId,
            command.Department.Id,
            _db.Departments,
            hasFullAccessPermission);

        // Check if the time dimension exists
        var timeDimension = _db.TimeDimensions.Find(command.Time.Id);
        if (timeDimension == null)
        {
            throw new GenericException()
            {
                Messages = new[] { "Time dimension does not exist" }
            };
        }

        Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel.ServiceChannelDemand? item;

        if (command is UpdateServiceChannelDemandCommand updateCommand)
        {
            item = await _db.ServiceChannelDemands
                .Include(serviceChannelDemand => serviceChannelDemand.Department)
                .FirstOrDefaultAsync(
                x => x.Id == updateCommand.Id, cancellationToken);

            if (item == null)
                throw new ItemNotFoundException();

            // Check if user is changing department
            if (item.Department.Id != command.Department.Id)
            {
                throw new GenericException()
                {
                    Messages = new[] { "Cannot change department for demand data" }
                };
            }

            UpdateDemandData(item, command);
        }
        else
        {
            // Check if there's already data for this department and time
            var existingData = await _db.ServiceChannelDemands
                .FirstOrDefaultAsync(x => x.Department.Id == command.Department.Id &&
                                     x.Time.Id == command.Time.Id, cancellationToken);

            if (existingData != null)
            {
                throw new GenericException()
                {
                    Messages = new[] { "Demand data already exists for this department and time period" }
                };
            }

            item = new Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel.ServiceChannelDemand
            {
                DepartmentId = command.Department.Id,
                TimeId = command.Time.Id,
                CreatedById = userId,
                CreationTime = DateTime.UtcNow
            };

            UpdateDemandData(item, command);
            _db.ServiceChannelDemands.Add(item);
        }

        _db.SaveChanges();

        return  _db.ServiceChannelDemands
            .AsExpandable()
            .Where(p => p.Id == item.Id)
            .Select(ServiceChannelDemandGetDto.Mapper(HelperFunctions.GetLanguageCode()))
            .First();
    }

    private void UpdateDemandData(Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel.ServiceChannelDemand item, CreateServiceChannelDemandCommand command)
    {
        item.HappinessCenterVolume = command.HappinessCenterVolume;
        item.MoiAppVolume = command.MoiAppVolume;
        item.AjpAppVolume = command.AjpAppVolume;
        item.WebsiteVolume = command.WebsiteVolume;
    }
}
