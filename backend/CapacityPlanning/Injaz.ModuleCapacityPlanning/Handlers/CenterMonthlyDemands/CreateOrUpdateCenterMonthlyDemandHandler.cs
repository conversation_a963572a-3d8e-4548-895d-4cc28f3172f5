using Injaz.Core;
using Injaz.Core.Dtos.CapacityPlanning.CenterMonthlyDemand;
using Injaz.Core.Dtos.CapacityPlanning.TimeDimension;
using Injaz.Core.Exceptions;
using Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel;
using Injaz.Core.Permission;
using Injaz.Core.Services;
using Injaz.ModuleCapacityPlanning.Core.Commands.CenterMonthlyDemand;
using Injaz.ModuleCapacityPlanning.Core.Commands.ServiceChannelDemand;
using Injaz.ModuleCapacityPlanning.Services;
using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Injaz.ModuleCapacityPlanning.Handlers.CenterMonthlyDemands;

public class CreateOrUpdateCenterMonthlyDemandHandler :
    IRequestHandler<CreateCenterMonthlyDemandCommand, CenterMonthlyDemandGetDto>,
    IRequestHandler<UpdateCenterMonthlyDemandCommand, CenterMonthlyDemandGetDto>
{
    private readonly DbContext _db;
    private readonly ValidationService _validationService;
    private readonly CurrentUserService _currentUserService;
    private readonly PermissionEnsurerService _permissionEnsurerService;
    private readonly DataEntryPermissionChecker _dataEntryPermissionChecker;
    private readonly IMediator _mediator;

    public CreateOrUpdateCenterMonthlyDemandHandler(
        DbContext db,
        ValidationService validationService,
        CurrentUserService currentUserService,
        PermissionEnsurerService permissionEnsurerService,
        DataEntryPermissionChecker dataEntryPermissionChecker,
        IMediator mediator)
    {
        _db = db;
        _validationService = validationService;
        _currentUserService = currentUserService;
        _permissionEnsurerService = permissionEnsurerService;
        _dataEntryPermissionChecker = dataEntryPermissionChecker;
        _mediator = mediator;
    }

    public Task<CenterMonthlyDemandGetDto> Handle(CreateCenterMonthlyDemandCommand command,
        CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command, cancellationToken);
    }

    public Task<CenterMonthlyDemandGetDto> Handle(UpdateCenterMonthlyDemandCommand command,
        CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command, cancellationToken);
    }

    private async Task<CenterMonthlyDemandGetDto> CreateOrUpdate(CreateCenterMonthlyDemandCommand command,
        CancellationToken cancellationToken)
    {
        if (!_validationService.IsValid(command, out var errors))
        {
            throw new GenericException
            {
                Messages = errors.Select(x => x.ErrorMessage).ToArray()
            };
        }

        var userId = _currentUserService.GetId();
        var hasFullAccessPermission = await _permissionEnsurerService.Ensure(PermissionNameList.FullAccess);
        var hasWriteAccessPermission = await _permissionEnsurerService.Ensure(PermissionNameList.CapacityPlanningWrite);

        if (!hasFullAccessPermission && !hasWriteAccessPermission)
        {
            throw new GenericException()
            {
                Messages = new[] { "You do not have permission to access this feature." }
            };
        }

        if (command.Department == null)
        {
            throw new GenericException()
            {
                Messages = new[] { "Department is required" }
            };
        }

        // Check if the time dimension exists
        var timeDimension = _db.TimeDimensions
            .FirstOrDefault(x => x.Id == command.Time.Id);

        if (timeDimension == null)
        {
            throw new GenericException()
            {
                Messages = new[] { "Time dimension does not exist" }
            };
        }

        // Check if we're allowed to create or update this entry
        await _dataEntryPermissionChecker.IsCenterMonthlyDemandEntryAllowed(
            command.Department.Id,
            timeDimension.Id,
            cancellationToken);

        // Check if user has permission for this department
        AppHelperFunctions.AssertUserInvolvedWithDepartment(
            userId,
            command.Department.Id,
            _db.Departments,
            hasFullAccessPermission);

        CenterMonthlyDemand? item;

        if (command is UpdateCenterMonthlyDemandCommand updateCommand)
        {
            item = await _db.CenterMonthlyDemands.FirstOrDefaultAsync(
                x => x.Id == updateCommand.Id, cancellationToken);

            if (item == null)
                throw new ItemNotFoundException();

            // Check if user is changing department
            if (item.DepartmentId != command.Department.Id)
            {
                throw new GenericException()
                {
                    Messages = new[] { "Cannot change department for monthly capacity" }
                };
            }

            UpdateCapacityData(item, command);
        }
        else
        {
            // Check if there's already data for this department and time
            var existingData = await _db.CenterMonthlyDemands
                .FirstOrDefaultAsync(x => x.DepartmentId == command.Department.Id &&
                                     x.TimeId == timeDimension.Id, cancellationToken);

            if (existingData != null)
            {
                throw new GenericException()
                {
                    Messages = new[] { "Monthly capacity data already exists for this department and time period" }
                };
            }

            item = new CenterMonthlyDemand
            {
                DepartmentId = command.Department.Id,
                TimeId = timeDimension.Id,
            };

            UpdateCapacityData(item, command);
            _db.CenterMonthlyDemands.Add(item);
        }

        _db.SaveChanges();

        // Automatically create or update ServiceChannelDemand with the service channel data
        await CreateOrUpdateServiceChannelDemand(command, timeDimension, cancellationToken);

        return _db.CenterMonthlyDemands
            .AsExpandable()
            .Where(p => p.Id == item.Id)
            .Select(CenterMonthlyDemandGetDto.Mapper(HelperFunctions.GetLanguageCode()))
            .First();
    }

    private void UpdateCapacityData(CenterMonthlyDemand item, CreateCenterMonthlyDemandCommand command)
    {
        // Employee data
        item.EmployeesAvailable = command.EmployeesAvailable;

        // Service data
        item.FastServicesCount = command.FastServicesCount;
        item.FastServiceTime = command.FastServiceTime;
        item.RegularServicesCount = command.RegularServicesCount;
        item.RegularServiceTime = command.RegularServiceTime;
        item.ComplexServicesCount = command.ComplexServicesCount;
        item.ComplexServiceTime = command.ComplexServiceTime;

        // Time parameters
        item.DailyWorkHoursPerEmployee = command.DailyWorkHoursPerEmployee;
        item.WorkDaysPerMonth = command.WorkDaysPerMonth;
        item.WorkHoursPerDay = command.WorkHoursPerDay;
        item.ReligiousAndNationalOccasionsDaysPerMonth = command.ReligiousAndNationalOccasionsDaysPerMonth;

        // Actual usage data
        item.ActualTransactionsPerMonth = command.ActualTransactionsPerMonth;
        item.ActualCustomersPerMonth = command.ActualCustomersPerMonth;

        // Facilities data
        item.AvailableSeatsInCenter = command.AvailableSeatsInCenter;
        item.AvailableParkingAtCenter = command.AvailableParkingAtCenter;

        // services channels
        item.HappinessCenterVolume = command.HappinessCenterVolume;
        item.MoiAppVolume = command.MoiAppVolume;
        item.AjpAppVolume = command.AjpAppVolume;
        item.WebsiteVolume = command.WebsiteVolume;
    }

    private async Task CreateOrUpdateServiceChannelDemand(
        CreateCenterMonthlyDemandCommand command,
        Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel.TimeDimension timeDimension,
        CancellationToken cancellationToken)
    {
        try
        {
            // Check if ServiceChannelDemand already exists for this department and time
            var existingServiceChannelDemand = await _db.ServiceChannelDemands
                .FirstOrDefaultAsync(x => x.DepartmentId == command.Department.Id &&
                                         x.TimeId == timeDimension.Id, cancellationToken);

            if (existingServiceChannelDemand != null)
            {
                // Update existing ServiceChannelDemand
                var updateCommand = new UpdateServiceChannelDemandCommand
                {
                    Id = existingServiceChannelDemand.Id,
                    Department = command.Department,
                    Time = new TimeDimensionSimpleDto
                    {
                        Id = timeDimension.Id,
                        Year = timeDimension.Year,
                        MonthNumber = timeDimension.MonthNumber,
                        Name = $"{timeDimension.MonthNumber} {timeDimension.Year}"
                    },
                    HappinessCenterVolume = command.HappinessCenterVolume,
                    MoiAppVolume = command.MoiAppVolume,
                    AjpAppVolume = command.AjpAppVolume,
                    WebsiteVolume = command.WebsiteVolume
                };

                await _mediator.Send(updateCommand, cancellationToken);
            }
            else
            {
                // Create new ServiceChannelDemand
                var createCommand = new CreateServiceChannelDemandCommand
                {
                    Department = command.Department,
                    Time = new TimeDimensionSimpleDto
                    {
                        Id = timeDimension.Id,
                        Year = timeDimension.Year,
                        MonthNumber = timeDimension.MonthNumber,
                        Name = $"{timeDimension.MonthNumber} {timeDimension.Year}"
                    },
                    HappinessCenterVolume = command.HappinessCenterVolume,
                    MoiAppVolume = command.MoiAppVolume,
                    AjpAppVolume = command.AjpAppVolume,
                    WebsiteVolume = command.WebsiteVolume
                };

                await _mediator.Send(createCommand, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            // Log the error but don't fail the main operation
            // The CenterMonthlyDemand creation should succeed even if ServiceChannelDemand fails
            // You might want to add proper logging here
            Console.WriteLine($"Failed to create/update ServiceChannelDemand: {ex.Message}");
        }
    }
}
