using Injaz.Core.Dtos.CapacityPlanning.TimeDimension;
using Injaz.Core.Exceptions;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Injaz.ModuleCapacityPlanning.Core.Commands.TimeDimension;
using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleCapacityPlanning.Handlers.TimeDimension;

public class CreateOrUpdateTimeDimensionHandler :
    IRequestHandler<CreateTimeDimensionCommand, TimeDimensionGetDto>,
    IRequestHandler<UpdateTimeDimensionCommand, TimeDimensionGetDto>
{
    private readonly DbContext _db;
    private readonly ValidationService _validationService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public CreateOrUpdateTimeDimensionHandler(
        DbContext db,
        ValidationService validationService,
        CurrentUserService currentUserService,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _db = db;
        _validationService = validationService;
        _localizer = localizer;
    }

    public Task<TimeDimensionGetDto> Handle(CreateTimeDimensionCommand command, CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    public Task<TimeDimensionGetDto> Handle(UpdateTimeDimensionCommand command, CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    private async Task<TimeDimensionGetDto> CreateOrUpdate(CreateTimeDimensionCommand command)
    {
        // Validate input
        ValidateInput(command);

        if (!_validationService.IsValid(command, out var errors))
        {
            throw new GenericException
            {
                Messages = errors.Select(x => x.ErrorMessage).ToArray()
            };
        }

        if (command is UpdateTimeDimensionCommand updateCommand)
        {
            return await HandleUpdateAsync(updateCommand);
        }
        else
        {
            return await HandleCreateAsync(command);
        }
    }

    private void ValidateInput(CreateTimeDimensionCommand command)
    {
        // Validate month number range
        if (command.MonthNumber < 1 || command.MonthNumber > 12)
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["month_number_must_be_between_1_and_12"] }
            };
        }

        // Validate year range (reasonable business range)
        var currentYear = DateTime.UtcNow.Year;
        if (command.Year < 2000 || command.Year > currentYear + 10)
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["year_must_be_between_2000_and_0", currentYear + 10] }
            };
        }
    }

    private async Task<TimeDimensionGetDto> HandleUpdateAsync(UpdateTimeDimensionCommand command)
    {
        var item = await _db.TimeDimensions
            .FirstOrDefaultAsync(x => x.Id == command.Id);

        if (item is null)
            throw new ItemNotFoundException();

        // Check for duplicate (excluding current record)
        await ValidateDuplicateAsync(command.Year, command.MonthNumber, command.Id);

        item.Year = command.Year;
        item.MonthNumber = command.MonthNumber;

        await _db.SaveChangesAsync();

        return await GetTimeDimensionDtoAsync(item.Id);
    }

    private async Task<TimeDimensionGetDto> HandleCreateAsync(CreateTimeDimensionCommand command)
    {
        if (command.AutoAddMonths)
        {
            return await HandleAutoAddMonthsAsync(command);
        }

        // Check for duplicate
        await ValidateDuplicateAsync(command.Year, command.MonthNumber);

        var item = new Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel.TimeDimension
        {
            Year = command.Year,
            MonthNumber = command.MonthNumber,
        };

        _db.TimeDimensions.Add(item);
        await _db.SaveChangesAsync();

        return await GetTimeDimensionDtoAsync(item.Id);
    }

    private async Task<TimeDimensionGetDto> HandleAutoAddMonthsAsync(CreateTimeDimensionCommand command)
    {
        var existingMonths = await _db.TimeDimensions
            .Where(x => x.Year == command.Year)
            .Select(x => x.MonthNumber)
            .ToListAsync();

        var monthsToAdd = Enumerable.Range(1, 12)
            .Where(month => !existingMonths.Contains(month))
            .Select(month => new Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel.TimeDimension
            {
                Year = command.Year,
                MonthNumber = month,
            })
            .ToList();

        if (monthsToAdd.Any())
        {
            await using var transaction = await _db.Database.BeginTransactionAsync();
            try
            {
                _db.TimeDimensions.AddRange(monthsToAdd);
                await _db.SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        // Return empty DTO for bulk operations as per original logic
        return new TimeDimensionGetDto();
    }

    private async Task ValidateDuplicateAsync(int year, int monthNumber, Guid? excludeId = null)
    {
        var query = _db.TimeDimensions
            .Where(x => x.Year == year && x.MonthNumber == monthNumber);

        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        var duplicateExists = await query.AnyAsync();

        if (duplicateExists)
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["time_dimension_year_month_already_exists", year, monthNumber] }
            };
        }
    }

    private async Task<TimeDimensionGetDto> GetTimeDimensionDtoAsync(Guid id)
    {
        return await _db.TimeDimensions
            .AsExpandable()
            .Select(TimeDimensionGetDto.Mapper())
            .SingleAsync(x => x.Id == id);
    }
}
