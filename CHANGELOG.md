# [1.12.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.11.2...v1.12.0) (2025-06-25)

### Bug Fixes

-   **capacity:** change columns types to double and fix error ([7d492ae](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/7d492aefb79e1c2d13f02791a9b4e73e0d49839f))
-   fix department nullable level number code ([4d135ce](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/4d135ce5313810d170fb048ed48a1477685767d9))
-   fixing runner work flow ([bfa985a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/bfa985a4d9663325ed5f99415859af95a2892339))
-   **release:** update Node.js version to 20 for semantic-release compatibility ([a661831](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/a661831ca3e1cbc6bde2ee3e5b53a80b72513d02))
-   semantic release work flow ([fe23f8d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/fe23f8d4c1f790ddc01f0a8515f68f151327880a))
-   **user-request:** added department tree to request form ([7358fa2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/7358fa221337ecf4a93f29897985e0fac2ba5cf0))

### Features

-   **department:** create department tree select feature ([b401453](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/b401453453f1afd8819bb3ef210e8480849dc466))
-   **mnm-form:** add validation for integer only ([004b84e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/004b84ed3259f3893b250153b6ca828b63dcfb81))

## [1.11.2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.11.1...v1.11.2) (2025-06-19)

### Bug Fixes

-   **evaluations:** allow user who read the kpi result to read the evaluation result ([693bc51](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/693bc51af8472eaff435f93b96ad4cae3e78ad45))

## [1.11.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.11.0...v1.11.1) (2025-06-16)

### Bug Fixes

-   **operations:** enhance the operation detail page performance ([cb391aa](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/cb391aa508ae00bb632e6c51c1cf47b77aee6380))

# [1.11.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.10.1...v1.11.0) (2025-05-28)

### Bug Fixes

-   **backend:** remove all unit fields from center parameter ([d7fd1da](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/d7fd1da5badef4eda8e84de2745a491128b9118c))
-   **capacity:** refactor center parameters remove and add columns ([a349413](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/a3494131545dbef24db674e61af2696cd163c850))
-   **capacity:** refactor center parameters remove and add columns ([eba9892](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/eba98922772d78190228953e7bfb6f6e88ca7d52))
-   **capacity:** refactor center parameters remove and add columns ([a2c2f08](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/a2c2f08b4da021949563aadb5f4ddd29ed4c70eb))
-   **departments:** sort department users by is authorized to sign ([e578868](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/e578868886107b3eead7928a03481b13b8338a15))
-   **misc:** create method to list the departments use is involve ([04719a2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/04719a244c0c8643bcbfe3924fb72925ab5c5ceb))
-   **misc:** create method to list the departments use is involve ([0587e8f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/0587e8fdf5a7ae9f3519fa68219979f4cd359864))
-   re-arrange the side menu items in capacity planning section ([7baaca9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/7baaca9cad8d67193e603d05e1370a66a69dbb24))
-   remove unused references and modify center monthly demands ([c02c003](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/c02c0035a21588c3b286ffbe535619273155870f))

### Features

-   **capacity:** refactor service capacity and apply the client changes ([403bafd](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/403bafdb75e95a3f6185a25109b9617cfc883bf3))
-   **capacity:** refactor service capacity and apply the client changes ([9c88011](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/9c880117118a90daa371112bf9163a13a54e4214))
-   **departments:** add link to user profile form department detail page ([ad9d2c7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/ad9d2c7618371ae04a27d35b8288ee6e36b8291e))
-   **departments:** add link to user profile form department detail page ([1191b86](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/1191b865831f28be8e2a74200c252b2e4e18bf07))

## [1.10.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.10.0...v1.10.1) (2025-05-20)

### Bug Fixes

-   **backend:** fix translation ([43e5086](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/43e5086f7692a496c0807c3aadd5dd71aaccb73b))
-   **backend:** fully revert plan flow changes and update connection string ([17785d9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/17785d92daa848279796c7ee03df22fe19da27b7))
-   **backend:** revert old changes flow-plan ([de12fd4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/de12fd4e2de045178a2d64a8e365e53363b7c13f))

# [1.10.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.9.0...v1.10.0) (2025-05-13)

### Features

-   **service:** add export services to Excel file ([d8e48f6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/d8e48f6fb9a535ea94bb5b82272f406814c96d1f))

# [1.9.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.8.0...v1.9.0) (2025-05-12)

### Features

-   **evaluation:** exempt kpis results from the evaluation list page by checkboxes ([135cefc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/135cefcbfe81dc5d7d33ba3d958c4574b4a6e662))
-   **evaluation:** exempt kpis results from the evaluation list page by checkboxes ([842591a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/842591a96f77d6edc561e85d35443b4ce773c7b2))
-   **evaluation:** exempt kpis results from the evaluation list page by checkboxes ([15d91b6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/15d91b6639438540f3f9cb5c07fdfaa06bc48628))
-   **evaluation:** exempt kpis results from the evaluation list page by checkboxes ([fb37f6f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/fb37f6f19b03f2b354e82a18105e27f15fd0b958))
-   **evaluation:** exempt kpis results from the evaluation list page by checkboxes ([99b0f39](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/99b0f3997b09f7d0f345368fef7d291b07801a90))

# [1.8.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.7.5...v1.8.0) (2025-05-09)

### Features

-   **evaluation:** separate toggle kpi evaluation status and add to the list page filter ([812f89f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/812f89fff949bcc1497f4b367715869449fd002b))
-   **evaluation:** separate toggle kpi evaluation status and add to the list page filter ([ca77ffa](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/ca77ffab1ad9b04b783681b6be6a43dc616c9397))
-   **users:** save users filter state and add shortcut link to user profile ([385c562](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/385c562542de9988b45aa2a4f86b3f80ba0af47b))

## [1.7.5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.7.4...v1.7.5) (2025-05-06)

### Bug Fixes

-   **capacity:** enhance capacity ([663c7f2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/663c7f2465d57e945950b8ff63f7c9f1d11a2052))
-   **capacity:** fix code rabbit comments ([56725df](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/56725df68917e6c509345c91440a21f823a5f5e1))
-   **capacity:** fix files and dynamic forms ([b078d74](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/b078d744f4e29ecddfc7be20f3492da8797d6a5d))

## [1.7.4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.7.3...v1.7.4) (2025-04-30)

### Bug Fixes

-   **kpis:** fix permissions and evaluation issues ([3545d3e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/3545d3e73e4c0e4d25133d456331d0f309d5d27d))

## [1.7.3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.7.2...v1.7.3) (2025-04-30)

### Bug Fixes

-   **evaluation:** fix query filter ([27d28ee](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/27d28ee9a70a773b8fdf24aed3cc99f749c94626))

## [1.7.2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.7.1...v1.7.2) (2025-04-29)

### Bug Fixes

-   **evaluations:** fix dashboard error ([072a751](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/072a75181c2b8566ac8c2470daf41aa6750d4e3b))

## [1.7.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.7.0...v1.7.1) (2025-04-29)

### Bug Fixes

-   **evaluations:** enhance evaluations filters and permissions ([1275628](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/127562890a3a7479f6c9bfa7533846b23bf694ae))
-   update database password in appsettings.Development.json ([b971d81](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/b971d81975f6191bcae9b0c55a6d7d8a7a86a51d))

# [1.7.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.6.1...v1.7.0) (2025-04-24)

### Bug Fixes

-   **backend:** fix the side menu translation ([ef37777](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/ef3777721f7558a4e831f3d3ded63337992157a1))

### Features

-   **capacity:** add enable and disable data entry feature and other misc ([e019111](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/e019111606474ee84c5909690107b35f4251c3bd))
-   **capacity:** add update police stations list in period of time dimension ([4f2b4eb](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/4f2b4eb48240eb48ebc95335a662b68fdee4ae53))

## [1.6.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.6.0...v1.6.1) (2025-04-23)

### Bug Fixes

-   **front:** fixed translation issue in export pdf button in service details ([5880fb6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/5880fb625f75fef98d74f7777ca0fca9e9b9f95f))

# [1.6.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.5.0...v1.6.0) (2025-04-23)

### Bug Fixes

-   **backend:** typo in words ([065e0ec](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/065e0ec414bd228b4fc6f07a6f091c0b8bcde2e5))
-   **backend:** typo in words ([ce38bee](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/ce38bee05d1d12179fa9339041cb465e5c3d1802))

### Features

-   **backend:** arabic content issue in print service pdf ([dbd41fa](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/dbd41fa8aedc4239cf59ad8a4201a6014fa09c39))

# [1.5.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.4.0...v1.5.0) (2025-04-22)

### Features

-   **user:** integrate with HR system ([9d9247c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/9d9247c06fccfb0240623a40229776bb7a8484d9))

# [1.4.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.3.1...v1.4.0) (2025-04-22)

### Bug Fixes

-   **backend:** refactor directories names and name spaces ([81a16aa](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/81a16aab4269a6a8119f294c08ca6f88519b10fe))
-   **backend:** rename objects, fix models and db context, add misc api ([c0d572c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/c0d572c31c631354d4356a7b3bdb799ebd6c1072))
-   **capacity:** misc fixes ([4b08b59](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/4b08b594754a320b641473438bd6cb00e4048923))
-   **frontend:** add feature to table controller and form template ([b25cd48](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/b25cd48f20b892f1ac751aac6d22fd34816e4567))
-   **frontend:** add grid feature to the dynamic form section ([d706379](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/d706379ee820c1f10eb2e502d33099ad0c2b68c9))
-   **frontend:** finalized service channel and time dimensions components, add language , side menu ([beae7f5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/beae7f531d6f66317ef91892d92249e7edef7b5e))

### Features

-   **capacity:** add service channels demand and refactor files ([0c477ed](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/0c477edf99d7c25cb900d283910e4d446068223a))
-   **capacity:** create service channel report ([4699fba](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/4699fba9c3bb7270733caa908f5da235d0515052))
-   **capacity:** create service monthly demand ([09d4d86](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/09d4d8634c8582defc90bc507e35c5084ed9aeb1))
-   **capacity:** create service monthly demand backend ([002faf9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/002faf91f38ceeea3701ccc2fb325122385586c8))
-   **capacity:** finalize backend and fronted of center monthly demand ([b237fc8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/b237fc8791bb1cc305d33bac4dfabaa8b285b8ac))
-   **capacity:** finalize backend and fronted of center parameters ([1c9b68d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/1c9b68ded1aef009bf49c6133a04b95a7a295351))
-   **capacity:** migration and data modeling and translation ([5db7219](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/5db7219385ab37f76e86b074cfd369b2af75f528))

## [1.3.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.3.0...v1.3.1) (2025-04-11)

### Bug Fixes

-   **backend:** fix nullable issue when IsTrend true ([c79d06b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/c79d06b74e8f973c73758f50ba90442b941221f9))

# [1.3.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.2.1...v1.3.0) (2025-04-10)

### Bug Fixes

-   **backend-frontend:** fix departments dropdown in plans ([b5cd8e2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/b5cd8e2d1170b064f005cde70880e695c95f5795))
-   **backend:** disable compiler analyzer ([f43e8d0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/f43e8d01883fbed198a972d9fb8c42979dd91db7))
-   **backend:** fix fun IsInvolvedWithPlanEntity ([de4fd08](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/de4fd08b96df14dd97f53574e1532f09a1b0b8e6))
-   **backend:** fix permission send actions assigned to user ([88a5e15](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/88a5e15ef265631a52b17e160e0c6971d56f3725))
-   **backend:** remove save changes async ([17bff40](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/17bff400b9ab78c9bf525ae42f89e2f5b7f1ac44))
-   **backend:** removed the duplicate keys ([9aafaf2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/9aafaf25917f86c947d9b3d35fcf2a6e432626a9))
-   **department:** add new flag is police station ([f9ded7d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/f9ded7db4842b1d5db68f8956a3b54a8913a0575))
-   **frontend:** fix ranking ([05c1d9e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/05c1d9ecdaa912db12398f15d52a453251ede09f))
-   **front:** fixed missing email label issue in some fields ([e0c7021](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/e0c7021366470dbea7867f603d17c779393f324b))
-   **plan:** fix plan dto slow loading issue ([5ff9e1c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/5ff9e1cf8bedf486be1024aecff21f3f3f3e170b))

### Features

-   **capacity:** add database models and database context ([86f8828](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/86f8828a9ae1f9df8408ceff23384a1e36d931f6))
-   **capacity:** add db migrations for capacity entities ([b035f98](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/b035f982b23b94d56f4c5fbe840f0a98f39c7702))
-   **capacity:** create backend time dimensions and fix data entry periods ([c8d1c21](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/c8d1c21a854a4bd90d81562b74c37ebc624c0efa))
-   **capacity:** create center parameters ([71894fc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/71894fc2540bd423f09be77c215ebb34fa066032))
-   **capacity:** create data entry controls ([a12b266](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/a12b2662a57e98d0ebb45902f00c14e408652607))
-   **capacity:** create frontend interfaces and finish time dimensions ([7a5bb84](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/7a5bb8472259d77280574166c8f7d97607b37b09))
-   **capacity:** create new module capacity planning ([ee2edf0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/ee2edf0fbc667f26bd5f211f8fa6a851d54b4c16))
-   **capacity:** create service channels ([3ff9a3d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/3ff9a3de171c6cff8d47aedd33ad519d1fe9b9f6))
-   **capacity:** create service channels ([314e8de](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/314e8de92c4147546b44077e7fb0a661156fe4ab))
-   **capacity:** create service monthly demand feature ([e5353e0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/e5353e0cfc6d022fdf0f340f8d22345183e5dd28))
-   **capacity:** create the center monthly demands feature ([92300eb](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/92300ebe4364c1c68390b5a119b850079329a020))
-   **capacity:** create time dimension entity ([dd468e2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/dd468e271be0886efe18f030038198b9e8056b8b))
-   **capacity:** define capacity planning permissions ([2de5f88](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/2de5f883a9688605149e583ea61d80b0bfc8dfe3))
-   **frontend:** add is police station checkbox to department ([3695b95](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/3695b9528bbb1b6c2509022b935123821ca1e21f))
-   **frontend:** show approval and reject button in final stage ([9f7d8b7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/9f7d8b7967f63e928bcd07202a62403d7a5aac8b))

## [1.2.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.2.0...v1.2.1) (2025-03-17)

### Bug Fixes

-   **backend:** upgrade six labors library ([687fabd](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/687fabd4d38a4e82e2af22a068cc7b4b37776905))

# [1.2.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.1.1...v1.2.0) (2025-03-16)

### Bug Fixes

-   **partnership:** correct column name ([f259838](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/f25983859af461b0d79c14abdda051ab36379f96))

### Features

-   **backend:** add screen permission in excel ([6f13358](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/6f133588b917729f4b84be07981e9c9b8d9821ce))
-   **front:** enhanced email label and fixed placeholder issue ([438021e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/438021e6945af60962c5fa2c039799b9e5bc93fb))

## [1.1.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.1.0...v1.1.1) (2025-03-12)

### Bug Fixes

-   **plans:** fix dashboard error dividing by zero ([0bbf9cc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/0bbf9cc6ac41cc586e8c2178ca18a7dceff74e41))

# [1.1.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/compare/v1.0.0...v1.1.0) (2025-03-12)

### Bug Fixes

-   **CI:** fix quality check workflow ([ea8d7f3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/ea8d7f35d375cd1ecb4dbdde69954a18e0ad158a))
-   **CI:** fix quality check workflow ([c9e013f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/c9e013fc30596356281cc63cad01b338b8958645))
-   fix workflow ([3a58fa3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/3a58fa30189b276d1b690a761d6f98d9e52819d4))
-   fix workflow ([e67e68c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/e67e68cb1d186bd01b41ac7184e226ac13e62939))
-   fix workflow ([c93bc39](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/c93bc39da9484ec1f4db308a3d96d3a3bad0cd20))
-   fix workflow and linting errors ([db0b386](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/db0b38681ed631ae178ef40975113973e29dfa86))
-   **front:** added missing en translation ([471a1bd](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/471a1bd4fed25120fe399a50a69d760883cfc3df))
-   **frontend:** fix import path ([2ceb169](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/2ceb1693fa9dd4972142358867b51d21ba1c3d07))
-   **frontend:** fix linting ([d0c55b9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/d0c55b96b7acb82b3a6326129884f02e119dffea))
-   **frontend:** fix login component ([27e543a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/27e543a91cd4dabf64f2c790c99f4ff7c4c38ec3))
-   **front:** migrate from old version ([6ef6dfe](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/6ef6dfe0e98321f9d491895899189ddb50e8a604))
-   workflow final fix ([0099c52](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/0099c52798539134421168df21d821ac0298c9c9))
-   **workflow:** fix build-backend job ([2b1c085](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/2b1c085744d2cd8443cbde46b7819bf64831df03))

### Features

-   **backend:** print service to pdf ([f44441d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/f44441da06bc6ac11337ce5f8d0ba76cdcec6fa7))
-   **front:** added missing login with employee number ([9cf921a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/9cf921aacc8298c68c6531b98068f29979589770))
-   **front:** added print button to service details ([12716f3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/12716f39c52ce63fac9a94d97526e4f2e0c95740))
-   **migration:** add new column partnership_scope alternative to partnership_scope_links ([1927d07](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/1927d0794011d72e2a0b17e4e35706cbf426d854))
-   **partnership:** backend add partnership_scope field ([e796e2f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/e796e2f9932c50f227edda1b228a4d6d79c06bd6))
-   **partnership:** frontend added partnership_scope field and removed the scope list ([2ebfe7c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/2ebfe7c5c3ffa3322ab49d3cbe4be97731de8f49))

# 1.0.0 (2025-02-28)

### Features

-   **docker:** update docker files ([1922db4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/commit/1922db4c9f41f548bf9d2d1bc685c2e36b1c6d92))

# [1.140.0](https://github.com/boring91/injaz/compare/v1.139.0...v1.140.0) (2025-02-20)

### Bug Fixes

-   **backend:** fix isSharedKpi filter ([1ed63b5](https://github.com/boring91/injaz/commit/1ed63b5568737b0fcf1ed6154fa62a119e188c77))
-   **front:** added shared kpi filter as param in the request ([fc0db01](https://github.com/boring91/injaz/commit/fc0db01ff5feaffa02835d0fa701c779248a6c2e))
-   **plans:** removed adding one hour to `to` plan entities before saving ([8ec223b](https://github.com/boring91/injaz/commit/8ec223b406dcb15e438bf7e4b79d6359ac5959ad))

### Features

-   **backend:** add isSharedKpi in filter kpi ([e8bfb84](https://github.com/boring91/injaz/commit/e8bfb846556e8824975b3319133a57e311a5a0ae))
-   **backend:** search user by email and employee number ([6fe325e](https://github.com/boring91/injaz/commit/6fe325e762cbbdc6bfc305aa987f4b6414637383))
-   **front:** added search by email and employment number ([de800dd](https://github.com/boring91/injaz/commit/de800dd29bdb7c53e336d92c759265aa112043a2))
-   **front:** added translation to the new filter box ([1f4f052](https://github.com/boring91/injaz/commit/1f4f0529fddc8b738a489579d0be0191f82e8b4b))
-   **front:** adding filter checkbox to kpi table ([aeced3e](https://github.com/boring91/injaz/commit/aeced3ece53c0de1f9918f9089395115cfec2ab8))

# [1.139.0](https://github.com/boring91/injaz/compare/v1.138.1...v1.139.0) (2025-02-17)

### Bug Fixes

-   **front:** added duplicate translation ([f3eb485](https://github.com/boring91/injaz/commit/f3eb485ef71843fb867e50cd712f732799d9f300))
-   **front:** added translation for partner table header ([3b0dcad](https://github.com/boring91/injaz/commit/3b0dcadf3ad4dcacd21c67efe3ddc675169f09a0))
-   **frontend:** add permission on delete and edit ([54c9d08](https://github.com/boring91/injaz/commit/54c9d08f8097db707e759b5fc18805ad8f7a5f47))
-   **frontend:** fix make isExemptFromEvaluation enabled for admin only ([8818554](https://github.com/boring91/injaz/commit/88185545463fde78a4b2cfeb70d307e2ef9f914a))
-   **frontend:** fix permission authorization status in departments ([3087827](https://github.com/boring91/injaz/commit/3087827c21a3ee2458075d450776ab4c0254030e))
-   **frontend:** fix permission delete in departments ([a7d7650](https://github.com/boring91/injaz/commit/a7d7650bcc2f12bd878c3249af1098b370cd3ef9))
-   **frontend:** make isExemptFromEvaluation enabled for admin only ([50f85b4](https://github.com/boring91/injaz/commit/50f85b4381071af7899a45e479cfea631851e55a))
-   **front:** enhanced code to use item tag ([f176381](https://github.com/boring91/injaz/commit/f1763814b88057a2cdd02d3c984bee8371d8c4b3))
-   **front:** fixed hide ministryStrategicGoal nav item ([509f25c](https://github.com/boring91/injaz/commit/509f25c76464fae1a907561462d49de22c45f963))
-   **front:** fixed hiding filter and settings nav item based on dynamic settings ([5cba700](https://github.com/boring91/injaz/commit/5cba7000b5cd691f692ba9f32a0727b341a1a69a))
-   **front:** fixed kpi filter options to be based on user current department ([3eae40b](https://github.com/boring91/injaz/commit/3eae40babbf068420ba45c6b13c2d2f1ce93d975))
-   **front:** small code refactoring ([d69c27a](https://github.com/boring91/injaz/commit/d69c27aac66c7104f7775f36d109b9ab51f3e484))

### Features

-   **backend:** add user and creation time and department to partners list ([5c9acdf](https://github.com/boring91/injaz/commit/5c9acdfd68c98a3fc95ff263796268443484b092))
-   **backend:** allow user with full access to get all statistical reports ([636343f](https://github.com/boring91/injaz/commit/636343fe333ec1ad79f3006f02b445b49805ac07))
-   **backend:** modify data entry request ([9962e3e](https://github.com/boring91/injaz/commit/9962e3e06a8808537ab5d4b816cfcc3d73d52571))
-   **backend:** modify data entry request ([a37e477](https://github.com/boring91/injaz/commit/a37e477ffae110e482f4877cc9e3acc435edda5b))
-   **backend:** not send kpi results to owen department ([4cf33a7](https://github.com/boring91/injaz/commit/4cf33a7b97b8f9d7fe72181e84f0b54508e39689))
-   **backend:** sign up request unread notification ([cad0a0a](https://github.com/boring91/injaz/commit/cad0a0a02b8888a83af25f54d9cd305f23e4186e))
-   **front:** added badge to settings icon and side nav in system settings ([bbae527](https://github.com/boring91/injaz/commit/bbae527e498fa34bf6f8aaef1919c3d27e16d118))
-   **front:** added new columns in partner list ([3846dd2](https://github.com/boring91/injaz/commit/3846dd20f69478323ac9e5a9761b110e7c4467c5))

## [1.138.1](https://github.com/boring91/injaz/compare/v1.138.0...v1.138.1) (2025-02-12)

### Bug Fixes

-   **plan:** check if total weight equals to one by using a threshold rather than a strict equality ([776c8d7](https://github.com/boring91/injaz/commit/776c8d717ee431acc906a481aaa865099fbe6b47))
-   **plans:** fix return 0.9999 when sum tasks weight ([bc8c7c2](https://github.com/boring91/injaz/commit/bc8c7c23df7d56e3a15cd65a0d373587b3df3de2))
-   **plans:** fix the subtask add file limitation when user linked with child department only ([67dbc6e](https://github.com/boring91/injaz/commit/67dbc6e9abb6171292ed380fa8dfe88c5823757f))
-   **plans:** limit access to plans using permissions and departments ([750ac75](https://github.com/boring91/injaz/commit/750ac75be78acee7b24244fe25b1d0576a2ef4a7))
-   **plans:** limit plans list in waiting for approval page ([15e6697](https://github.com/boring91/injaz/commit/15e6697b14fe47143866a029dd9521040cfc58d3))
-   **plans:** solve the permissions issue with linking files with subtasks ([5738db5](https://github.com/boring91/injaz/commit/5738db5a7fb60a4dd714cf14318b57b7450e1ea7))
-   **plan:** wrong total weight for plan ([04dd107](https://github.com/boring91/injaz/commit/04dd107a88e1264b363a9112af4c3d9bacb71713))

# [1.138.0](https://github.com/boring91/injaz/compare/v1.137.0...v1.138.0) (2025-02-11)

### Bug Fixes

-   **front:** added password and confirm password ([1afac36](https://github.com/boring91/injaz/commit/1afac36975eb515632fe1c48ef4f852ac8fcd9ad))
-   **front:** enhanced optional fields in new user request ([251bcd9](https://github.com/boring91/injaz/commit/251bcd9a9b593464fa436088125a8cdd631eafff))
-   **front:** fixed translation issue ([b9321bb](https://github.com/boring91/injaz/commit/b9321bbb601bcd0d2eb1c7a9a96ec82ab528e078))
-   **front:** hide library button and checkbox from custom organizations ([690584e](https://github.com/boring91/injaz/commit/690584eeff703663356c76c8ff1b2bdcadd699b6))
-   **front:** restored required validation and enhanced the code based on DR mohamed advice ([0bc7a91](https://github.com/boring91/injaz/commit/0bc7a913123393023ef8dcd1acec403b38c8b94a))

### Features

-   **backend:** add password for new user request ([5b489fb](https://github.com/boring91/injaz/commit/5b489fbc8405e8a730bda9b441575423c2a312c3))

# [1.137.0](https://github.com/boring91/injaz/compare/v1.136.0...v1.137.0) (2025-02-11)

### Bug Fixes

-   **front:** hide evaluation list in kpi details ([a4f5d24](https://github.com/boring91/injaz/commit/a4f5d246d68099935fbaff69784fe2b19e140f05))

### Features

-   **backend:** exclude draft and approve final state from not approved plans ([54a6540](https://github.com/boring91/injaz/commit/54a6540597a86acb925aba2941cf6fc7885c5665))
-   **front:** added more permissions for settings btn ([b053e4a](https://github.com/boring91/injaz/commit/b053e4ae91d59f1230e4224b2530d31f7e0c44ce))

# [1.136.0](https://github.com/boring91/injaz/compare/v1.135.0...v1.136.0) (2025-02-10)

### Bug Fixes

-   **frontend:** fix error Missing return type on function ([e75470d](https://github.com/boring91/injaz/commit/e75470d674599810de377960c3c63d39474694fc))
-   **frontend:** fix issue check notes ([e59bb16](https://github.com/boring91/injaz/commit/e59bb16db13738abb305110759462be3c7c09e81))
-   **frontend:** fix issue check notes ([9ba0d7a](https://github.com/boring91/injaz/commit/9ba0d7a4e794b744392f0bb7d33ba0a0c56d2428))
-   **frontend:** fix issue check notes ([b7820dd](https://github.com/boring91/injaz/commit/b7820dd507ba151f93ff5a87af43a7d9c06b9f23))
-   **frontend:** fix lint ([967a0c3](https://github.com/boring91/injaz/commit/967a0c399a64717b0aa4a7a60ef396989520d795))
-   **frontend:** fix name ([756e38a](https://github.com/boring91/injaz/commit/756e38a94539083e21bc7db2eda648e951912609))
-   **frontend:** modify back button ([55fc083](https://github.com/boring91/injaz/commit/55fc083149d8d3ecab400d85eaa6ff1a2c38a56b))
-   **frontend:** modify back button ([c1f6d53](https://github.com/boring91/injaz/commit/c1f6d53319163acbdab89ed791a55a63d9b34ef3))
-   **frontend:** modify back button translation for risk and partners ([6053b98](https://github.com/boring91/injaz/commit/6053b980d86c081c3c7fbddbfb04f8be63e74d2b))
-   **frontend:** remove unused ([4cd4e01](https://github.com/boring91/injaz/commit/4cd4e01cc434122218be93d4173095782a0e1f2f))

### Features

-   **backend-frontend:** show or hidden internal partner from plan with filter ([0d8559a](https://github.com/boring91/injaz/commit/0d8559a1d971557c10cd264f77a34f40bac0f027))
-   **backend-frontend:** show or hidden risk type ([f1d0567](https://github.com/boring91/injaz/commit/f1d0567ebbf91653bf28ceca98f83f9e627c4050))
-   **front:** added new translation keys ([533cc73](https://github.com/boring91/injaz/commit/533cc73b5e0ae6dd3e8a4e298ff727f3c0831b13))
-   **frontend:** fix translate and tabs in nav menu ([7adff2f](https://github.com/boring91/injaz/commit/7adff2fda6a2a57bfdb5aec4b33c9e985115ebd1))
-   **frontend:** show or hidden risk type in detail ([04fdf0a](https://github.com/boring91/injaz/commit/04fdf0a598406fe06ca5abcf533dba755e9c013b))

# [1.135.0](https://github.com/boring91/injaz/compare/v1.134.1...v1.135.0) (2025-02-06)

### Features

-   **frontend:** hidden evaluation section from police and injaz ([b784124](https://github.com/boring91/injaz/commit/b784124721b7cc8c010929e8e7e97dc4c98c4a25))
-   **frontend:** remove evaluation list section from kpi detail and translate evaluation in risk ([5965650](https://github.com/boring91/injaz/commit/5965650ed90c9566c46973296d125196d3cc9c6f))
-   **frontend:** remove evaluation list section from kpi detail and translate evaluation in risk ([009119c](https://github.com/boring91/injaz/commit/009119c2d0910f514ee5ef6e79d2de067e669b46))
-   **front:** login access request allowed for police and dev only ([b099184](https://github.com/boring91/injaz/commit/b0991842af0d353f682509aa6473c6a550f209eb))

## [1.134.1](https://github.com/boring91/injaz/compare/v1.134.0...v1.134.1) (2025-02-06)

### Bug Fixes

-   **kpis:** fix performance issues and timeout errors ([2712a22](https://github.com/boring91/injaz/commit/2712a22246bde3d9fb2e9125a91ff1fede826b2a))
-   **users:** added missing fields and required validation ([bc7e7e3](https://github.com/boring91/injaz/commit/bc7e7e3a5e2ac476e22d85a9e72e66de4bd71df4))

# [1.134.0](https://github.com/boring91/injaz/compare/v1.133.0...v1.134.0) (2025-02-05)

### Features

-   **backend-frontend:** modify on new user request ([36e480b](https://github.com/boring91/injaz/commit/36e480bc1c1c5eccec3bbf2ad3dc097088063389))

# [1.133.0](https://github.com/boring91/injaz/compare/v1.132.0...v1.133.0) (2025-02-05)

### Bug Fixes

-   **backend-frontend:** fix format ([313c0ce](https://github.com/boring91/injaz/commit/313c0ce3c81d39dbe77a2529c7a2c003c517fde8))
-   **backend-frontend:** fix transfer approves ([1c549a9](https://github.com/boring91/injaz/commit/1c549a932141f8a345b278cbd77e3e1cb11f3c70))
-   **backend:** delete comments ([6758975](https://github.com/boring91/injaz/commit/675897535bc2460d883c7cc4fefb60c5e09c86d9))
-   **backend:** fix formate ([eed1398](https://github.com/boring91/injaz/commit/eed13984a110e5850a1e36e731b83b0a3e9ea233))
-   **backend:** fix translation duplicate ([ec08185](https://github.com/boring91/injaz/commit/ec081855ffc677cb855a91b2824431c0584693d2))
-   **backend:** let admin approve plan ([5767bc2](https://github.com/boring91/injaz/commit/5767bc2f85e2050927689a698b44bd18a6d3425a))
-   **backend:** wait for on move before returning in flows ([622d42b](https://github.com/boring91/injaz/commit/622d42b63bc4193d9494d1388e193a231372bfa7))
-   errors after merging with develop ([5112636](https://github.com/boring91/injaz/commit/5112636d59cec57ebdeab05710155496856cef6f))
-   **frontend:** fix condition for periods ([a15a55a](https://github.com/boring91/injaz/commit/a15a55ac0a7df8797dd9656bad24ebf7db985f45))
-   **frontend:** fix issue ([84bb68d](https://github.com/boring91/injaz/commit/84bb68d7a67fa3c9f2615e4eb5f971370ee910ba))
-   **frontend:** fix issue when measurement cycle is quarter ([72be89b](https://github.com/boring91/injaz/commit/72be89b9530170d0192f6c449af822676f4a6987))
-   **frontend:** fix monthly periods ([7a0b158](https://github.com/boring91/injaz/commit/7a0b158ed30a217259c3d443df9a0b88c2f48d3e))
-   **frontend:** fix save translate in plan ([34ca19f](https://github.com/boring91/injaz/commit/34ca19f8909349ab07ca68adb6f6ef74fd2d6a30))
-   **frontend:** fix translate and modify size (move button) ([83633c3](https://github.com/boring91/injaz/commit/83633c38310cd16f66eade9fd65800fa6f8962e7))
-   **frontend:** modify translate app ar ([a03c276](https://github.com/boring91/injaz/commit/a03c2769d7c12d620c09a563d6ebf8f1c85384fa))
-   **new-user-request:** added department to request ([fa771b0](https://github.com/boring91/injaz/commit/fa771b001f529658a9c3d3e8a26fba10b81ae92f))
-   **new-user-request:** create user on approval ([b2378ec](https://github.com/boring91/injaz/commit/b2378ec50505e1e13bac967727cd9cfb1ed11c56))
-   **new-user-request:** link request with user ([2d10dcf](https://github.com/boring91/injaz/commit/2d10dcfd7cee2f49a1a773a13113e6e1f0c45cf0))
-   **new-user-request:** prevent from entering emails similar to pending requests ([f67de6e](https://github.com/boring91/injaz/commit/f67de6e7e47b32461e19d6a509a40a2a18ea55a0))

### Features

-   **backend-frontend:** show or hidden (UsersWithSignatureApprovalInTopLevels) ([8b6eabb](https://github.com/boring91/injaz/commit/8b6eabb5d0c9801c62ce52871c401a37058cf2e6))
-   **backend-frontend:** show or hidden (UsersWithSignatureApprovalInTopLevels) for TransferApproves ([f1ae360](https://github.com/boring91/injaz/commit/f1ae3605fac245efae479dc89471b15f94e856b7))
-   **backend:** add new endpoint previous period ([f6d84af](https://github.com/boring91/injaz/commit/f6d84afd65b87d84e38f0baed5051ae35399094c))
-   **backend:** add period , measurementCycle ([60661bd](https://github.com/boring91/injaz/commit/60661bdd4e915e2fdf62a6b3ed10a2b476f09d3f))
-   **backend:** allow admins to change status to any status ([62bb949](https://github.com/boring91/injaz/commit/62bb949a11911a7618d3a1aac6374999128067bd))
-   **backend:** change status ([5048f33](https://github.com/boring91/injaz/commit/5048f3376abbf34436c0af06b9cacc1ca49a21db))
-   **backend:** using current user service ([a1b3dab](https://github.com/boring91/injaz/commit/a1b3dab55a0f226244e50234d5df33b5520c10d4))
-   **backend:** when the status is draft start the process from the beginning ([89dd15e](https://github.com/boring91/injaz/commit/89dd15e363c6c966c10f7be14dbceb7cb8808599))
-   **front:** added disabled fields for period breakdown update component ([607fb30](https://github.com/boring91/injaz/commit/607fb30cac71c424350e346a42d1eb6b9d3b254b))
-   **front:** displaying the kpi period cycle label correctly ([329e7be](https://github.com/boring91/injaz/commit/329e7bec7ea7d2e9b074dac7a69f2fc457febc3b))
-   **frontend:** fix translate ([9d94109](https://github.com/boring91/injaz/commit/9d9410907ede925ea1f1276b09a02597ce24e8ca))
-   **frontend:** fix translate ar ([eb8827e](https://github.com/boring91/injaz/commit/eb8827e8c0150d9ed3b83a4e581d1172887bdea8))
-   **frontend:** fix translate en ([a24a41f](https://github.com/boring91/injaz/commit/a24a41f7244b40d65beea0adaca58363ba45d1eb))
-   **frontend:** fix translate en ar ([f84a20f](https://github.com/boring91/injaz/commit/f84a20f1c1955eea619ac41e38490cd2a73e842a))
-   **frontend:** modify translate ar ([e9c9434](https://github.com/boring91/injaz/commit/e9c9434ac6f51d4d0dd8ac13fa657b51c2bcaeda))
-   **frontend:** work on period ([5cd4deb](https://github.com/boring91/injaz/commit/5cd4debedf1b5611c99f14125622ba3cea5dc520))
-   **frontend:** work on period ([45b0ca3](https://github.com/boring91/injaz/commit/45b0ca347d5c974e647983b101ecd01bbaf90ade))
-   **new-user-request:** added ability for unauthorized users to request new accounts ([e750c10](https://github.com/boring91/injaz/commit/e750c10a5af123bb5487e85d093bddd371af4215))

# [1.132.0](https://github.com/boring91/injaz/compare/v1.131.0...v1.132.0) (2025-01-28)

### Bug Fixes

-   **backend-frontend:** add measuringDepartment in app setting to enabled or no ([ece92d9](https://github.com/boring91/injaz/commit/ece92d97586e83b82f68e01d9b9583cd774d10e9))
-   **backend-frontend:** fix notes ([a425b38](https://github.com/boring91/injaz/commit/a425b38aecdf94531f5899a5d08bf5a085d59a43))
-   **frontend:** add condition on measuring department checkbox ([84d08cb](https://github.com/boring91/injaz/commit/84d08cb0adbd564672ff72d07d45da2eb1b15bec))
-   **frontend:** fix word syncKpiSettings ([9de095f](https://github.com/boring91/injaz/commit/9de095f88f1d984835e5e3b6ff5a76f0c44ee794))
-   **frontend:** remove measuring department from kpi evaluation ([7e5582c](https://github.com/boring91/injaz/commit/7e5582c64bb028801da894b143c572423cc6da4f))
-   **front:** fix to hide measuring department filter in multiple page based on settings ([2a34209](https://github.com/boring91/injaz/commit/2a34209e49337a513028f3357d55ccee331ba2e6))
-   **front:** fixed field required issue with validation message ([e95c66e](https://github.com/boring91/injaz/commit/e95c66eb34377962002db985858a80d088c4f57c))

### Features

-   **backend:** add IsRequired for MeasuringDepartment ([104509b](https://github.com/boring91/injaz/commit/104509b77d484b2e889e554f4849f3bc8111adf1))

# [1.131.0](https://github.com/boring91/injaz/compare/v1.130.0...v1.131.0) (2025-01-24)

### Bug Fixes

-   **frontend:** hide library link from nav bar from police ([1093f79](https://github.com/boring91/injaz/commit/1093f79b449ba696e8a7a907d936420b3383d1ff))
-   **frontend:** hide risk permissions from police ([60ee917](https://github.com/boring91/injaz/commit/60ee9173549402c5f4c56197cdefaf4c6f74f956))

### Features

-   **backend:** fix issue with serial number and risk number when adding a new risk ([50c75e0](https://github.com/boring91/injaz/commit/50c75e0974b8de71fff716b81c57d2fa7dfda5f5))
-   **backend:** sort descending by order ([44bf0c3](https://github.com/boring91/injaz/commit/44bf0c30b300a10a013ab6ae575195eae24ad69b))

# [1.130.0](https://github.com/boring91/injaz/compare/v1.129.0...v1.130.0) (2025-01-24)

### Bug Fixes

-   **backend:** delete comments ([8d4d2f3](https://github.com/boring91/injaz/commit/8d4d2f32a05e1b8c0a3b849ae63470e00545f29b))
-   **backend:** delete comments ([a3c90e3](https://github.com/boring91/injaz/commit/a3c90e344ffbb2d08c9c91897ce032543b8dda84))
-   **front:** added ask popup in new partnership-contract ([0fb5a94](https://github.com/boring91/injaz/commit/0fb5a949bb9563f104a811b0478b97009804f5eb))
-   **frontend:** fix Prettier format ([538b66a](https://github.com/boring91/injaz/commit/538b66a1ebeb4ef575c35771191ed8a3b03dc049))
-   **frontend:** fixed back button style and enhanced the back button in page details component ([700cd38](https://github.com/boring91/injaz/commit/700cd384c95e3f386f9323b3be620c98f4eb28c8))
-   **front:** fixed re-routing issue in partnership contract popup ([8e3acd1](https://github.com/boring91/injaz/commit/8e3acd1bfc0efb26437d8e7aa79e82cf7664fe63))
-   **front:** removed none-useful submit config interface and its related code ([0c7a5dc](https://github.com/boring91/injaz/commit/0c7a5dcfde5c8b774961ee84800e0bf01a2b08f6))

### Features

-   **backend:** get formula description from kpi ([1decf99](https://github.com/boring91/injaz/commit/1decf9980f33aaf23dac90e1ec9232711e03cad0))
-   **backend:** notify coordinator when kpi result data entry rejected ([94ecd85](https://github.com/boring91/injaz/commit/94ecd85eb83834931142d35a45d5f3b17b48e73d))
-   **front:** hide risks in settings for police and injaz ([22dc91f](https://github.com/boring91/injaz/commit/22dc91febd96db9fadd05b03812996fb1a679c89))
-   **partnership-contract:** added optional alert in new partnership-contract ([8030f7d](https://github.com/boring91/injaz/commit/8030f7dd2c9e6e004a7c9a4e9b95480b32914a72))

# [1.129.0](https://github.com/boring91/injaz/compare/v1.128.0...v1.129.0) (2025-01-23)

### Bug Fixes

-   **backend:** fix bug delete plan related with risk ([0162de1](https://github.com/boring91/injaz/commit/0162de1f66e237395f2744e0e158ee46add5ab83))
-   **backend:** fix delete plans related with risk ([06107bb](https://github.com/boring91/injaz/commit/06107bb7bbca3ce733f9d5bf088d5813bc58637d))
-   **backend:** fix duplicated translate ([2ec998d](https://github.com/boring91/injaz/commit/2ec998d0312e88ef81380e53cfe94a5fc9bec70c))
-   **backend:** fix translate ([2fdbdbf](https://github.com/boring91/injaz/commit/2fdbdbf9f3f2483b0a4bc075e6458ee342cb361b))
-   **backend:** fix translate ([946174e](https://github.com/boring91/injaz/commit/946174e0e52ab4cd5c6b546b1f888e850cb22808))
-   **backend:** fix translate for excel ([7cc44a4](https://github.com/boring91/injaz/commit/7cc44a489856cd2fb96616e91f2398776d018ce4))
-   **frontend:** fix permission owner with disable or enable some fields ([4a4e52b](https://github.com/boring91/injaz/commit/4a4e52b6c3d08249e6ce7f6eda15183c2972fcf3))
-   **frontend:** fix permission with disable some fields ([e2feabf](https://github.com/boring91/injaz/commit/e2feabfc4fea73bbad26e66433c30d8b1f7fc616))
-   **frontend:** fix permission with disable some fields ([991ed74](https://github.com/boring91/injaz/commit/991ed7434f6d1a5a8b82b925beb7813e7a347b33))
-   **frontend:** fix rename word full access ([eb5a560](https://github.com/boring91/injaz/commit/eb5a560752a881b37c87571d5f51b57bc5ae3da8))
-   **frontend:** fix word full access ([018d133](https://github.com/boring91/injaz/commit/018d133dc14dce116019c1745f7c705e0255a55c))
-   **frontend:** modify permission disable or enable some fields ([6a4bf29](https://github.com/boring91/injaz/commit/6a4bf29b7e1c9fd720942dd8f718c7548a858eeb))
-   **frontend:** modify permission kpi periods ([2f88aed](https://github.com/boring91/injaz/commit/2f88aed684118b8bc814900cb6d49ed2753bf8fb))
-   **frontend:** modify permission kpi periods ([d395c09](https://github.com/boring91/injaz/commit/d395c09cdbad57c97c9089980246ec0d6dea3284))
-   **frontend:** modify permission kpi periods on normal user ([6217ebf](https://github.com/boring91/injaz/commit/6217ebfa90c3abfb8b85ef3d10fa60a5e89119f9))
-   **front:** fixed plan-task filter translation issue ([f88c82b](https://github.com/boring91/injaz/commit/f88c82b7ab51ed67c182b27aaee1c5ec832492d9))
-   **front:** fixed redirection after edit or adding new plan-task ([dbf4c54](https://github.com/boring91/injaz/commit/dbf4c5421af2fdeece62b5093292ddc53f51a851))
-   **front:** fixed showing standards and kpi-evaluation scores for the period ([7bed098](https://github.com/boring91/injaz/commit/7bed0989f01de21b1b7bc1e9ee0d46371095f9f9))
-   **front:** restored page size per DR Moh Requested ([d9df8bc](https://github.com/boring91/injaz/commit/d9df8bc2d26291c5da44c1e002a727d5e04c6bcc))

### Features

-   **backend:** add another translate for excel ([185b4c1](https://github.com/boring91/injaz/commit/185b4c1cdf92d6266f559a455ec39c00e143c01a))
-   **backend:** add new endpoint duplicate for evaluation ([388a184](https://github.com/boring91/injaz/commit/388a184120f312ad761a6ba7c8554d6b3a8d15bd))
-   **backend:** add permissions screens for excel ([c4fff0a](https://github.com/boring91/injaz/commit/c4fff0afeaf9314d633f592ada31328dfd541680))
-   **backend:** allow users to access statistical reports with their hierarchy ([5d432e7](https://github.com/boring91/injaz/commit/5d432e7a1dd42df012e8b0053621ee285ba60bc1))
-   **front:** added duplicate button in evaluation table ([d5098bd](https://github.com/boring91/injaz/commit/d5098bd53440ba6e1b532b9d04180759605f76e1))

# [1.128.0](https://github.com/boring91/injaz/compare/v1.127.3...v1.128.0) (2025-01-21)

### Bug Fixes

-   **backend:** fix permission for admin in risks ([40ed009](https://github.com/boring91/injaz/commit/40ed009143310fe1debd94daea6ec1ce5146c5e8))

### Features

-   **backend:** fix permission department in plans ([835eebf](https://github.com/boring91/injaz/commit/835eebf1a0104d47c395fb969fe61c5150879b33))
-   **backend:** fix translate ([9e8aa09](https://github.com/boring91/injaz/commit/9e8aa098c304b8297e4d12e1ec75392afc265e9d))
-   **backend:** formate code and delete comments ([d2e3892](https://github.com/boring91/injaz/commit/d2e38925f21faa36fa9388ad7b272517e3481d77))
-   **backend:** plan tasks and plan subtasks with no assigned department ([eb38d64](https://github.com/boring91/injaz/commit/eb38d648fd81d8c12f0a9811ed7358267efcd82b))
-   **backend:** remove assignment check ([1824157](https://github.com/boring91/injaz/commit/182415793cebf1b077acd36064462efae5044a90))
-   **backend:** remove comments and format code ([1d292c4](https://github.com/boring91/injaz/commit/1d292c4082d5da19f02220dd34f00a5900066cc1))
-   **backend:** remove the created user from seeing the plan ([915658b](https://github.com/boring91/injaz/commit/915658bcf1af2839e62ea992fd3ceef5b6e29d09))

## [1.127.3](https://github.com/boring91/injaz/compare/v1.127.2...v1.127.3) (2025-01-20)

### Bug Fixes

-   **frontend:** fix translate app ar ([74daf9d](https://github.com/boring91/injaz/commit/74daf9d65ccad3e345a7794e03f01429817a3f54))
-   **front:** fixed label naming issue in multiple page ([56d5181](https://github.com/boring91/injaz/commit/56d5181fd1912e006586a89676b3f7cc06278f47))
-   **front:** fixed naming issue in risk and department report ([4881004](https://github.com/boring91/injaz/commit/48810046e474bdf2f3ac0760a74519af408305a4))
-   **front:** fixed risks management list translation ([41703b4](https://github.com/boring91/injaz/commit/41703b42dca9bbc4fa8596759d4e6b92cea889cf))
-   **front:** fixed title issue in evaluation , department-report , risk list ([a3ccd0e](https://github.com/boring91/injaz/commit/a3ccd0e2a7efa4492ca3eda360faa7d718dcf51f))
-   **front:** fixed translation and naming issue in risks and kpi-evaluation ([79b9973](https://github.com/boring91/injaz/commit/79b997300e0d31f699e9756354bb05268f37e3d9))

## [1.127.2](https://github.com/boring91/injaz/compare/v1.127.1...v1.127.2) (2025-01-20)

### Bug Fixes

-   **backend-frontend:** add parent for results from backend ([8ce85da](https://github.com/boring91/injaz/commit/8ce85da46a4148a60e97ea3c8f3c7085c96f8594))
-   **backend:** fix parent if null from backend ([72704de](https://github.com/boring91/injaz/commit/72704de4447a6db9d66ec4c6271f1b0239b6d5c4))
-   **frontend:** add parent in StatisticalReportCategoryResult ([2aefda1](https://github.com/boring91/injaz/commit/2aefda1bd62a3232edbdef4b13a33b845b84a6d6))
-   **frontend:** add round for totalPeriodsEvaluationScore ([c44235d](https://github.com/boring91/injaz/commit/c44235dd4618e40a317cb3685212975061bd4623))
-   **frontend:** fix category filter bug ([41ff87a](https://github.com/boring91/injaz/commit/41ff87abbe3b1b9f6a548189fdc7460b7ba974cc))
-   **frontend:** fix category filter bug ([ca7f014](https://github.com/boring91/injaz/commit/ca7f014681627921ea2ad86c42e76106f50f741a))
-   **frontend:** fix measuringDepartment in kpi detail ([f92d26c](https://github.com/boring91/injaz/commit/f92d26c6942a3f4d40440e65e069129d44b5b0e7))

## [1.127.1](https://github.com/boring91/injaz/compare/v1.127.0...v1.127.1) (2025-01-17)

### Bug Fixes

-   **front:** fixed active child state for nav items in sidebar menu ([0907bed](https://github.com/boring91/injaz/commit/0907bed690e757d4ef06891ab1b9a2b4bd75cfe8))

# [1.127.0](https://github.com/boring91/injaz/compare/v1.126.0...v1.127.0) (2025-01-16)

### Bug Fixes

-   **backend:** add PartnerShipSetting ([49ac043](https://github.com/boring91/injaz/commit/49ac04309594c9431971b7ff90c7137b309802f9))
-   **backend:** change IsEnabled false ([8a4d2d7](https://github.com/boring91/injaz/commit/8a4d2d787dbd0b11053d16e7d18a0f1c79b73748))
-   **backend:** change PartnerShip to Partnership ([ded719d](https://github.com/boring91/injaz/commit/ded719ddee4d00168e084b095dc908c51fd9850b))
-   **backend:** modify excel ([945d9f9](https://github.com/boring91/injaz/commit/945d9f99c65c4a19a41dfb1a4bdec16ae2448997))
-   **backend:** remove duplicated translate ([07321a5](https://github.com/boring91/injaz/commit/07321a53eedda57d9d105391e82d719b32b77993))
-   **backend:** work on export excel ([619cd85](https://github.com/boring91/injaz/commit/619cd85a427072901533b2aa35618ad83f40ee5b))
-   **departments:** proper data fetching needed for exporting departments to excel file ([30bccda](https://github.com/boring91/injaz/commit/30bccda9fa18bc275e39dacce7c1a6d6076e6af4))
-   **front:** added partnership tab title ([0dfec1e](https://github.com/boring91/injaz/commit/0dfec1e921e1c8fb2b681859861b453659cc970f))
-   **front:** added partnership translation in app settings ([1d30b83](https://github.com/boring91/injaz/commit/1d30b83d7baddc7070c9a155209d026eaebcc381))
-   **frontend:** fix edit permission in kpi result ([30f9303](https://github.com/boring91/injaz/commit/30f9303504866207f64049f7cbc612d58697f090))
-   **frontend:** fix edit permission in kpi results with disable some fields ([2685b58](https://github.com/boring91/injaz/commit/2685b58ab3f027cef3b9f0f8abfcb8b34413408e))
-   **frontend:** fix edit permission with disable some fields ([32b4b8e](https://github.com/boring91/injaz/commit/32b4b8e5516f41d7337665462ba3f605fa9b1e21))
-   **frontend:** modify permission disable or enable some fields ([ebc7a38](https://github.com/boring91/injaz/commit/ebc7a38363e6470cf11ec5f4292b7c36d49e8675))
-   **front:** fixed expand and collapsing issue ([7b6d686](https://github.com/boring91/injaz/commit/7b6d686247b8678475df6736d4b43f86b7b7f0f4))
-   **front:** fixed hide partnership fields to be from settings ([6520b50](https://github.com/boring91/injaz/commit/6520b50b1e9d73885aeee2719e5220c265d212eb))
-   **front:** fixed no result message in tree select component ([0945069](https://github.com/boring91/injaz/commit/0945069c7f65359204fc4c8d930dcb8c2d7a071d))
-   **front:** fixed search issue in the tree select component ([029c0e5](https://github.com/boring91/injaz/commit/029c0e5cbc7d460921f6e8e2332830c4f302af86))
-   **front:** fixed search issue in tree select component ([f6ef4c5](https://github.com/boring91/injaz/commit/f6ef4c5b536d4986b538e5bd5e08802eef42294b))
-   **front:** fixed sub nodes issue in the tree select ([bb9ba01](https://github.com/boring91/injaz/commit/bb9ba01fe4862dc60b7362e2de6f504eb5feb838))
-   **front:** modified property name as backend changes ([e154fbd](https://github.com/boring91/injaz/commit/e154fbd56841b51826b280d8214fbf98b4214349))
-   **front:** removed un-used code from tree select component ([493347e](https://github.com/boring91/injaz/commit/493347ece16bd619d692c51ca20df01b13d56135))
-   **front:** renamed endpoint in partnershipService in system settings ([ab356b3](https://github.com/boring91/injaz/commit/ab356b3b6e40d5fa0dfbe3b860d364c9e49cb264))

### Features

-   **backend:** export user permission ([3738050](https://github.com/boring91/injaz/commit/37380501e60db9b2563176cb4fc9ca25a0b26492))
-   **backend:** export user permission ([9a2c610](https://github.com/boring91/injaz/commit/9a2c6100107558c64e9d30823a789d1a1b220193))
-   **backend:** export user permission translation ([7ec6462](https://github.com/boring91/injaz/commit/7ec6462883502bb964120ee0fc6bb4991206f08f))

# [1.126.0](https://github.com/boring91/injaz/compare/v1.125.0...v1.126.0) (2025-01-13)

### Bug Fixes

-   **frontend:** fix route ([54a5c17](https://github.com/boring91/injaz/commit/54a5c175d1c22e52b1357c7f8bf33a29dfc9a0a7))
-   **front:** fixed kpi report table issue ([ffbdf09](https://github.com/boring91/injaz/commit/ffbdf0923a13fe825f8e2df9fa529c3ba073d18e))

### Features

-   **backend:** formate ([01a9098](https://github.com/boring91/injaz/commit/01a9098e6f82e4196ba9c1f84d6e48b9d878843d))
-   **backend:** make the mapping inside the mapping strategy ([56708c4](https://github.com/boring91/injaz/commit/56708c4126fcc3c4d21b7750387067a31743aef9))
-   **backend:** modify total count of statistical report statistics to go with user permissions ([750f71d](https://github.com/boring91/injaz/commit/750f71d7d35703100b51bcc897953f3a91334822))
-   **backend:** remove comments ([d555506](https://github.com/boring91/injaz/commit/d5555063e7beaf81ebd6e311c214362e607493eb))
-   **backend:** remove comments ([ad12913](https://github.com/boring91/injaz/commit/ad129133a2a8c2827478c954322baba4854e8b50))
-   **backend:** remove duplication in translation ([d903cf5](https://github.com/boring91/injaz/commit/d903cf56a6f347d6f69d0336db64513e9d1a2528))
-   **backend:** statistical report statistics per user department ([517bfbc](https://github.com/boring91/injaz/commit/517bfbc6113ab07034aaeb9dfe1a2fa043ba4459))

# [1.125.0](https://github.com/boring91/injaz/compare/v1.124.0...v1.125.0) (2025-01-09)

### Bug Fixes

-   **backend-frontend:** merge conflicts ([ab337ff](https://github.com/boring91/injaz/commit/ab337fffffaf93d85a39d3989311c92c3f209575))
-   **backend:** added missing translations ([c677585](https://github.com/boring91/injaz/commit/c67758588841cc8f3c2fa1e738bb5ac6bedaa49a))
-   **backend:** fix filter status ([315be92](https://github.com/boring91/injaz/commit/315be92c05db14b847ba9ffc625a6f0fd7c6013c))
-   **backend:** fix formate ([e6be1bb](https://github.com/boring91/injaz/commit/e6be1bb4387dcc5a0c40b12e756e47ac975a954f))
-   **backend:** fix translate ([52a00db](https://github.com/boring91/injaz/commit/52a00db9fae96b2a71ed1cb9c5e0ea5ca9c97c98))
-   **backend:** pageNumber and pageSize should be at the end ([f7f8664](https://github.com/boring91/injaz/commit/f7f8664cbee4e6913228ed6a34d12569914e842d))
-   **frontend-backend:** fix partners with evaluations statistics ([1567bf0](https://github.com/boring91/injaz/commit/1567bf09dbee5dd50a467dcbdc5d9db40846eca8))
-   **frontend:** fix category filter ([1bd9c52](https://github.com/boring91/injaz/commit/1bd9c522d8a848329691fc2680497f83af7487b6))
-   **frontend:** fix merge translate ([83976aa](https://github.com/boring91/injaz/commit/83976aa04bfeda7c7d55c3dad9434bbe65676668))
-   **frontend:** fix notes ([a211c73](https://github.com/boring91/injaz/commit/a211c73d37fe6321d6a7644f0ab03d780870d4bf))
-   **frontend:** remove label of chart ([6beed2b](https://github.com/boring91/injaz/commit/6beed2bf68cf2b3746c553a385cf98a4f2e3638a))
-   **frontend:** work on chart ([01968eb](https://github.com/boring91/injaz/commit/01968ebe1fc4dc9f7afa4f60005b9203c17caad6))
-   **frontend:** work on chart ([2b8f5ce](https://github.com/boring91/injaz/commit/2b8f5ce6e658121c89498ca6bc5b7b00ee1f38d0))
-   **frontend:** work on chart ([13198d1](https://github.com/boring91/injaz/commit/13198d110f3a44e6face1b0bdfeb364a5e0330c6))
-   **frontend:** work on chart ([3a394e4](https://github.com/boring91/injaz/commit/3a394e436f046890016a9314285dbc7bb67d5997))
-   **frontend:** work on chart ([e2af297](https://github.com/boring91/injaz/commit/e2af29732d52c7d045d35aac945b3b0a57d8c707))
-   **frontend:** work on filter category chart ([7618702](https://github.com/boring91/injaz/commit/761870236ec4c2d5d9c34a502e3c43cd17324e2f))
-   **frontend:** work on filter category chart ([326b30d](https://github.com/boring91/injaz/commit/326b30d06e5134e85d4921a9f50edb92afc3761d))
-   **frontend:** work on filter category chart ([0fb61d8](https://github.com/boring91/injaz/commit/0fb61d8ee29b9501ac4ad949a03b25abd0239fee))
-   **frontend:** work on label chart ([350c638](https://github.com/boring91/injaz/commit/350c638fa421e74561dff6ea9633844154a4ada3))
-   **frontend:** work on partner evaluations ([24a0771](https://github.com/boring91/injaz/commit/24a0771711ecaa33d201a167a2596705a222b940))
-   **frontend:** work on total chart ([f371bec](https://github.com/boring91/injaz/commit/f371becdc163088782984d9be5bd9765515d5dc5))
-   **front:** fix formatter issue ([cb0a2b2](https://github.com/boring91/injaz/commit/cb0a2b24670e0b1f8c916814d0ad4bc282031590))
-   **front:** fixed all comments issue on pull request for front end ([3376491](https://github.com/boring91/injaz/commit/3376491b7bda8ff45b128d5e90c5e8e62a6d74e0))
-   **front:** fixed charts calculation issue in statistical-report details ([948f9bd](https://github.com/boring91/injaz/commit/948f9bdc22bb77fd6f72636975a95685d7713895))
-   **front:** fixed console warning issue and added new filter in statistical-report chart component ([12a08d1](https://github.com/boring91/injaz/commit/12a08d1cbefa824949cde3ed36d62584dae39b7e))
-   **front:** fixed the second charts issue in statistical-report detail component ([eeb1a93](https://github.com/boring91/injaz/commit/eeb1a93525034667625086d8804d8f0113c43ba1))
-   **front:** removed un-needed code ([53295ea](https://github.com/boring91/injaz/commit/53295eaf0456bb0181b3f73a73b975e5c732c78c))
-   **operation:** account for nullable code ([f2b2cd6](https://github.com/boring91/injaz/commit/f2b2cd6a048c5a9def90b8e2cab0e88913acb398))
-   **operation:** fixed code getting reset when updating through requests ([24baeff](https://github.com/boring91/injaz/commit/24baeffde3c4ace2d0dbd7e3fd84290dc50d0365))
-   **operation:** preserve data from update request when rejected during edit ([ea206c6](https://github.com/boring91/injaz/commit/ea206c63b3bd1245973b89b79402b7f25e9942f0))
-   **operations:** sort update requests descending by creation time ([ee279c6](https://github.com/boring91/injaz/commit/ee279c67f654808e6e37d2a57d7329247ea656a5))
-   **partner:** fix the git conflicts ([b7f34ed](https://github.com/boring91/injaz/commit/b7f34edb5bdd98bc6ba0cd0f6887cacb5ff57dc7))

### Features

-   **backend-front-alert-message:** add pop alert message when change date ([47c44ce](https://github.com/boring91/injaz/commit/47c44ceb79b1f17f856c99c8acf64cb6a2658b30))
-   **backend-front:** modify translate , merge and init subtask fetchers ([f1f243d](https://github.com/boring91/injaz/commit/f1f243d04c15486197b573af55936cd175c041dc))
-   **backend:** add error message ([963f31c](https://github.com/boring91/injaz/commit/963f31cdc01ee13737350b4a80e6d00bf5965456))
-   **backend:** add error message if department has any linked resources ([f72aa14](https://github.com/boring91/injaz/commit/f72aa143690ec5f794f162a0b7b13e65f42fa2a9))
-   **backend:** add filters and statistics to partner ([317bfad](https://github.com/boring91/injaz/commit/317bfadaf8f07cac6609658b979c90093146e164))
-   **backend:** add formate and delete comments ([720db61](https://github.com/boring91/injaz/commit/720db61a3d1a54044901480e4d2d986fecf7fda0))
-   **backend:** merge conflicts ([32b4526](https://github.com/boring91/injaz/commit/32b4526b857d8b71ae042ed6c7d3a843b9ec6f33))
-   **backend:** modify translate , and remove init subtask fetchers ([65a51a7](https://github.com/boring91/injaz/commit/65a51a7a591d6844dad293ef86685e9bc02f73c8))
-   **front-partner:** enhanced filters in partners dashboard ([14b732d](https://github.com/boring91/injaz/commit/14b732d00d4a96ac7a9d13f1d9fa0794c55439c4))
-   **front:** change alert message to be in html ([dd0fdf4](https://github.com/boring91/injaz/commit/dd0fdf42f4f08782b60858536a4a3f4f5b34a53b))
-   **frontend-backend:** change notes in statistics partnership contract ([ddaecca](https://github.com/boring91/injaz/commit/ddaecca332770504695f38f2ba7da1ea313749ee))
-   **frontend:** add filters and statistics to partner ([9eced93](https://github.com/boring91/injaz/commit/9eced93359f05620c68894a847de6c2927e1d226))
-   **frontend:** added show label input to flow move button component ([29961d7](https://github.com/boring91/injaz/commit/29961d77ee905c825c33dd2fa876a6e506ef6a10))
-   **frontend:** change translate ([472e582](https://github.com/boring91/injaz/commit/472e5820d913d296de3727d0ce983148d3876e16))
-   **front:** fix bugs datesHaveChanged ([7682ff3](https://github.com/boring91/injaz/commit/7682ff3b7ad19bb6664a38850c8306fd436dee68))
-   **operation:** added ability to submit update requests from the frontend ([f8f1cd9](https://github.com/boring91/injaz/commit/f8f1cd92cd0799ad0dd8bc388da04567cae6f0d3))
-   **operation:** added endpoints for update request crud ([b74ff44](https://github.com/boring91/injaz/commit/b74ff44ba5168ad5b0c7df8a3e073c74c00824dc))
-   **operation:** added flow available actions to dtos + streamlined checks for pending requests ([194100a](https://github.com/boring91/injaz/commit/194100aa2f2659d3e0da9531f0d746d917aeeffc))
-   **operation:** added update request entities ([88e3d1b](https://github.com/boring91/injaz/commit/88e3d1bf5bc8e49e3fd3dc6bb704c217f0fb9ccb))
-   **operation:** added update request entities ([1815662](https://github.com/boring91/injaz/commit/18156624c9b16e5fde94763e326a71ac582ef04e))
-   **operations:** added ability to resubmit update request when rejected ([248f875](https://github.com/boring91/injaz/commit/248f875520c9bb26c100ca8db95675b98010de1d))
-   **operations:** added list and detail pages for update requests ([fa4abaa](https://github.com/boring91/injaz/commit/fa4abaa7f5b098f1c6867918e6545c3b51c9e4e5))

# [1.124.0](https://github.com/boring91/injaz/compare/v1.123.3...v1.124.0) (2025-01-07)

### Bug Fixes

-   **backend-library:** add new linked resources to show in library file ([6a00ab5](https://github.com/boring91/injaz/commit/6a00ab54e9228aa440291adf633f53dd4a480232))
-   **frontend-backend:** fix notes and merge ([e24dc16](https://github.com/boring91/injaz/commit/e24dc16142891760be7606aaf1c15cd2d3edfca2))
-   **frontend:** fix error message ([d2a3293](https://github.com/boring91/injaz/commit/d2a3293d6bc3fc74f164550493588ae5d7d79a9d))
-   **frontend:** fix merge conflict ([3f06fd3](https://github.com/boring91/injaz/commit/3f06fd32da0716287229e734d789fe3ee95ed714))
-   **front:** fixed kpi loader issue in data entry request ([b2a7f24](https://github.com/boring91/injaz/commit/b2a7f24ec54c1ef3209b431c20d8c948394ed9a3))
-   **front:** removed no kpi alert on submission ([d37a561](https://github.com/boring91/injaz/commit/d37a5610a188005b88810c9234676b8ad315c61c))

### Features

-   **backend-front-file-library-not-found:** fix bugs and merge in translate ([b56bf52](https://github.com/boring91/injaz/commit/b56bf527f136b137b22d02d0f668eb24491d90c7))
-   **backend-front:** remove translate errorMessageFile ([7c07df9](https://github.com/boring91/injaz/commit/7c07df9e97f5e6e05b358bf2155b583f23998fb6))
-   **backend-library:** add new endpoint for replace resources with new file attached ([2e5848f](https://github.com/boring91/injaz/commit/2e5848fe37ab83c91c1fe35103ed1c3aec144fb4))
-   **backend-library:** fix some notes ([3565b60](https://github.com/boring91/injaz/commit/3565b60f2113f13cc8452bcf9c703b1008040302))
-   **backend-library:** modify endpoint for replace resources with new file attached ([26349f5](https://github.com/boring91/injaz/commit/26349f5507de5d66727eb23bb1c6036a3248939c))
-   **backend:** add translate ([d2b61d5](https://github.com/boring91/injaz/commit/d2b61d5398641fc92a08e92a5cefb25693fc604a))
-   **backend:** file not found message ([eca67c8](https://github.com/boring91/injaz/commit/eca67c818e61c265a70d2229973d3366aa4733fd))
-   **file-not-found-message:** fix the conflicts ([69e4976](https://github.com/boring91/injaz/commit/69e4976c3b0bb416bb84f47adc797c3cda25ae38))
-   **frontend-library:** add api call to replace the files before deleting ([690ffb9](https://github.com/boring91/injaz/commit/690ffb9670c057225f10610fae439aa64606fdd5))
-   **frontend-library:** add new action to replace the files ([84e1e25](https://github.com/boring91/injaz/commit/84e1e25f078d1355c3aa5a67aee1e4befcb99156))
-   **frontend-replace-file-library:** change icon for replace files ([55b6601](https://github.com/boring91/injaz/commit/55b660182abe4227880c3529f4e6836fabfe480c))
-   **frontend:** add error message ([a3ce963](https://github.com/boring91/injaz/commit/a3ce9639264d1cb7cc525b6506a5d9d72d679d2f))
-   **frontend:** add error message ([fee406d](https://github.com/boring91/injaz/commit/fee406dd5b9b144389c1302ada6d61b3dd401c7d))
-   **frontend:** update "mnm-webapp": "^3.8.2" to "mnm-webapp": "^3.8.5" ([650610a](https://github.com/boring91/injaz/commit/650610a6bff8c374c9328b7eaef1e5ad78e308fa))
-   **front:** fix comments or notes ([4ff792c](https://github.com/boring91/injaz/commit/4ff792ccb2662699f04c313679df5332cc191176))
-   **front:** fix format ([e4480e7](https://github.com/boring91/injaz/commit/e4480e76bd42656216688fcd80dbd93e2ce7f04f))
-   **front:** fix format ([ebdba5e](https://github.com/boring91/injaz/commit/ebdba5e483726a4786df089aee44976e1286f98c))
-   removing the changes of the package lock file ([3f80c04](https://github.com/boring91/injaz/commit/3f80c043bde267db2288ec657b6468baec324d54))

## [1.123.3](https://github.com/boring91/injaz/compare/v1.123.2...v1.123.3) (2024-12-26)

### Bug Fixes

-   **front:** fixed date issue as Dr mohamed requested ([f38cb42](https://github.com/boring91/injaz/commit/f38cb424f457e36ea4372b833c01bf87ce61b740))
-   **front:** fixed date issue as Dr mohamed wanted ([0aca938](https://github.com/boring91/injaz/commit/0aca938bff65a4e88badd9ced0e387d94ab7b3b7))

## [1.123.2](https://github.com/boring91/injaz/compare/v1.123.1...v1.123.2) (2024-12-24)

### Bug Fixes

-   **plans:** period overlap check issue ([36f7166](https://github.com/boring91/injaz/commit/36f7166d0f60dfa06d09d01d30f233ae99772788))

## [1.123.1](https://github.com/boring91/injaz/compare/v1.123.0...v1.123.1) (2024-12-24)

### Bug Fixes

-   **plans:** disable plan entity parent time coverage ([4fc7a47](https://github.com/boring91/injaz/commit/4fc7a4749e50c85d3bd1f2ea4b2f28c45322e8e6))

# [1.123.0](https://github.com/boring91/injaz/compare/v1.122.0...v1.123.0) (2024-12-23)

### Bug Fixes

-   **backend-front:** change route statistical report ([b10dcdb](https://github.com/boring91/injaz/commit/b10dcdb16266cd6278256b2a26c7348f41f90348))
-   **backend:** add [BindBodySingleJson] to endpoint Finalize ([468bad0](https://github.com/boring91/injaz/commit/468bad028126018fe75ae1d00f12ff5a20a90e37))
-   **backend:** change list to array for guid ids ([b6f800a](https://github.com/boring91/injaz/commit/b6f800aa6ba91997a8c4e9703a4c2a3af013ffca))
-   **backend:** fix notification ([1794d71](https://github.com/boring91/injaz/commit/1794d71dd55a26a5caa0088a926799735d842153))
-   **backend:** fix translate ([f5dc6ed](https://github.com/boring91/injaz/commit/f5dc6ed943b56e45d622d75417592d3b41b6361c))
-   **backend:** modify condition for Subtasks and sub subtasks( start - end date ) ([326da80](https://github.com/boring91/injaz/commit/326da803271739c0dfe90c902502c35555883163))
-   **backend:** stop the hosted services in(KpiResultDataEntryResponseModule)createdBy omar ([fb47b04](https://github.com/boring91/injaz/commit/fb47b0409af5d7c4899f781d8f4396f81668dd9f))
-   **frontend:** merge conflict ([b780755](https://github.com/boring91/injaz/commit/b7807555e15d5d5f07e038184d49bf4759a3881c))
-   **front:** enhanced code as required ([dc669d0](https://github.com/boring91/injaz/commit/dc669d0d6b4d0730cc386dab789a411260b1cc9d))
-   **front:** fix date to utc format issue ([fa276f4](https://github.com/boring91/injaz/commit/fa276f4d8fec4123edf4867dda7b6701c3a21b3d))
-   **front:** fix format date ([45bb361](https://github.com/boring91/injaz/commit/45bb3614d517aaf2e32a320421f336885dd0c818))
-   **front:** fixed active state issue in sidebar ([4a5cb88](https://github.com/boring91/injaz/commit/4a5cb88a77777a553fa361d11e9d316adc3f063c))
-   **front:** fixed date issue in plan task component ([03307d2](https://github.com/boring91/injaz/commit/03307d2097cbf84bd16cffef01b35605225c4e8f))
-   **front:** fixed date picker value issue ([48e5f2b](https://github.com/boring91/injaz/commit/48e5f2b08c18ee721c03115b7058f56c73163636))
-   **front:** fixed end date issue in new plan subtask ([0b4dcb1](https://github.com/boring91/injaz/commit/0b4dcb166555a1a5c791e7aa0191cf45acbd92e2))
-   **front:** fixed multi approve request issue ([7fdcfd3](https://github.com/boring91/injaz/commit/7fdcfd356f1513f375382cb590e76a455a77e075))
-   **front:** fixed no kpis issue by adding alert also in submission ([ab74d2c](https://github.com/boring91/injaz/commit/ab74d2c79d813ab69ecaaf8de403e727208b726a))
-   **front:** fixed start and end date issue in plan-task component ([8cd34d9](https://github.com/boring91/injaz/commit/8cd34d939bd7eaa0fe80f7a87c7c15e6c1db3d38))
-   **front:** fixed start date and end date issue in plan-task ([8eeaeb8](https://github.com/boring91/injaz/commit/8eeaeb893231b73d67c77c8f699e150d5b5e1dee))
-   **front:** fixed start date issue in new plan subtask form ([14e3404](https://github.com/boring91/injaz/commit/14e3404fdba273109867220f14fb9bdd1cff3c1a))
-   **front:** removed selected from Plan Sub subtask model and updated the component ([7f886b0](https://github.com/boring91/injaz/commit/7f886b02d4dee9ebd515e5659119fdb385a547c3))
-   **front:** updated mnm-webapp version ([8bfcab8](https://github.com/boring91/injaz/commit/8bfcab8ed683bd5507a1adc3d4e9941b053c1e53))

### Features

-   **backend:** change finalize endpoint ([9765dcf](https://github.com/boring91/injaz/commit/9765dcf327875bef5abc93c90c2b95b54fc43d03))
-   **backend:** change finalize endpoint to received list of ids ([8828332](https://github.com/boring91/injaz/commit/88283327cb6dd0b7924ec7e70c2a4ff3cd4a72fd))
-   **backend:** check file mandatory before deleted ([32dfa35](https://github.com/boring91/injaz/commit/32dfa35e7c5d175ca13983a7f927dadb37a8f3ad))
-   **backend:** edit default flow service ([897a439](https://github.com/boring91/injaz/commit/897a439d50dd93d11c73a0bde88661be1cfb5722))
-   **backend:** merge conflicts ([2e3b65a](https://github.com/boring91/injaz/commit/2e3b65a6352726bede2c225e1fc7dda24ef1c53e))
-   **backend:** merge conflicts ([ce4eef4](https://github.com/boring91/injaz/commit/ce4eef4d19f17e608b848374d70317f0205dc123))
-   **backend:** plan flow notification ([3d348ce](https://github.com/boring91/injaz/commit/3d348ce97f9866c2cb92cd8a9417301727cd1a34))
-   **backend:** send notification in rejected final flow state ([f5afc0c](https://github.com/boring91/injaz/commit/f5afc0cf49408bd403113a5247c71eeb9753f4c0))
-   **backend:** send notification when final approval is done ([de417a6](https://github.com/boring91/injaz/commit/de417a6f727267dff235629262f9a3f1fe077daf))
-   **front:** added ability for multi approve in subsubtask-approval component ([fcecf4a](https://github.com/boring91/injaz/commit/fcecf4a626e1ef8b0484f91ed3d8ec0ce18f61ba))
-   **front:** added checkbox to sub subtask approval table and global approval button ([bb73461](https://github.com/boring91/injaz/commit/bb73461752d1a4fbc5ff3deafd4eb8fe65ae437c))
-   **front:** added multi approve in sub subtask approval finalize component ([4087591](https://github.com/boring91/injaz/commit/4087591d479edc756ebd6a4974a104a0a5d9d795))
-   **frontend-backend:** adding a checkbox for attachments is required or not ([39ddb00](https://github.com/boring91/injaz/commit/39ddb00f9001bc9dc7eb48a1bc33695da8064f34))
-   **frontend-backend:** make merge with develop ([cf9ecc7](https://github.com/boring91/injaz/commit/cf9ecc76b97a775f53fd10afd9330eafeb324763))
-   **frontend-backend:** rename function properties and names ([e2bd6b1](https://github.com/boring91/injaz/commit/e2bd6b15811a71f8bc6b87fa3e6e784592a96f82))
-   **frontend:** change == to === ([5919f7f](https://github.com/boring91/injaz/commit/5919f7f0d136309ba771021ed3ebf638077f40e8))
-   **frontend:** change condition ([10d305e](https://github.com/boring91/injaz/commit/10d305e84b2a3f45b9843c3f850682eece0f5867))
-   **frontend:** mandatory field attachment reports ([b795811](https://github.com/boring91/injaz/commit/b7958118b58a4ce5676b368045554e5a18f0d3dd))
-   **frontend:** remove console ([1d433b8](https://github.com/boring91/injaz/commit/1d433b84f1c70d2dc2a25389b1477f2a051827f8))
-   **frontend:** remove underscore '\_' ([8941eb6](https://github.com/boring91/injaz/commit/8941eb6280603797599b0e656f67386c1e83b54f))

# [1.122.0](https://github.com/boring91/injaz/compare/v1.121.0...v1.122.0) (2024-12-19)

### Bug Fixes

-   **backend:** fix conflicts ([474b72a](https://github.com/boring91/injaz/commit/474b72a524b79e5386a203b501451ea45d9d4372))
-   **backend:** fix translation error ([9b9996c](https://github.com/boring91/injaz/commit/9b9996cb5d8a46dc32d2d218034a0476c98575a5))
-   **back:** remove new top item ([9f5fe11](https://github.com/boring91/injaz/commit/9f5fe1155395fa280f1acd7467379be6a1866471))
-   **front-plan-internal:** change internalDepartment to partneringDepartment ([e0ba053](https://github.com/boring91/injaz/commit/e0ba0534413bab57e9a503eb2a82b786da9a18df))
-   **front:** changed colors and percentage in department report result ([7e28db6](https://github.com/boring91/injaz/commit/7e28db6557ee3f8b57221a75a1958e9d65d42591))
-   **frontend-backend:** add dashboard top item ([726c95e](https://github.com/boring91/injaz/commit/726c95e8d744b9155c4e39ceb7afa9d41242cc96))
-   **frontend-backend:** fix names and dashboard top item ([6cf5ea9](https://github.com/boring91/injaz/commit/6cf5ea90b3d3b191293656ec45ff8fb522aa661a))
-   **frontend-operation:** adjusting the label in case level one or two ([ed86778](https://github.com/boring91/injaz/commit/ed86778bbef17d82b62285ad5e03f0f3f9b1aabc))
-   **frontend-plan:** adjust the translation ([f8a587e](https://github.com/boring91/injaz/commit/f8a587e99deae45282ef38d6846bd0e8d20af16c))
-   **frontend:** change measurement to measuring ([0598d14](https://github.com/boring91/injaz/commit/0598d14d914e78f086e5899f2033fb754e9661a0))
-   **frontend:** fix conflict ([62e52f7](https://github.com/boring91/injaz/commit/62e52f7d86dda6d6e5a39392ec0f8573c42530d1))
-   **frontend:** fix conflict ([13e89a4](https://github.com/boring91/injaz/commit/13e89a476a8f882cd7b834e61319a9c164f1da37))
-   **frontend:** fix frontend notes ([4e306fb](https://github.com/boring91/injaz/commit/4e306fb9604444962a055ebb70002c46722f8b38))
-   **frontend:** tree select placeholder type ([b90daa1](https://github.com/boring91/injaz/commit/b90daa11f3898fd2af425a76e9af0aefacb60940))
-   **front:** fixed pull request issue in partner ([e7aad76](https://github.com/boring91/injaz/commit/e7aad76477e3c61b11c95da679f8be4dfec1c638))
-   **front:** partnership contract hide details fields ([7f1fd7f](https://github.com/boring91/injaz/commit/7f1fd7f6adc093943d14c76ab60ced79ff8beacf))
-   **front:** partnership contract pull request issue ([0c9dc3f](https://github.com/boring91/injaz/commit/0c9dc3f5bbaf4244ef3d1ace0ec8bc7f8e05f47b))
-   **partnership-contract-new:** fields layout issue ([c188bcf](https://github.com/boring91/injaz/commit/c188bcfe8409323bb58d3962f1921c2643f9a7dc))
-   **partnership-contract:** hide unwanted field ([4e436ee](https://github.com/boring91/injaz/commit/4e436ee9fc683f35b928ac9929cf5553476d3f35))

### Features

-   **backend:** change measurement department to measuring department ([f39c5d1](https://github.com/boring91/injaz/commit/f39c5d16f156fe9af9508a740cef67b3b800aad8))
-   **backend:** change names to partnered plan list ([cbf609d](https://github.com/boring91/injaz/commit/cbf609d3e3002b3020626fed2f38bb3404a5633e))
-   **backend:** code review changes ([3d11da5](https://github.com/boring91/injaz/commit/3d11da5334c1493e1290aea715ead3fc71f2f582))
-   **backend:** code review changes ([4e427c6](https://github.com/boring91/injaz/commit/4e427c671720e6979b1b376e909953fa55476b67))
-   **backend:** code review dto changes ([24b30ef](https://github.com/boring91/injaz/commit/24b30efc3e86ef6b8b8de70068320f0d7962d644))
-   **backend:** code review migration changes ([9db7d24](https://github.com/boring91/injaz/commit/9db7d24f11d96ecb39050925347a8a28e1fcf61d))
-   **backend:** correct report department completed kpi result count kpi result count ([dc9dd6c](https://github.com/boring91/injaz/commit/dc9dd6c2b5d4e0a3fa08d98ec4ee63998d75cd47))
-   **backend:** correct report department completed kpi result count kpi result count ([b12bd5c](https://github.com/boring91/injaz/commit/b12bd5c22537be8695794e354904484fef179c5d))
-   **backend:** correct report department not started kpi result count ([41b1658](https://github.com/boring91/injaz/commit/41b1658b694de8fd8b0d13f63fb6f61af80d5fa9))
-   **backend:** correct report department result ratio ([ef15d1c](https://github.com/boring91/injaz/commit/ef15d1c63dda85f38cbf0766b3491c97d2fc0567))
-   **backend:** format the code ([5d13320](https://github.com/boring91/injaz/commit/5d13320d06948589dd92552b0a6d215daaaf879a))
-   **backend:** main operation owner ([d3df1fe](https://github.com/boring91/injaz/commit/d3df1fe9a2297090d19ed3195982fbe24fb1e170))
-   **backend:** measurement department ([c94567d](https://github.com/boring91/injaz/commit/c94567d64f88e6033649c88d40857d7e8a9df401))
-   **backend:** measurement department migration ([e7c3629](https://github.com/boring91/injaz/commit/e7c36296246155c9bc96422b69f066432e1307c7))
-   **backend:** name in english is not required ([c172a71](https://github.com/boring91/injaz/commit/c172a713ed0d8e04572b91f8d7f23976651ba057))
-   **backend:** name in english is not required in create and update ([89b33d8](https://github.com/boring91/injaz/commit/89b33d8c268ee560553245b3e654c8490fd83073))
-   **backend:** plan internal partner ([48bfb43](https://github.com/boring91/injaz/commit/48bfb43e32de467651a4128b58f3c36e60874bf0))
-   **backend:** plan internal partner migration ([fc4093d](https://github.com/boring91/injaz/commit/fc4093de1f56696871e0a688787810bf98a9f05b))
-   **back:** remove dashboard top items ([cb73a2d](https://github.com/boring91/injaz/commit/cb73a2d3418aecc781ecc5e31dad6fd1c9aae23e))
-   **front:** adjusted color percentage in department result ([be4957d](https://github.com/boring91/injaz/commit/be4957d1c51a5832afa6c9a60dcd1ae285be02df))
-   **frontend-operation:** add new field for operation owner ([082107b](https://github.com/boring91/injaz/commit/082107bc9468e490cad10e4508e77b99e8f35cb5))
-   **frontend-plan:** add internal partners field ([e45e222](https://github.com/boring91/injaz/commit/e45e222c0dbf4e31b0e9e9b264d0b1cc3e86e8c5))
-   **frontend-plan:** adjust select tree ([b1c0a52](https://github.com/boring91/injaz/commit/b1c0a5214db5dae8e3fa0a9e17582fa62983c0eb))
-   **frontend:** add input in kpi result page ([f049320](https://github.com/boring91/injaz/commit/f049320de2e28bface1b815294a67d634835fba1))
-   **frontend:** add some changes to update select ([c622add](https://github.com/boring91/injaz/commit/c622addb70ca2742c46aee1cdaddd2dd5a980589))
-   **frontend:** create filter and add input in create with measurement Department ([8add42e](https://github.com/boring91/injaz/commit/8add42eda4c03f144c579378af860ec93586e11c))
-   **frontend:** fix function and Properties names ([645b8df](https://github.com/boring91/injaz/commit/645b8df67e877d259245f866051ceedcbc11dffa))
-   **frontend:** fix function and Properties names ([70ceacd](https://github.com/boring91/injaz/commit/70ceacdc7eb90b9ee4f8654cf7dc004913712fd8))
-   **frontend:** fix key title ([87a7df2](https://github.com/boring91/injaz/commit/87a7df2d6c854d2add29585681204e8b4ea820aa))
-   **frontend:** update naming ([c193947](https://github.com/boring91/injaz/commit/c193947a95664a6b53bfaab59ec0e5b540d578f8))
-   **front:** fix conflicts and merge ([6a1f05d](https://github.com/boring91/injaz/commit/6a1f05d4db3caa80ce424aeff9a239ae4c8c3bed))
-   **front:** fix measuring Department ([b9349f4](https://github.com/boring91/injaz/commit/b9349f44e507eb85e91ea3a81baa3209f95b1dca))
-   **front:** fix measuring Department ([50c4907](https://github.com/boring91/injaz/commit/50c4907375662ecc3e1224003010b678bf818179))
-   **front:** fix Property name ([e84db87](https://github.com/boring91/injaz/commit/e84db879540d036a5586da81dba331c5cbb0438c))
-   **front:** fix revert this (flex flex-col to grid ) ([ed9803e](https://github.com/boring91/injaz/commit/ed9803e81675a9b49d9090dd41c14593f682c161))
-   **front:** fix translate ([10b4c64](https://github.com/boring91/injaz/commit/10b4c64ddefa99b802cbb9aed12b25a926962b65))
-   **front:** fix translate ([e42bf77](https://github.com/boring91/injaz/commit/e42bf77d0d3dad8b4be0c71ac057b070faf78cc5))

# [1.121.0](https://github.com/boring91/injaz/compare/v1.120.0...v1.121.0) (2024-12-16)

### Bug Fixes

-   **backend-statistical-report:** remove unused variable ([744d8f1](https://github.com/boring91/injaz/commit/744d8f18068af2feac549a9bffe09a60e3be3388))
-   **backend:** check remove years not available ([3f6dc01](https://github.com/boring91/injaz/commit/3f6dc016ae2af8ece47365c4d1226b12b6014b4c))
-   **backend:** enable initialYear ([99ffb23](https://github.com/boring91/injaz/commit/99ffb238c63260509521cb0bdeac32c79f59a281))
-   **backend:** fix result years ([6864412](https://github.com/boring91/injaz/commit/6864412a7d748a90cf075af732fa6f2073f7db73))
-   **frontend-statistical-report:** enable categories on edit mode ([d036d9d](https://github.com/boring91/injaz/commit/d036d9d3c72b81adaecc2280239ebaaeac6c121d))
-   **frontend:** enable initialYear ([43e9cfa](https://github.com/boring91/injaz/commit/43e9cfadb0744264b67365f29f89488d67f10495))

### Features

-   **back:** add approve status ([76f9af3](https://github.com/boring91/injaz/commit/76f9af3af60adcda581be5afda488f365016c249))
-   **back:** add one commit, improve the code as much as possible based on the last meeting ([c592b8b](https://github.com/boring91/injaz/commit/c592b8bddc253fe0014fe62434896cec424e7f68))
-   **back:** cancel merge with pr 1170 then pull and merged from develop ([63eaf61](https://github.com/boring91/injaz/commit/63eaf61b0d4dd3e1febef239f15d595f9227397b))
-   **backend-frontend-duplicate-report:** duplicate or copy report with add permission ([0c06134](https://github.com/boring91/injaz/commit/0c06134c35a705405e1a092a05bbeb650c07fc38))
-   **backend-frontend-duplicate-report:** fix and edit some notes when duplicate ([d734bd3](https://github.com/boring91/injaz/commit/d734bd3a15bc74c1f8d786beda2081081e853c68))
-   **backend-frontend-duplicate-report:** remove query not used in update changeValue ([d87891b](https://github.com/boring91/injaz/commit/d87891b7de04a9550ee5ed7ce8f135fab2c75cf9))
-   **backend-front:** fix translate , merge and notes ([7509950](https://github.com/boring91/injaz/commit/7509950ae7197404213fe3a4d3de5fb77fc1951d))
-   **backend-front:** fix translate , merge and notes ([3c52088](https://github.com/boring91/injaz/commit/3c520883165418078b4a5122692296a8864922d5))
-   **backend-front:** remove null! ([d3c462e](https://github.com/boring91/injaz/commit/d3c462efe710356eed306609490f080af1be7e2e))
-   **backend-update-categories:** remove query not used in update changeValue ([cb03f34](https://github.com/boring91/injaz/commit/cb03f34041f2dc192a5562b883e6c7e4ca5ffd54))
-   **backend:** fix notes with merge ([e880c6e](https://github.com/boring91/injaz/commit/e880c6e699024fab699b06ed525d7a549900715d))
-   **backend:** work on copy report ([6284741](https://github.com/boring91/injaz/commit/62847413f8efc3551651d709854cb68ceb194042))
-   **front:** added filter to subsubtask approval finalization list ([c1ab3ef](https://github.com/boring91/injaz/commit/c1ab3ef2f96909d67714e43219284a7caf849f5f))
-   **front:** disable initial year and cycle ([8144b58](https://github.com/boring91/injaz/commit/8144b589bc802cea059c691ad0ef963f6a3136d1))
-   **frontend-backend:** add new filter for procedure status ([8e1b9a9](https://github.com/boring91/injaz/commit/8e1b9a92662814b499df6ee9bab93acd4c8b2fcc))
-   **frontend-backend:** change field approveStatus to approvalStatus ([d1cde14](https://github.com/boring91/injaz/commit/d1cde147932b5af8b679986ce2be249cac4ec39d))
-   **frontend-backend:** change field procedure status to status ([3efd857](https://github.com/boring91/injaz/commit/3efd857b852d6120631559a1fada71767064a796))
-   **frontend-duplicate-report:** modify translate ([38c6a9e](https://github.com/boring91/injaz/commit/38c6a9e1505fc549da4a7ec3d3fd42c0342e17ff))
-   **frontend-duplicate-report:** modify translate ([5e65b52](https://github.com/boring91/injaz/commit/5e65b521310e7bd8f8d74c02a51d3b3c9b8a4b0b))
-   **frontend-duplicate-report:** remove with data button ([9e8e703](https://github.com/boring91/injaz/commit/9e8e703ba8cdef154b71e9d495f9716899b71d96))
-   **frontend:** delete symbol ¸ ([23ecc10](https://github.com/boring91/injaz/commit/23ecc100188c53fe097d6da60ac84759179bcd0e))
-   **frontend:** remove translate all and add clearable in dropdown ([8913764](https://github.com/boring91/injaz/commit/8913764b8bb7e9c73c3597b0573afe8da9f4efde))
-   **front:** finished filter for sub subtask approval list ([c9844b5](https://github.com/boring91/injaz/commit/c9844b5d3bf3124f462f86345a92f83251e502c2))
-   **front:** fix name statisticalReportCopy ([6ccc9ab](https://github.com/boring91/injaz/commit/6ccc9ab1a8983463c4a8472284cc7c6369ce3fb5))

# [1.120.0](https://github.com/boring91/injaz/compare/v1.119.1...v1.120.0) (2024-12-16)

### Bug Fixes

-   **backend:** fix translate ([f3e9379](https://github.com/boring91/injaz/commit/f3e93793eaed63a02c4208ec10d5f4b04cc14007))
-   **backend:** fix translate ([8557ab2](https://github.com/boring91/injaz/commit/8557ab24be885091476992d5df3b46f9cfba4ca1))
-   **backend:** fix translate ([9c101fb](https://github.com/boring91/injaz/commit/9c101fbab14d1e81199383e686657ebec91ac740))
-   **back:** fix message date from and to ([6fa835b](https://github.com/boring91/injaz/commit/6fa835be148ea2c38900d6a849382deeb96f8f85))
-   translation types ([36b557a](https://github.com/boring91/injaz/commit/36b557a525add583ab55caccff21b2df6f0611cb))

### Features

-   **front:** edit translate ([169e32d](https://github.com/boring91/injaz/commit/169e32de7c602f6c946d281c8515babcb6eebb53))
-   **frontend:** add filter translate type kpi ([744bced](https://github.com/boring91/injaz/commit/744bcedeef87346d9915ec9a0fc5d5d9357c867a))
-   **frontend:** remove disabled for button saved ([2ad04b0](https://github.com/boring91/injaz/commit/2ad04b0d11095865de6789d531563cbe0cdc83b1))
-   **frontend:** required partner ([39b2d57](https://github.com/boring91/injaz/commit/39b2d570ffca0b9893142032f9998bf9893bd82d))
-   **front:** fix button (save and send) ([b7483f3](https://github.com/boring91/injaz/commit/b7483f37b087b796db3293345f5d2f574642ca87))

## [1.119.1](https://github.com/boring91/injaz/compare/v1.119.0...v1.119.1) (2024-11-27)

### Bug Fixes

-   **frontend:** update after department deletion ([5aa97b5](https://github.com/boring91/injaz/commit/5aa97b5ba74ff92fda4d70c7c7a009a0a9065c89))

# [1.119.0](https://github.com/boring91/injaz/compare/v1.118.0...v1.119.0) (2024-11-21)

### Bug Fixes

-   **evaluation:** fixing new evaluation inputs issue in smaller screens ([a40011c](https://github.com/boring91/injaz/commit/a40011cc2a826ac0b4f879317ae4b89da8ce7d16))
-   **front:** added missing readonly property to kpiLoader data entry component ([ea8ad65](https://github.com/boring91/injaz/commit/ea8ad6509340656ecc76b173e6c9287f4bbea1a8))
-   **frontend-system-list:** adjusting the tooltip and add field in update result ([46f3458](https://github.com/boring91/injaz/commit/46f34587043492697853835d8c8963b182468345))
-   **frontend:** enhanced the code as required in data entry request new ([79b10c7](https://github.com/boring91/injaz/commit/79b10c757e52e5e9207ad956321545cb3ad12d6d))
-   **frontend:** fix kpi charts show and hide labels ([cf4d71d](https://github.com/boring91/injaz/commit/cf4d71de854df0a71fe248c175c3e38fc776af56))
-   **frontend:** input does not have padding ([c56619b](https://github.com/boring91/injaz/commit/c56619bdade138908e9c9859bd96f78f6e9c60ce))
-   **frontend:** issue when calling `jumpToEnableDate` ([8263be9](https://github.com/boring91/injaz/commit/8263be95397670a103def359578ab1bd5af47b38))
-   **kpi:** fixed the issue when loading results that should be excluded from data entry ([96a2e87](https://github.com/boring91/injaz/commit/96a2e8785782da2f6cdc1f254ddb68c46a49cd0e))
-   **login:** adapt app email domain setting into the login form ([4e2f37b](https://github.com/boring91/injaz/commit/4e2f37bbd8650784672016bfe622b2beb305132f))
-   **page:** restored page style as it was ([5fcdf6c](https://github.com/boring91/injaz/commit/5fcdf6cb33af9353d8c9c97fbf9495de43c0a8d6))
-   **setting:** force rtl for app email domain field ([35c5557](https://github.com/boring91/injaz/commit/****************************************))
-   **setting:** group app email domain with other general fields and remove unnecessary code ([6aefd37](https://github.com/boring91/injaz/commit/6aefd37f6d9fcd9071045b133d4a6f11db1a7d5d))
-   **setting:** remove unnecessary method ([008ee2d](https://github.com/boring91/injaz/commit/008ee2de31e8610e268cd56679a045bf793dbb81))
-   **user:** adapt app email domain setting into user creation and remove messy code ([9ca6f70](https://github.com/boring91/injaz/commit/9ca6f703f8bd861595717fccf9be40dc36d69b32))

### Features

-   added ability to enable/disable data entry attachment requirement ([0a30c50](https://github.com/boring91/injaz/commit/0a30c507dac099877f51200cbd799ecbc513c002))
-   **back-front:** add condition to check if kpis return empty ([9d19fa3](https://github.com/boring91/injaz/commit/9d19fa31233585b7fedc09911c5894ca0761ea6e))
-   **back-front:** fix merge remote ([a57c593](https://github.com/boring91/injaz/commit/a57c593dfdf90e182b5c485be1b6c1c4468675c8))
-   **back-front:** remove is..should and change migration ([9816ee1](https://github.com/boring91/injaz/commit/9816ee181a9004c3eb8a7e7903789044c977dbdb))
-   **back-front:** work on DataEntryRequest ([54c22af](https://github.com/boring91/injaz/commit/54c22af615d992358ee08620bcaa874120494250))
-   **back:** add migration ([563612a](https://github.com/boring91/injaz/commit/563612a1071486722871b75aec926131a244a9aa))
-   **back:** change migration ([48c2a37](https://github.com/boring91/injaz/commit/48c2a37205519f0a80d00830b421f2899f0bd151))
-   **backend-decimal-places-control:** add DecimalPlaces in kpi ([9f111d4](https://github.com/boring91/injaz/commit/9f111d4e43639f98e73d7ea36b25671c72b506e4))
-   **backend-decimal-places-control:** add in kpi result ([34c11b4](https://github.com/boring91/injaz/commit/34c11b4b5e53071d88f5ded738decaed15cfee42))
-   **backend-decimal-places-control:** add in KpiResultDto ([3b626be](https://github.com/boring91/injaz/commit/3b626be4678d435b03b0d70abe675609c191fa41))
-   **backend-decimal-places-control:** add IsDecimalPlaceEnabled in kpi setting ([51a7551](https://github.com/boring91/injaz/commit/51a755147f53bec1d49faef2943fa5c5aeecab5e))
-   **backend-decimal-places-control:** add migration KPIDecimalPlaces ([01d5794](https://github.com/boring91/injaz/commit/01d5794c9322d6f9d65a0a3b3bf83dc1972d28bf))
-   **backend-decimal-places-control:** change value in results and archived by decimal property ([a5daf1b](https://github.com/boring91/injaz/commit/a5daf1be60fa403e66515d07a817750f95fad89c))
-   **backend-decimal-places-control:** fix and modify some notes in decimal_places ([e0dae62](https://github.com/boring91/injaz/commit/e0dae628539a67fde16cfa56c81385a5487cabbb))
-   **backend-decimal-places-control:** fix migration ([23339ae](https://github.com/boring91/injaz/commit/23339ae5a2ef8c23f701f2eaacad60fce40f0a71))
-   **backend-front-decimal-places-control:** fix number achieved and add min value for decimalPlaces ([39fcf3c](https://github.com/boring91/injaz/commit/39fcf3c71bef9fde073747b4741566410ab40ad5))
-   **backend-front-decimal-places-control:** fix number achieved and add min value for decimalPlaces ([9b1a19f](https://github.com/boring91/injaz/commit/9b1a19fd97186c453a6190e6fea878c8cb3a139f))
-   **backend-front-decimal-places-control:** remove round from front and round in KpiResultPeriod ([523572b](https://github.com/boring91/injaz/commit/523572b22df23c433434f17d553c5e91aee033ef))
-   **backend-front:** fix translate , format and change round from back to front ([f5d9285](https://github.com/boring91/injaz/commit/f5d928544cb9445fb5640b5a98ed594d10978bb6))
-   **backend:** add app email domain ([505e76b](https://github.com/boring91/injaz/commit/505e76beed3bc052307a08e6ab41be691707dc91))
-   **backend:** add app email domain create dto format ([3512c74](https://github.com/boring91/injaz/commit/3512c749d19a5cc709397d9e100eaf71968a65cb))
-   **back:** fix notes ([cd8794f](https://github.com/boring91/injaz/commit/cd8794f24d77f8287b28196d6618c7478ee5c3a3))
-   **back:** remove condition to check if kpis return empty ([60f7477](https://github.com/boring91/injaz/commit/60f7477c297687b619646ffdc6bdaa3030e33092))
-   **front:** adding filter based on previous filters ([0dfd049](https://github.com/boring91/injaz/commit/0dfd049e783718b401410f2e8413eb11a566c72b))
-   **frontend-kpi:** add button hide-show result in 2 component achieved and target ([f071983](https://github.com/boring91/injaz/commit/f071983e713417d2fd4ebd5170c729c9f7d6819c))
-   **frontend-kpi:** add checkbox show or hide result on top of bar ([585b99c](https://github.com/boring91/injaz/commit/585b99c9f35628131ee7710091bf2cdff6eff39f))
-   **frontend-kpi:** add new field called decimal point ([064c9f0](https://github.com/boring91/injaz/commit/064c9f07dabfb249e603b9ad32786ee66679f198))
-   **frontend-kpi:** add show or hide results on chart in details page ([fdb242f](https://github.com/boring91/injaz/commit/fdb242f6bb7f1f197a5fec54ad5a36999c7951a9))
-   **frontend-kpi:** add the decimal places field and remove the rounding from the table ([3b410ea](https://github.com/boring91/injaz/commit/3b410ea2268054d5f6cc59328ab63316851fb4c3))
-   **frontend-kpi:** determine object that return ([073daa7](https://github.com/boring91/injaz/commit/073daa7865e74f5b8fac055530cfd30c8924f766))
-   **frontend-kpi:** determine object that return ([c7f94f3](https://github.com/boring91/injaz/commit/c7f94f3568892e8f231d2a4fb586648427292bff))
-   **frontend-kpi:** enhance ([ca92ab1](https://github.com/boring91/injaz/commit/ca92ab177fe44b1015ccbef9e09977b5e7702fd9))
-   **frontend-kpi:** fix conflict code ([eca329b](https://github.com/boring91/injaz/commit/eca329b41eaef5446950d884a702e1e8fb8ff292))
-   **frontend-kpi:** hide or show result on top of bar in chart ([c235f52](https://github.com/boring91/injaz/commit/c235f52b612652a57867d911b2a2536a52626fb0))
-   **frontend-kpi:** hide or show result on top of bar in chart using ChartDataLabel ([bba8abc](https://github.com/boring91/injaz/commit/bba8abcf2c52f63e9be3adf219ea9f7ccb14c043))
-   **frontend-kpi:** hide or show result on top of bar in chart using ChartDataLabels ([d5d1890](https://github.com/boring91/injaz/commit/d5d18908f12b6005166c52c4aedbeaba1aceab2f))
-   **frontend-kpi:** hide result in the start ([7162ff2](https://github.com/boring91/injaz/commit/7162ff2ff4ae4e83befef3e94abb29fd869486b3))
-   **frontend-kpi:** reverse status show results ([a0c7432](https://github.com/boring91/injaz/commit/a0c74323854b960d08a0141e7e488da6f6b71fdb))
-   **frontend:** add custom field by design ([21b00de](https://github.com/boring91/injaz/commit/21b00de536d1cc049e7341f409567fc9a03db66b))
-   **frontend:** add domain in login page and send it to update email ([f6f25e8](https://github.com/boring91/injaz/commit/f6f25e83ba17dd0715def8bb9bc72c5915749d0a))
-   **frontend:** add domain in new user page ([c7f6d07](https://github.com/boring91/injaz/commit/c7f6d07c23a2036058b2530d1cf1e7b182a32782))
-   **frontend:** add permission for disable lock button ([15c100b](https://github.com/boring91/injaz/commit/15c100b43d3177442b965ba99b5904cec221aa49))
-   **frontend:** add some changes in style ([4085bd0](https://github.com/boring91/injaz/commit/4085bd08cd39914888e950a77342dff63e92d40c))
-   **frontend:** create branch and add input ([74c9ec2](https://github.com/boring91/injaz/commit/74c9ec2bb35c6d1d85da266c5f5efe2b8d910062))
-   **frontend:** fix domain based on last comment ([a95a011](https://github.com/boring91/injaz/commit/a95a0114c7d20cacfcec7cf6d1ab55efde0a0532))
-   **frontend:** fix lock ([6ce8002](https://github.com/boring91/injaz/commit/6ce800283362cfa166ff2b836a23e76342a2381e))
-   **frontend:** fix lock permission ([cf3a563](https://github.com/boring91/injaz/commit/cf3a563b677fcee8f43c2e0a75f46e2fe43579b3))
-   **frontend:** fix lock permission ([9329c21](https://github.com/boring91/injaz/commit/9329c21ce3d1464ced20bef35203fc017161c928))
-   **frontend:** fix problem in login with domain ([0adef36](https://github.com/boring91/injaz/commit/0adef3645e3fab3fd176ad1ce5d3e7f8865834da))
-   **frontend:** fix problem in report lock ([af91a02](https://github.com/boring91/injaz/commit/af91a02e1779389c6a6474e4a93168e65937c7a1))
-   **front:** fix bug round ([7aa0d74](https://github.com/boring91/injaz/commit/7aa0d745ab49d6ab436354a6075147193fc24533))
-   **front:** fix comments change preventDecimal to isIntegerOnly ([64da519](https://github.com/boring91/injaz/commit/64da519aa87b5d82d5f686904fd068cc1cd4a086))
-   **front:** fix field name ([59fcf99](https://github.com/boring91/injaz/commit/59fcf99bea5e42a47c62083b6803979b8c31c9b1))
-   **front:** fix format Prettier ([23a7279](https://github.com/boring91/injaz/commit/23a7279b70e98c7057132c69bd97566233a16cc3))
-   **front:** fix merge and change email with domain in add user ([52666df](https://github.com/boring91/injaz/commit/52666dfd4981e495527edc01a0115dca616f99d7))
-   **front:** fix suffix ([8490757](https://github.com/boring91/injaz/commit/8490757b467aaaa448110dc1087b3188781bb487))
-   **front:** modify translate ([fc9247c](https://github.com/boring91/injaz/commit/fc9247cddc493417201a901644ff275046e8d7cb))
-   **front:** revert the changes on this file ([09b4ccb](https://github.com/boring91/injaz/commit/09b4ccbaa1b7f99dbe81cf105fdcb7e89fb2fbc5))

# [1.118.0](https://github.com/boring91/injaz/compare/v1.117.0...v1.118.0) (2024-11-18)

### Bug Fixes

-   **frontend:** collapse side bar when dashboard selection is style_1 ([336d6b2](https://github.com/boring91/injaz/commit/336d6b24871fce70d6317460526da4a5251647ba))

### Features

-   **frontend:** hide sidebar via home button in style1 ([0355fd4](https://github.com/boring91/injaz/commit/0355fd4161b4ce928b6a1069df7e37a1e46798da))

# [1.117.0](https://github.com/boring91/injaz/compare/v1.116.0...v1.117.0) (2024-11-15)

### Features

-   **backend-front:** add new fields to partner ([7a0bf24](https://github.com/boring91/injaz/commit/7a0bf24e3244bd87969dca1610848fd93a98303a))
-   **backend:** add new fields ([ad3ee31](https://github.com/boring91/injaz/commit/ad3ee31ff69fdc42bbd0c12b2e2a70477dae620b))
-   **backend:** fix translate ([f64ed34](https://github.com/boring91/injaz/commit/f64ed34221a7bedd3538e1c9fb943ddc1e06d4fe))

# [1.116.0](https://github.com/boring91/injaz/compare/v1.115.0...v1.116.0) (2024-11-14)

### Features

-   **backend-front:** added comments and change excludedProcedureId to excludedProcedureIds ([5a2a756](https://github.com/boring91/injaz/commit/5a2a756c56890c5f858248fd395c88b141de8af2))
-   **backend-frontend-display-duration-procedure-steps:** display duration procedure steps ([f6bf83b](https://github.com/boring91/injaz/commit/f6bf83b3983241a21b8879961d94f3a7951cffe3))

# [1.115.0](https://github.com/boring91/injaz/compare/v1.114.2...v1.115.0) (2024-11-13)

### Features

-   added tutorial video for risk management ([74c8e86](https://github.com/boring91/injaz/commit/74c8e866cee510e94baa70e8b74f88af7f1278a9))

## [1.114.2](https://github.com/boring91/injaz/compare/v1.114.1...v1.114.2) (2024-11-12)

### Bug Fixes

-   **frontend:** allow app logo upload outside of safe mode ([5037091](https://github.com/boring91/injaz/commit/50370919d83fe4b994925da585c9dad826a8403c))

## [1.114.1](https://github.com/boring91/injaz/compare/v1.114.0...v1.114.1) (2024-11-11)

### Bug Fixes

-   **frontend:** moved gauge text to bottom of gauge ([70b87e6](https://github.com/boring91/injaz/commit/70b87e673204585c501def1bf55690cdad9dfdae))

# [1.114.0](https://github.com/boring91/injaz/compare/v1.113.2...v1.114.0) (2024-11-11)

### Bug Fixes

-   backend not starting up due to translation json invalid format ([9866ba7](https://github.com/boring91/injaz/commit/9866ba7e167de3c3d125f73315ed1d6833485147))
-   **backend-breakdowns:** edit name Breakdowns to PeriodBreakdowns ([82eb8b9](https://github.com/boring91/injaz/commit/82eb8b92da2d9022fc0803b27a16da7345c0cd08))
-   **backend-front-breakdowns:** fix delete message ([347062f](https://github.com/boring91/injaz/commit/347062fe1084ce485281117354568f4c1f1e99f9))
-   **backend:** code review changes ([ec6b02b](https://github.com/boring91/injaz/commit/ec6b02bd511a309afd3c6d9b597606e16bcd24b5))
-   **backend:** fix model snapshot ([0d09052](https://github.com/boring91/injaz/commit/0d0905232250e505d2573996c8c0d3cedb67b691))
-   **front-field-required:** add required for appNameAr , appId ([03b99e4](https://github.com/boring91/injaz/commit/03b99e406be159b4a75dfbc4478e88b9fab8323a))
-   **frontend-kpi:** :bug: fix disabled inputs on kpi result form ([b852d56](https://github.com/boring91/injaz/commit/b852d561d1ba0f6736f0783187ed263c1b6de308))
-   **frontend-kpi:** prevent send kpi result responses request if has no permission ([decc655](https://github.com/boring91/injaz/commit/decc6556d036f222bf0fd2d77a2513d665718b8f))
-   **frontend-kpi:** use the `kpiResultWrite` permission on the correct edit button ([97e961c](https://github.com/boring91/injaz/commit/97e961ce046e6142622a443c9e8f1e7d81260c47))
-   **frontend:** added missing permission guard to plan pages ([c9d5833](https://github.com/boring91/injaz/commit/c9d5833b0b9696405c2bc929e6649a82051f3222))
-   **frontend:** added no permission message for permission guard ([a3be037](https://github.com/boring91/injaz/commit/a3be037542af24ded275be1b31d146ba799feb7e))
-   **frontend:** fix conflict ([c938a06](https://github.com/boring91/injaz/commit/c938a063d10f3c21c4b34d5fd1ce6c087c7a617c))
-   **frontend:** fix conflict ([753a8ef](https://github.com/boring91/injaz/commit/753a8ef92aeef484884a6a8979fb8d6db357fcec))
-   **frontend:** fix conflict ([3d1743c](https://github.com/boring91/injaz/commit/3d1743c422f8d6da87020c1fb97916ccfc7bf5b5))
-   **frontend:** fix kpi result ([d6fbd4b](https://github.com/boring91/injaz/commit/d6fbd4bb3d90bf243c0f9ac36bfe598bffe96a90))
-   **frontend:** fix permission ([1a3ba68](https://github.com/boring91/injaz/commit/1a3ba6861ec63990e0cff845c4b32a74308de211))

### Features

-   **backend:** add kpi result data entry response permission ([61a452d](https://github.com/boring91/injaz/commit/61a452d24001b626f5f7e3617e534f1384ec982a))
-   **backend:** change column name to dashboard top item migration ([9e6d627](https://github.com/boring91/injaz/commit/9e6d6277c686291c94dda9319f28c6f22f7339b4))
-   **backend:** change the permission to kpi read ([d19ba64](https://github.com/boring91/injaz/commit/d19ba644484016cfa881631790c747dba8edfbf3))
-   **backend:** dashboard top items migrations ([e4a3bd9](https://github.com/boring91/injaz/commit/e4a3bd98619d988b49f36dac86e8ff3522af368a))
-   **backend:** sustainability impact not required ([3c600ef](https://github.com/boring91/injaz/commit/3c600ef1c3faf7129ad925ed31ff629bf085c74d))
-   **backend:** translate dashboard top items ([bc9d21c](https://github.com/boring91/injaz/commit/bc9d21cfde3e2357eb0b2a11569e56663bc5ce47))
-   **frontend-kpi): fix(kpi-update-result:** add missing condition in sensitive case ([f7ac11d](https://github.com/boring91/injaz/commit/f7ac11d62cf9c7f8de1043b951632b029e0e5974))
-   **frontend-kpi:** adjust the permission of the kpu data entry ([2025ae4](https://github.com/boring91/injaz/commit/2025ae47645cc4fdba8d434072aa225acdb7ec5c))
-   **frontend(kpi-evaluation):** add a button to approval for the evaluation ([a742e1d](https://github.com/boring91/injaz/commit/a742e1d3f2fd3704d4bf57e4be6861d8c0c588aa))
-   **frontend(kpi-evaluation):** adjust the style of the action in the evaluation instance details ([05d75ca](https://github.com/boring91/injaz/commit/05d75ca9f91e9292d2ec6947bc24257fc8a11d99))
-   **frontend:** add new fields in dashboard setting ([31eca8d](https://github.com/boring91/injaz/commit/31eca8d75c6bcf402646f4e6f89fcbd247d6394b))
-   **frontend:** added gauge style 1 ([dadf9d5](https://github.com/boring91/injaz/commit/dadf9d5a583eb581054faa8ba340006a0d4b4bb6))
-   **frontend:** translate field inputs in app settings ([428df7b](https://github.com/boring91/injaz/commit/428df7bc69a050626d74b0b9d2d70a1d5ada50d3))
-   **front:** fix format in opportunity-new and change flex to grid in mm-form ([24d506a](https://github.com/boring91/injaz/commit/24d506a86e51e44e0d1f754512d1de3cf603948a))

## [1.113.2](https://github.com/boring91/injaz/compare/v1.113.1...v1.113.2) (2024-11-07)

### Bug Fixes

-   **dashboard:** allow anyone to access get endpoints for dashboard settings ([b0836f8](https://github.com/boring91/injaz/commit/b0836f8c0e1d5fc70620a4f67d65c016e0cb0cf1))
-   **frontend:** added space in user card between elements ([b683731](https://github.com/boring91/injaz/commit/b683731a6f6cbc5d48ce81a92b80be59643bcadc))

## [1.113.1](https://github.com/boring91/injaz/compare/v1.113.0...v1.113.1) (2024-11-07)

### Bug Fixes

-   **dashboard:** increase the goal width in dashboard ([9428254](https://github.com/boring91/injaz/commit/94282540c4763c4933b72ca90c56e860e53fa70a))
-   **evaluation:** remove index number when displaying options of evaluation ([3ca7c3b](https://github.com/boring91/injaz/commit/3ca7c3b08af888f23b54010d02dceb6dc7782b59))
-   **frontend:** added logo to the header when on dashboard page for style 1 only ([81bf0eb](https://github.com/boring91/injaz/commit/81bf0eb977dabd27bdb8b29b4bc2d7c6b584e786))
-   **frontend:** hide sidebar for style 1 dashboard ([3dd0cc3](https://github.com/boring91/injaz/commit/3dd0cc37f0a60ca6571c0981c75a56d15579cdfa))
-   **frontend:** linting errors ([03cb457](https://github.com/boring91/injaz/commit/03cb457ef4eae50aac7eab71ec46b2a9cb38bb17))
-   **frontend:** move the user card from sidebar to header ([61ce0d7](https://github.com/boring91/injaz/commit/61ce0d727f7615d42d128058f237117677fd194f))
-   **settings:** added ability to hide sections and items in dashboard style 1 ([5a9d140](https://github.com/boring91/injaz/commit/5a9d14046bc94a386e2314bf8e7f325f7ea9f520))

# [1.113.0](https://github.com/boring91/injaz/compare/v1.112.0...v1.113.0) (2024-11-06)

### Bug Fixes

-   **backend:** fix bug in kpi report ([c3e6db2](https://github.com/boring91/injaz/commit/c3e6db25577290054b430a13d55e9db1d8792bff))
-   **evaluation:** proper coloring for evaluation result select options ([3086c1a](https://github.com/boring91/injaz/commit/3086c1af3aa7140f3a2d8d7575cbe9ce004a6a22))

### Features

-   **backend:** permission kpi read to kpi result data entry response ([f0f2376](https://github.com/boring91/injaz/commit/f0f2376efd8c338e26f118528b5d8def5d930618))
-   **backend:** remove relation between kpi result flow and kpi result write ([6e1e6e1](https://github.com/boring91/injaz/commit/6e1e6e1f159ede2724af5e5c0cb1aa5d72ca26cf))
-   **front:** change permission kpiResultFlow to kpiRead ([fd9d3f7](https://github.com/boring91/injaz/commit/fd9d3f7aa74a3df2406a1d0b461a4cf57d261bd5))
-   **front:** change permission kpiResultFlow to kpiRead ([d03cf5a](https://github.com/boring91/injaz/commit/d03cf5a36ef8e0be3e37e3207b4ae08ef94eec8c))
-   **frontend-evaluation-colors:** enhancement set color ([42c05bf](https://github.com/boring91/injaz/commit/42c05bfe79cd5dd0ba7685a422cce0b85a5e94e2))
-   **frontend-evaluation-colors:** fix error style ([8ea055a](https://github.com/boring91/injaz/commit/8ea055a788292048d7b542ebadf08281f47d4861))
-   **frontend-evaluation-colors:** set colors ([3f6b3b5](https://github.com/boring91/injaz/commit/3f6b3b58c35fdd87dcd9d5a30fd7b72e60559952))

# [1.112.0](https://github.com/boring91/injaz/compare/v1.111.0...v1.112.0) (2024-11-05)

### Bug Fixes

-   **dashboard:** limit style 1 dashboard to ajman police ([5b8eeb6](https://github.com/boring91/injaz/commit/5b8eeb6a7cc386f531368453bf1bb89433c9be7f))
-   **dashboard:** top items dialog not popping up in style 1 dashboard ([dde0b99](https://github.com/boring91/injaz/commit/dde0b99f63c2cf14fef1d8e6299ea5f3eb407cd8))

### Features

-   added dashboard settings + style-1 dashboard settings ([6b31646](https://github.com/boring91/injaz/commit/6b31646d7b4d8029aeb816ddec23b88f323d98f0))
-   **dashboard:** added style 1 dashboard component ([2ab4fa8](https://github.com/boring91/injaz/commit/2ab4fa8b8d705e7ca0a3042e6060a9bcc15acbe5))

# [1.111.0](https://github.com/boring91/injaz/compare/v1.110.0...v1.111.0) (2024-11-04)

### Bug Fixes

-   **frontend-enhancement:** add enhancement service as a provider to the details component ([2f51ae3](https://github.com/boring91/injaz/commit/2f51ae358801438424ee57abfcf424a65cb923e6))
-   **frontend-kpi:** fix word increase is best in periods table ([075011e](https://github.com/boring91/injaz/commit/075011e75896047178f9c935005bdcacaefa3053))
-   **frontend-system-list:** adjust the translation ([36dbb11](https://github.com/boring91/injaz/commit/36dbb119185c9799313e49fe3299fae82262c623))
-   **frontend-tree-component:** removing the deleted item from the grid ([7ca4666](https://github.com/boring91/injaz/commit/7ca4666b087d682f9d044ec78059f0718403c3e0))

### Features

-   **backend-multiple-action-opportunity:** add list AssignedDepartment-AssignedTeamL-AssignedUser ([8bc044c](https://github.com/boring91/injaz/commit/8bc044cc5abaaac5c6a68367e70b153ee21af079))
-   **backend:** operation code fields ([7090133](https://github.com/boring91/injaz/commit/70901335c5df86d664a1d0e543fd9174a935226f))
-   **backend:** remove user from standard ([3afda97](https://github.com/boring91/injaz/commit/3afda9711dcf6f946580d003459316ea3c27ddc9))
-   **frontend-department:** add buttons add and delete in department details page ([28e7a6a](https://github.com/boring91/injaz/commit/28e7a6a8c0d06522593ff900cff24f64f0669a7c))
-   **frontend-department:** change place of delete button ([77036d8](https://github.com/boring91/injaz/commit/77036d8aa19ece5f15b06cd88725a6e829cf7c82))
-   **frontend-kpi:** separate words translated kpi achieved ([f043b24](https://github.com/boring91/injaz/commit/f043b2492780336b3c6a2f4b64308fcb7faf700c))
-   **frontend-multiple-action-opportunity:** add list AssignedDepartment-AssignedTeamL-AssignedUser ([599d0ed](https://github.com/boring91/injaz/commit/599d0ed3f6a0a945f7f0ab8a7cfa519ae921d5de))
-   **frontend-multiple-action-opportunity:** fix multi dropdown ([2c442f2](https://github.com/boring91/injaz/commit/2c442f2a593d8b67816e2915999490937500942d))
-   **frontend-operation:** add two fields in add or edit and operation details ([0837c66](https://github.com/boring91/injaz/commit/0837c66b2a3f4924ccbc3e09d6530604320e7cfa))
-   **frontend:** create checkbox for show and hide password ([b1117fa](https://github.com/boring91/injaz/commit/b1117faf00f8559c1798ea6d944aee91df26d98c))

# [1.110.0](https://github.com/boring91/injaz/compare/v1.109.0...v1.110.0) (2024-10-29)

### Bug Fixes

-   **frontend:** fix bug in date Excellence Awards ([11f8a8f](https://github.com/boring91/injaz/commit/11f8a8f17e3934e475ed9e981a601e448101e2f4))

### Features

-   **backend:** capability revert changes ([db108af](https://github.com/boring91/injaz/commit/db108af8b57d711a2a54631868036acf6da1c6f2))

# [1.109.0](https://github.com/boring91/injaz/compare/v1.108.0...v1.109.0) (2024-10-29)

### Features

-   **backend:** kpi next code ([4cbf4fc](https://github.com/boring91/injaz/commit/4cbf4fc33db4af4fe8fa9f7992ff72750ae3d692))

# [1.108.0](https://github.com/boring91/injaz/compare/v1.107.0...v1.108.0) (2024-10-28)

### Bug Fixes

-   **frontend-operation:** add missing condition in detail page ([291d537](https://github.com/boring91/injaz/commit/291d537bdc8ac08fd6033d292942099b16f1d875))
-   **frontend-operation:** add missing condition in get operation special level ([f9b24b6](https://github.com/boring91/injaz/commit/f9b24b60589042ae784d0f900a441096481a2e46))

### Features

-   **frontend-operation:** adding the option to update level 5 data for level 5 operations ([904cf78](https://github.com/boring91/injaz/commit/904cf783decc2900b5850a2069307e788a360d64))
-   **frontend-operation:** adding the option to update level 5 operations for ajman police ([e17fe23](https://github.com/boring91/injaz/commit/e17fe23e4fdff5032846b4cc510c801c059caaf6))

# [1.107.0](https://github.com/boring91/injaz/compare/v1.106.0...v1.107.0) (2024-10-24)

### Bug Fixes

-   **backend-awards-excellence-access-problem:** fix taskWeight and subtaskWeight equal 0 ([317673a](https://github.com/boring91/injaz/commit/317673ab865a7b624f5cdd944dae5ef862175eec))

### Features

-   **backend-order-column:** add migration ([ec69f51](https://github.com/boring91/injaz/commit/ec69f5121c06fa41981fbd372ffa3936bb17cc92))

# [1.106.0](https://github.com/boring91/injaz/compare/v1.105.2...v1.106.0) (2024-10-23)

### Bug Fixes

-   **backend-kpi:** improve kpi evaluation list dto ([3c3be24](https://github.com/boring91/injaz/commit/3c3be24dbf2ca2036fc5fd34ea12f11b4f0becb8))
-   **backend-plan-deletion-error-translation:** fix translate when delete ([fc37231](https://github.com/boring91/injaz/commit/fc37231b0c8bbdfca7e8e3344f2c3473e0c3980f))
-   **backend-plan:** remove same date filter condition ([dad4c8a](https://github.com/boring91/injaz/commit/dad4c8a86d37e1fd0b8623f26a3eaad9ce5d487f))
-   **backend-plan:** remove unused property from plan task ([41a4b39](https://github.com/boring91/injaz/commit/41a4b39a7335d432e5f2c0f2fcc01b18ce59220a))
-   **backend-plan:** update linked with user and team logic ([53c6a8d](https://github.com/boring91/injaz/commit/53c6a8d95861d9ba7f79b5cbd2effeca2fcd7505))
-   **backend-plan:** update subtask to date ([87fae1c](https://github.com/boring91/injaz/commit/87fae1ce1c2ce53ae28efeb8ea74a43c9c182a68))
-   **backend:** fix bug in operation procedure create and update ([0d76c2f](https://github.com/boring91/injaz/commit/0d76c2f0f3f222ca8ab408ec427f7756a9caf739))
-   **backend:** fix date in plan tasks and subtasks ([49e71a6](https://github.com/boring91/injaz/commit/49e71a663e233445c4750751795e275511102680))
-   **backend:** fix date in plan tasks and subtasks ([7a12c52](https://github.com/boring91/injaz/commit/7a12c520474ad538fc2867040769a4d7b786bbfa))
-   **backend:** fix error in operation procedure ([71b153f](https://github.com/boring91/injaz/commit/71b153f1fb64212b055792e8c16309d180ff99b7))
-   **backend:** operation procedure ([4fe256d](https://github.com/boring91/injaz/commit/4fe256db9418dd3cc4b0ad936078d2b7834fb95f))
-   **backend:** remove unused imports ([52157a2](https://github.com/boring91/injaz/commit/52157a26b8c5dc100892a3928fa7f3b8a8802017))
-   **backend:** rename variable and components from change approvers to transfer approval ([19e1e0b](https://github.com/boring91/injaz/commit/19e1e0b0e91ab2a6ae7fdc97f4d4fe208797111a))
-   **backend:** update wrong model ([ec1cda9](https://github.com/boring91/injaz/commit/ec1cda937b53e893c3a20d28cbb8d6bc458aacbe))
-   **backend:** update wrong model ([e38862e](https://github.com/boring91/injaz/commit/e38862e676144048a8962eee50b90542b3740def))
-   **backend:** update wrong model ([053a9ca](https://github.com/boring91/injaz/commit/053a9cace32997bdc2f298a2b67f846d40ce5981))
-   **backend:** use `AsExpandable` instead of `AsQueryable` ([4c3b614](https://github.com/boring91/injaz/commit/4c3b6141ca8f1a132a1ac8031522acde4c97f123))
-   **frontend-operation:** remove unused column ([fe232d8](https://github.com/boring91/injaz/commit/fe232d83bd63d5c24f3a1d08837a3226cb184cfa))
-   **frontend-plan:** fix the not approved list style ([d561c95](https://github.com/boring91/injaz/commit/d561c95dce56ebe1eecb86dc18057cbcfd39aaeb))
-   **frontend-shared:** improve flat date picker instance jump to date ([46f59f1](https://github.com/boring91/injaz/commit/46f59f1df383643900ef70ef2b4a3dff5046019e))
-   **frontend-sidebar:** show evaluation link on staging server ([51fbc6f](https://github.com/boring91/injaz/commit/51fbc6f09a9da6195eac26e270dcf76aa72d626c))
-   **frontend:** rename variable and components from change approvers to transfer approval ([764d430](https://github.com/boring91/injaz/commit/764d43078cdc7f4220a066e7987ba21ce16d95ab))
-   **frontend:** solve conflicts ([b646d03](https://github.com/boring91/injaz/commit/b646d035add7b4bdfe136e25d069cdd317acd30a))
-   **i18n:** add missing translation string ([76d4132](https://github.com/boring91/injaz/commit/76d41322bfb929568cc9d56c7f99c8664b2dc9a7))
-   **i18n:** fix translation name ([8bca921](https://github.com/boring91/injaz/commit/8bca921e2001b7fd6ceff30b90795378367a1f1f))
-   **i18n:** remove unused translations ([72abea2](https://github.com/boring91/injaz/commit/72abea2b98314d26fdde421a0e4a747397cc1b4e))
-   **kpi-evaluation:** fix some issues ([25b22f1](https://github.com/boring91/injaz/commit/25b22f11c63b0c2f022fb40b2ee4dfc1f430f6f8))
-   **kpi-evaluation:** fix some issues ([afc6630](https://github.com/boring91/injaz/commit/afc66307d87e016c9e02e92307c4015403636dec))
-   **kpi:** rename permission to the correct one ([d2d1655](https://github.com/boring91/injaz/commit/d2d1655bdf60bca9422cc786599c9010b0332494))
-   **kpi:** use `user.name` ([bc9dc0d](https://github.com/boring91/injaz/commit/bc9dc0d009ffa9cd6d2ad7291c7cc2e77c668661))
-   **kpi:** use the correct permission ([8abfbdd](https://github.com/boring91/injaz/commit/8abfbdd83b58c8fc66379d41416cf5806b26a379))
-   **operation:** order operation procedures by code ([ff9d5f1](https://github.com/boring91/injaz/commit/ff9d5f1b820bd395a11c70149a36486c7a833733))
-   solve conflicts ([005dded](https://github.com/boring91/injaz/commit/005dded67f06940e18602f99b804e77fd0b00f30))
-   **statistical-report:** fix statistical report permissions and attachments ([3b80680](https://github.com/boring91/injaz/commit/3b8068004b69779702efb26e9f94f96b0893ae0a))

### Features

-   **backend-frontend-plan-filter:** add filter user-team in plan tasks , plan sub task ([fe9125a](https://github.com/boring91/injaz/commit/fe9125a78d2875f6e1fa2cd6ca351251f5476414))
-   **backend-frontend-plan-filter:** modify filter user-team ([af6029e](https://github.com/boring91/injaz/commit/af6029e7a146930d55f48e6db0809b99b1949819))
-   **backend-frontend-plan-filter:** remove teams from db context plan not used ([9ed0356](https://github.com/boring91/injaz/commit/9ed0356b92fc16916f3e9ef9ea0288c6c17ea226))
-   **backend-kpi-evaluation-average-score:** add MeasurementCycle ([10ddea5](https://github.com/boring91/injaz/commit/10ddea57b986e87c6c1a3120c1f58705b874e0f4))
-   **backend-kpi-evaluation-average-score:** add new fields into kpi evaluation ([da19213](https://github.com/boring91/injaz/commit/da1921303f04791ecddf6b64f87534bec378afdb))
-   **backend-kpi-evaluation-average-score:** add new new api for details /kpi/evaluation/ ([a2796d5](https://github.com/boring91/injaz/commit/a2796d5c61233590420945541abc41055edfb77a))
-   **backend-kpi-evaluation-average-score:** change period id to instance id ([cade024](https://github.com/boring91/injaz/commit/cade02439eea47b780710a38d8b873cf846fe27a))
-   **backend-kpi-evaluation-average-score:** fix bug ([e61d6ba](https://github.com/boring91/injaz/commit/e61d6bab614769ef04b0a730115e7eaba0f79bca))
-   **backend-kpi-evaluation-average-score:** fix bug ([b0502ab](https://github.com/boring91/injaz/commit/b0502ab0206685315435a59238d9efc9d9489144))
-   **backend-kpi-evaluation-average-score:** fix error null ([b576b36](https://github.com/boring91/injaz/commit/b576b36f5fcd332fac8615b769e5c54fa8d86534))
-   **backend-plan-details-status-update:** add flowActionAvailability for details ([28e8513](https://github.com/boring91/injaz/commit/28e8513373a75272507075636e929ad28630ee59))
-   **backend-plan-details-status-update:** add flowActionAvailability for details ([c89c41f](https://github.com/boring91/injaz/commit/c89c41fb790f3aa59576be8d84d45893fd1fde0e))
-   **backend-plan-filter:** add new filter user-team ([ee02ef9](https://github.com/boring91/injaz/commit/ee02ef96e4f1f8675e726b6a4286d44ecdb22962))
-   **backend-service:** add SubName , SupplementaryName ([d32d70c](https://github.com/boring91/injaz/commit/d32d70cc55c8829d2db7c59b23ff0ca01b34420b))
-   **backend:** access locked statistical report ([bbf1cbc](https://github.com/boring91/injaz/commit/bbf1cbc3d795ce526d2451aed1022211225817dd))
-   **backend:** add change kpi result response approvers dtos and endpoints ([5eace19](https://github.com/boring91/injaz/commit/5eace1986cc5ba769fb763c0354f59bfc8a4f9da))
-   **backend:** add frontend translations ([b226ce3](https://github.com/boring91/injaz/commit/b226ce31731d69524a2c3ba6f19625328401d912))
-   **backend:** capability kpi result data entry ([09fca44](https://github.com/boring91/injaz/commit/09fca4474a5ee7988ea02ed99b455b056ac719f3))
-   **backend:** kpi balanced behavior card ([086afaa](https://github.com/boring91/injaz/commit/086afaa19a7c4f7b9fb5f0e76c704889fc428c34))
-   **backend:** kpi balanced behavior card ([4362f63](https://github.com/boring91/injaz/commit/4362f6383df3a983ebc4247206b8a351b93318eb))
-   **backend:** kpi balanced behavior card crud ([11cbc26](https://github.com/boring91/injaz/commit/11cbc2604467234617262dd5ad1874585fe9f8cb))
-   **backend:** kpi balanced behavior card permission ([070c834](https://github.com/boring91/injaz/commit/070c834ee20882a9b46b56fdb6eabf646a4faa3b))
-   **backend:** kpi balanced behavior card permission translation ([30f17f3](https://github.com/boring91/injaz/commit/30f17f3977e2443d066cb68fcdf5230ae865ac03))
-   **backend:** link operation procedure with each other ([dde9a0b](https://github.com/boring91/injaz/commit/dde9a0bec0820a90b2bec2a591f4407eee6abb09))
-   **backend:** operation procedure ([1961b18](https://github.com/boring91/injaz/commit/1961b181a1bd2ff8a339bb1cd3e6aedde58d2dc9))
-   **backend:** operation procedure parent child link ([04cb6da](https://github.com/boring91/injaz/commit/04cb6da4904cddc498810bff8310229488e211a0))
-   **backend:** statistical report attachment ([069ef31](https://github.com/boring91/injaz/commit/069ef31614687848bedc06202aebe12d505a3b43))
-   fixing the translation ([ff9407a](https://github.com/boring91/injaz/commit/ff9407ae5b3a47bfd77871a8c52986d733100c93))
-   **front-kpi-evaluation-average-score:** modify KpiEvaluationListDto ([5100fce](https://github.com/boring91/injaz/commit/5100fce2ba6595d09021364a0c4cb16fe575c258))
-   **front-kpi-evaluation-average-score:** modify MeasurementCyclePipe ([9d5046f](https://github.com/boring91/injaz/commit/9d5046f6603170aacb7f216900ad6a15019b182c))
-   **frontend-operation:** add field to link the procedure with another procedure ([190cbcc](https://github.com/boring91/injaz/commit/190cbccc67473f2764d1190e1d89c15ce352c450))
-   **frontend-operation:** add operation mapper in misc api ([e8ed899](https://github.com/boring91/injaz/commit/e8ed899c4de9cf1dd4b0296db29589e4a654ee5b))
-   **frontend-plan:** add update status action to plan details page ([76ff444](https://github.com/boring91/injaz/commit/76ff444d79c39ed1f014f109ebda89c1ac6dd0f1))
-   **frontend-system-list:** add new system list for kpi balanced behavior card and adding permission ([2cda914](https://github.com/boring91/injaz/commit/2cda9149789b44f0dfa6e9bb5be55e28beaf72c9))
-   **frontend(kpi-evaluation):** add list for the kpi evaluation ([7f55f52](https://github.com/boring91/injaz/commit/7f55f52958fb63abe6d0cd9ca92032ad8b8c9038))
-   **frontend(kpi-evaluation):** add title to the dialogs add pipe for measurement cycle ([543a7d7](https://github.com/boring91/injaz/commit/543a7d7bf23936b0485bce21e2940b8d3478094e))
-   **frontend(kpi-evaluation:** adjust kpi evaluation list ([546b975](https://github.com/boring91/injaz/commit/546b9756a08024f1bc7fbce536bc9f82c7144d73))
-   **frontend(kpi-evaluation:** adjust kpi evaluation list ([a45e6fa](https://github.com/boring91/injaz/commit/a45e6fa2a07337a1500dc34f2d596c6875605a0d))
-   **frontend(misc-api):** adjust the string in the service mapper ([4a3c7c7](https://github.com/boring91/injaz/commit/4a3c7c7c4d8093e610fd0bb710a92bfed0cd860b))
-   **frontend(strategic-goals):** add description to service dropdown ([6d557fc](https://github.com/boring91/injaz/commit/6d557fcca353fd350cef5c0b04aa34e1156a433d))
-   **frontend:** add attachments popup in details page ([d71eb58](https://github.com/boring91/injaz/commit/d71eb58c04f762caa5305cc8e55ab8e0bc362725))
-   **frontend:** add change response approver dialog component ([61e4d6f](https://github.com/boring91/injaz/commit/61e4d6f85f4fd7e4ecaa84be8ff25228cdccbc7e))
-   **frontend:** add permission in kpi result entry ([62d8978](https://github.com/boring91/injaz/commit/62d8978a8df002fc5c64cd494a691af243618f40))
-   **frontend:** create permission flow ([1b0e343](https://github.com/boring91/injaz/commit/1b0e3431c7097743b31b7eee6333b52c720c4cd1))
-   **service:** add ability to show and search service name with its subs ([300779b](https://github.com/boring91/injaz/commit/300779bc11f2f9fe5edbea76521fe80b902c8bfb))

## [1.105.2](https://github.com/boring91/injaz/compare/v1.105.1...v1.105.2) (2024-10-08)

### Bug Fixes

-   temporary fix, handling failure in partnership activity period reminder ([7eacc84](https://github.com/boring91/injaz/commit/7eacc8423c1d72a82359e5dc51d59fc3a928030b))

## [1.105.1](https://github.com/boring91/injaz/compare/v1.105.0...v1.105.1) (2024-10-08)

### Bug Fixes

-   use scope factor in partnership activity period reminder hosted service ([c4032ce](https://github.com/boring91/injaz/commit/c4032cecc37ac8079d47a6f5c27e777746fb8e0b))

# [1.105.0](https://github.com/boring91/injaz/compare/v1.104.0...v1.105.0) (2024-10-07)

### Bug Fixes

-   **backend-attachments:** change AttachmentItems to AllowedFileTypes ([04aff74](https://github.com/boring91/injaz/commit/04aff74d804e9871354bb20578100ccea8c1880b))
-   **backend-attachments:** fix error FileTypeAttributeAdapter ([0c8cf33](https://github.com/boring91/injaz/commit/0c8cf33a588acd6190a67c6380fc3b88cf6953d9))
-   **backend-attachments:** fix migration ([26c79ad](https://github.com/boring91/injaz/commit/26c79ad4485334e0341f5fb6868875729e6e1e79))
-   **backend-attachments:** remove attachments_title and change attachments to attachment_items ([86238ea](https://github.com/boring91/injaz/commit/86238ea8408bd3c9b2ee8fe2bfebfe9988010de3))
-   **backend-attachments:** use model property error message instead of custom one ([42b9f0f](https://github.com/boring91/injaz/commit/42b9f0ff78d09b38ecccc452ed3a69fb10c9ed66))
-   **backend-frontend-attachments:** change in structure for filetypes ([de809c8](https://github.com/boring91/injaz/commit/de809c81ede7a4de75f51d1b879f235cfbd513df))
-   **backend:** add relational column order ([20dfc94](https://github.com/boring91/injaz/commit/20dfc947d51e6e96afa999bd8e4b26165beaa08c))
-   **backend:** remove useless tag ([6c5c5f7](https://github.com/boring91/injaz/commit/6c5c5f7c661505055edb97212ccd9984f587ddad))
-   **backend:** remove useless tags ([550c755](https://github.com/boring91/injaz/commit/550c755b2fde53510a1bd02b21083e0962ffb680))
-   **frontend-attachments:** change AttachmentItems to AllowedFileTypes ([9b5d2ab](https://github.com/boring91/injaz/commit/9b5d2abb654083190591887d6133733ecd0291de))
-   **frontend-attachments:** fix allowed file type issues ([02d18ec](https://github.com/boring91/injaz/commit/02d18ecae0858508f11cfdc486b1022ec9cc1081))
-   **frontend-attachments:** remove comment for attachment ([62852a0](https://github.com/boring91/injaz/commit/62852a08a861e1596032356594c0a5b46befdc01))
-   **frontend-setting:** enhance attachment in settings section ([91bbfd9](https://github.com/boring91/injaz/commit/91bbfd9f3392957e35d4ee1621c50f83ca1b166b))
-   **frontend:** fix all field attachment mime ([af1776c](https://github.com/boring91/injaz/commit/af1776c8e76d151188b3caee40c506ac98d3fb50))
-   **frontend:** fix attachment settings field types ([9acb276](https://github.com/boring91/injaz/commit/9acb2763a6fb85d13aacd5bebe2ed1fc45990000))
-   **i18n:** add missing translation ([e5d0105](https://github.com/boring91/injaz/commit/e5d010588aa272adf77cec37bee66406c879c342))
-   **i18n:** fix translation string ([bdfce67](https://github.com/boring91/injaz/commit/bdfce6750d7bcfa2731b70e32c57755984273527))
-   lint ([0563539](https://github.com/boring91/injaz/commit/05635393db8293603257fa78f77860f117f52ed2))

### Features

-   **backend-attachments:** add attachments in general ([7224979](https://github.com/boring91/injaz/commit/7224979e6a0617850bef5e7580d886c141732af4))
-   **backend-attachments:** modify filetype attachments ([f3ed45c](https://github.com/boring91/injaz/commit/f3ed45ca33197c075093bfcc79213df60020d9f2))
-   **frontend-attachment:** modify filetype attachments ([f32c237](https://github.com/boring91/injaz/commit/f32c2373d0be49b9afdd812d77e030e78fcde2fd))
-   **frontend-attachments:** modify-filetype attachment ([8848242](https://github.com/boring91/injaz/commit/8848242812519db7ca00f4ff5240edf10909dead))
-   **frontend-attachments:** refactor line in front-end ([a77ff73](https://github.com/boring91/injaz/commit/a77ff73d727075af7784fe21891f0e4e940c1792))
-   **frontend-attachments:** translate_attachment ([5213f4b](https://github.com/boring91/injaz/commit/5213f4b3e016fedcc9520684a351bd19f3f4f6ea))

# [1.104.0](https://github.com/boring91/injaz/compare/v1.103.0...v1.104.0) (2024-10-01)

### Bug Fixes

-   **backend-plan:** check for entity instead of entity id ([53a8209](https://github.com/boring91/injaz/commit/53a82094480d7cd18deefd8c1fa741fdf99d3e46))
-   **backend-strategic-goal:** fix strategic goal param ([5f4c59f](https://github.com/boring91/injaz/commit/5f4c59f3d921e4c6db82a00cf4cd9b2d77de517b))
-   **frontend-kpi-data-entry:** check for null or undefined ([279b1cc](https://github.com/boring91/injaz/commit/279b1cc35405344c044ba80e76cb96f2230dd1b4))
-   **frontend-kpi-data-entry:** check for null or undefined ([ead456b](https://github.com/boring91/injaz/commit/ead456b47e23d9ea7adcc2a4d572dc36942d8db1))
-   **frontend-operation:** fix conditions ([20430fb](https://github.com/boring91/injaz/commit/20430fb5b4fb531d52b6dbd15bfaab60191672fb))
-   **frontend-operation:** remove unused method ([8cd8b4f](https://github.com/boring91/injaz/commit/8cd8b4fc778e300ae2b2e2364dea6e27a07006ec))
-   **frontend-operation:** remove useless component ([592100d](https://github.com/boring91/injaz/commit/592100df643e83a0698a90ff479dbaca81446a74))
-   **frontend-strategic-goal:** enhance strategic goal ui and add missing translations ([1e0c629](https://github.com/boring91/injaz/commit/1e0c629527156ef9749a598b86446e94136e1603))
-   **frontend-strategic-goal:** fix linked kpis component ([acd66d2](https://github.com/boring91/injaz/commit/acd66d280981bb5269e668bec803c161f5db603f))
-   **frontend-strategic-goal:** fix shared list component ([4ee911c](https://github.com/boring91/injaz/commit/4ee911cb44fc48e1033c838fe6a3e8f675c13c1e))
-   **frontend-strategic-goal:** fix shared operation list component ([fb96c3e](https://github.com/boring91/injaz/commit/fb96c3eeeed476107c0921242ac8c94c817da9b8))
-   **frontend-strategic-goal:** remove services from strategic goal provider ([e8d617f](https://github.com/boring91/injaz/commit/e8d617ff9761bcf5e25d2fa87692c4662cacde92))
-   **frontend-strategic-goal:** use partial filter object ([6253f9b](https://github.com/boring91/injaz/commit/6253f9bcdf1fc77314267e13d8ae1bfd5412eb78))
-   **frontend-system-list:** handle optional chaining for mapperFn invocation ([2d8439d](https://github.com/boring91/injaz/commit/2d8439deef5bfd3fefb62ec0f564203761cd99a3))
-   **frontend:** add missing merge code ([8d30e3e](https://github.com/boring91/injaz/commit/8d30e3ee2887838a6fc7f08b137435685c1c07ff))
-   **frontend:** fix action buttons icons and labels ([abf531e](https://github.com/boring91/injaz/commit/abf531e206d7e649ca849e7efb24ba699882e272))
-   **frontend:** rename comments ([d38fd2e](https://github.com/boring91/injaz/commit/d38fd2ee5894423562390587fba164f57a1434dc))
-   github workflow ([8ae314d](https://github.com/boring91/injaz/commit/8ae314d5d321c0f634d2c476d19981fad2514089))
-   **i18n:** fix translation string ([1bad4de](https://github.com/boring91/injaz/commit/1bad4dee3a5ab7f3552d93d9f5798d861c7e3c11))
-   update changelog ([78cff3a](https://github.com/boring91/injaz/commit/78cff3a1633667f18b8745eb5f89a81de92bff25))
-   update changelog [skip ci] ([7fad6c6](https://github.com/boring91/injaz/commit/7fad6c66856dca4635e852fc7edb0fd1eafbdc57))
-   update workflow ([45e3b88](https://github.com/boring91/injaz/commit/45e3b88ff1e3e57243c84113d4c93e9b86da3ea8))

### Features

-   **backend-operation:** add strategyGoalIds filter ([e7d9e16](https://github.com/boring91/injaz/commit/e7d9e1612c13d032a993f61d42363b62715d9078))
-   **backend-strategic-goal:** add new endpoint GetStrategyGoalKpis ([f123db3](https://github.com/boring91/injaz/commit/f123db3ac3a3451d879b5864828405c7bb733ba7))
-   **backend-strategic-goal:** add new endpoint misc/plan-category-type ([ca0b4dd](https://github.com/boring91/injaz/commit/ca0b4ddd535f6c8c8bb635469ba2253c2fc0d6e0))
-   **backend-strategic-goal:** add new param categoryTypes in plan-list ([36ca9c5](https://github.com/boring91/injaz/commit/36ca9c51940762216273f435d20d4e1d12014053))
-   **backend-strategic-goal:** edit endpoint GetStrategyGoalKpis ([24ffb74](https://github.com/boring91/injaz/commit/24ffb74fc884e9d1ea34408660dc888db36c8cf8))
-   **backend:** add kpi result direct approve permission ([f63fb3a](https://github.com/boring91/injaz/commit/f63fb3a1fd4628a13e25c4f404f6d8c628e0cbe1))
-   **backend:** add translation ([b22e8c7](https://github.com/boring91/injaz/commit/b22e8c7a705477340f88d0e17c4353b703d3dd01))
-   **backend:** add translations ([58378e7](https://github.com/boring91/injaz/commit/58378e7a393c40d54c41409add4d1c25ec4b65a3))
-   **backend:** delete statistical report ([eae1403](https://github.com/boring91/injaz/commit/eae1403eb5c263d8675e95506e089ee56dfd530a))
-   **frontend-system-list:** create shared component for plan list and use it in system list ([3b7fa22](https://github.com/boring91/injaz/commit/3b7fa22abcca47de5d0d967b3cc8bae6c01a97c4))
-   **frontend:** add direct approve button ([55653a4](https://github.com/boring91/injaz/commit/55653a45e6337fa12cf3fdd8dd4fdbc5a0a8ed39))
-   **frontend:** add kpi result direct approve permission ([e73182f](https://github.com/boring91/injaz/commit/e73182f972703dcb5e34254dffedc05f0485cf30))
-   **frontend:** add Strategic operations ([2fd5370](https://github.com/boring91/injaz/commit/2fd5370e0d1a452efbf6fb94c1a9daf1eabf4907))
-   **frontend:** add Strategic operations ([ff76545](https://github.com/boring91/injaz/commit/ff76545cee0fb467a7e3bf2ba7374f2e28ab0d7c))
-   **frontend:** add Strategic operations ([3392188](https://github.com/boring91/injaz/commit/3392188845438b13d9dd455a4d53da8005e0d854))
-   **frontend:** add Strategic operations ([a263257](https://github.com/boring91/injaz/commit/a2632572d76c6b71171d317a216fcc3402106abe))
-   **frontend:** add Strategic operations ([9e32508](https://github.com/boring91/injaz/commit/9e325084dec94cecbd01765ccd6265dae30b8bed))
-   **frontend:** hide direct approval button when users selected ([c652487](https://github.com/boring91/injaz/commit/c652487b289950e573e99754ccc1054bc8a9a3d9))
-   **statistical-report:** add ability to update report if no result entered ([0c00ec9](https://github.com/boring91/injaz/commit/0c00ec93d92ee627e75cc438977d99fa8df7051c))

# [1.103.0](https://github.com/boring91/injaz/compare/v1.102.0...v1.103.0) (2024-09-23)

### Bug Fixes

-   **backend-database:** change and revert database migrations ([ac6e9eb](https://github.com/boring91/injaz/commit/ac6e9eb3e80051f269b92dc8cbeddb2a08193bbd))
-   **backend-operation:** prevent get list in memory on `operation-procedure` method ([344a483](https://github.com/boring91/injaz/commit/****************************************))
-   **backend-operation:** remove linked procedure from enhancements before delete procedure ([b6c4c64](https://github.com/boring91/injaz/commit/b6c4c640c5fba855b8daf9e29580d5d937c2577c))
-   **backend-operation:** use the correct dtos ([6340fc1](https://github.com/boring91/injaz/commit/6340fc158abb4d4cb5120cdf0c5baa854505875c))
-   **backend-risk:** leave only id and name properties on simple Dtos ([1489358](https://github.com/boring91/injaz/commit/1489358eed4c41907bdff85e2efcedeb5ef13c60))
-   **backend-risk:** remove useless property from dto ([8b0968d](https://github.com/boring91/injaz/commit/8b0968d5ab832a84a3ba7d1ca15025b394101a51))
-   **backend:** add missing database migrations ([7d19cb2](https://github.com/boring91/injaz/commit/7d19cb2928febbe2111f2d753f729fed7050aec9))
-   **backend:** fix bug in operation enhancement create and update ([75cfea8](https://github.com/boring91/injaz/commit/75cfea88bb592cc302b47368707f13833bc961b5))
-   **backend:** fix bug in operation enhancement edit dto ([403d932](https://github.com/boring91/injaz/commit/403d932e0d4bad5581dd542274bd21aee6f1a0fe))
-   **backend:** remove useless migrations ([169383b](https://github.com/boring91/injaz/commit/169383b098a70d4e2e0d1a209737ca115a6ba697))
-   **backend:** revert backend migrations ([0b33452](https://github.com/boring91/injaz/commit/0b33452841c71c1d7165729183e2c8487a149bb2))
-   **frontend-enhancement:** review comments ([6f20151](https://github.com/boring91/injaz/commit/6f2015100687ec48511f3ee002eb344ce571d177))
-   **frontend-mnm-form:** use tailwind classes instead ([2bd344b](https://github.com/boring91/injaz/commit/2bd344bca030610f76c25e4453bdbb7a000d7377))
-   **frontend-operation:** fix operation procedure table ([bc1f067](https://github.com/boring91/injaz/commit/bc1f0674419f1b52d185451161705c13086c4468))
-   **frontend-operation:** refresh operation details after delete procedure ([de67677](https://github.com/boring91/injaz/commit/de67677ab0405e5c6f2714cc593c23d98073dca9))
-   **frontend-system-list:** use type inside system list columns ([b217f16](https://github.com/boring91/injaz/commit/b217f168b82ced6d41df621ba0e32e42138d6e99))
-   **frontend:** update field name ([24ac700](https://github.com/boring91/injaz/commit/24ac700dc03d5372fbb73813af9f59c5f3339dd0))
-   **frontend:** update field name ([651b39a](https://github.com/boring91/injaz/commit/651b39a20c3741c406bb18601f066d70d47f2f27))
-   **i18n:** fix translation name ([8df750e](https://github.com/boring91/injaz/commit/8df750edca3583dd727bab0324fdd074a1899acd))
-   **operation:** rename `isDurationList` to `IsDurationCalculatedFromSteps` ([5482906](https://github.com/boring91/injaz/commit/5482906e08eb6879f744bd8c618a17369382d212))

### Features

-   allow the response data entry user to track it ([abc1313](https://github.com/boring91/injaz/commit/abc13131846ee3c2158347a05b4022f361668451))
-   **backend-operation-execution:** modify duration when add new execution ([fa2b724](https://github.com/boring91/injaz/commit/fa2b724c94e71c0fd78d994db2ceb95f7ece92a5))
-   **backend-operation-Procedures:** change ChkDurationList To IsDurationList ([79a138e](https://github.com/boring91/injaz/commit/79a138e36205e6eace629acd3fbb50b5fb9faf86))
-   **backend:** add description to risk category ([394ba9d](https://github.com/boring91/injaz/commit/394ba9d7f548ae463d9582894fc8647589e2a550))
-   **backend:** add description to risk category in create ([dfaa10b](https://github.com/boring91/injaz/commit/dfaa10b326a9881ee720a0883b8af05b188cde3f))
-   **backend:** add operation procedure to operation enhancement ([06fb940](https://github.com/boring91/injaz/commit/06fb940fa35f4ff1016169ddb6ba6ac0ca5362a5))
-   **backend:** add operation procedure to operation enhancement ([bae9eac](https://github.com/boring91/injaz/commit/bae9eac7a4dc42a91c89c232bc396b08acd29d24))
-   **frontend-kpi-data-entry:** hide action if the user can not approve response ([9573608](https://github.com/boring91/injaz/commit/9573608e5ff448ed7a8d3716f56681b2dcc5929d))
-   **frontend-mnm-form:** add description to the select ([bd6becb](https://github.com/boring91/injaz/commit/bd6becba51930a3a1330f844a8bd20e74e233e0d))
-   **frontend-operation-execution:** modify duration when add new execution ([dfd0e4c](https://github.com/boring91/injaz/commit/dfd0e4c3fe3bef6d53b80efd56a6af88fa7b7756))
-   **frontend-operation:** add new field in add enhancement and in edit enhancement called procedure ([a170541](https://github.com/boring91/injaz/commit/a170541301f5389f11e96b38cf7f7e9c4026dbf0))
-   **frontend-system-list:** add description to each column ([e413729](https://github.com/boring91/injaz/commit/e41372995aaa73884046acbd7bada82b600dd33f))
-   **frontend:** add pipe for description value factory ([f355027](https://github.com/boring91/injaz/commit/f3550275f59ac8602ca708bce65c89324ef8c175))

# [1.102.0](https://github.com/boring91/injaz/compare/v1.101.1...v1.102.0) (2024-09-19)

### Bug Fixes

-   **backend-department:** fix users count ([fe169e8](https://github.com/boring91/injaz/commit/fe169e8ee075a9634d1c6c5b2a39865060914a3b))
-   **backend-government-strategic-goal:** fix order by in government strategic goal ([c45be3c](https://github.com/boring91/injaz/commit/c45be3c54132c4a984adce1bfa157c4c5c9d36a3))
-   **frontend:** enhance code quality ([489d72c](https://github.com/boring91/injaz/commit/489d72c02a2724dfbfc8009ba4594db0bc91a24a))
-   **frontend:** enhance code quality ([ea1460f](https://github.com/boring91/injaz/commit/ea1460fe67788db56b20a2a571c9c1ff27182964))
-   **frontend:** enhance code quality ([66d2018](https://github.com/boring91/injaz/commit/66d201862777ecc10ac24244a32a9e6ce4d566c3))
-   **i18n:** fix translation string ([94ab932](https://github.com/boring91/injaz/commit/94ab9327713130cf54785239a507187919892503))
-   **i18n:** fix translation strings ([c84a5e8](https://github.com/boring91/injaz/commit/c84a5e8b10f904a3c1e31858ebf270d486db6195))
-   pagination in department members section ([45a97d4](https://github.com/boring91/injaz/commit/45a97d4e859abeee8ba3f570034c3eeff2234151))

### Features

-   **backend:** add to year and from year to government strategic goal ([95d7970](https://github.com/boring91/injaz/commit/95d7970e0e12b3426eb7920478b3a728d5766a4b))
-   **frontend-system-list:** add group by key and add mapper fn to the system list ([3030bcf](https://github.com/boring91/injaz/commit/3030bcfc5ef330e8da8998103822dc95c8e48d32))
-   **frontend:** create edit year in government strategic goal ([9dde80a](https://github.com/boring91/injaz/commit/9dde80a7dc67c06e8d003812700e8249d58d59a9))
-   **frontend:** create edit year in government strategic goal ([daa620a](https://github.com/boring91/injaz/commit/daa620ae056cea7194eaf373fdc4f503c3a3960a))
-   **service:** remove the validation of providers and hide it form front-end ([7f850ff](https://github.com/boring91/injaz/commit/7f850ffad63b1de37ec4d4ca7b2dc64bd835c024))

## [1.101.1](https://github.com/boring91/injaz/compare/v1.101.0...v1.101.1) (2024-09-19)

### Bug Fixes

-   **kpi:** exclude child departments as well in evaluation report ([c55be84](https://github.com/boring91/injaz/commit/c55be84f2352cf043934db9b110ed1d42037af2f))

# [1.101.0](https://github.com/boring91/injaz/compare/v1.100.1...v1.101.0) (2024-09-19)

### Bug Fixes

-   **backend-statistical-report:** add missing condition for locked statistical report ([f354054](https://github.com/boring91/injaz/commit/f354054eebbbb0287b2eef749611a4e7a73cd2f3))
-   **backend:** fix conflict ([1cfbfdf](https://github.com/boring91/injaz/commit/1cfbfdf2ba9adb85682315bb1158d1a1ca263322))
-   **department-dashboard:** fix all department dashboard statistics bugs ([e937e5e](https://github.com/boring91/injaz/commit/e937e5e790153249e2ca04fdbd9f5272f6585838))
-   **frontend-kpi:** enhance component UI ([e2ecaaf](https://github.com/boring91/injaz/commit/e2ecaafb348565f643ae6aa9c07aadbc509496ea))
-   **frontend-risk:** adjust the gauge to be max at 100 not 140 ([a4b5552](https://github.com/boring91/injaz/commit/a4b5552cabf3dd7bc0cd19d55a7c292e28d97a0d))
-   **frontend-risk:** enhance risk dashboard UI ([6a51d4b](https://github.com/boring91/injaz/commit/6a51d4be914e7411d3eeabb072813f620f8bf59f))
-   **frontend-statistical-report:** add a check if the status is null send it empty string in the list ([e19eb24](https://github.com/boring91/injaz/commit/e19eb24675db899485a86fbc59d9eb8306c1f101))
-   **i18n:** fix translation string ([e343757](https://github.com/boring91/injaz/commit/e343757258d19b229bffdfef460941e69cb5a576))
-   **i18n:** remove unused translation strings ([1799d49](https://github.com/boring91/injaz/commit/1799d49e9d9eeb8bb1bfa8e0780525d860dc4942))
-   **kpi:** fix some issues ([231e397](https://github.com/boring91/injaz/commit/231e397b2a94e33dde959918f3f42243cc4a5500))
-   **kpi:** rename section title ([008ad50](https://github.com/boring91/injaz/commit/008ad50559d301c2852064d558de17c8deeb66c7))
-   remove unused `orderBy` parameter ([1e939bb](https://github.com/boring91/injaz/commit/1e939bb0f43219b26d4e1bd927def6a0a42d16b5))
-   removing .toString ([12a8597](https://github.com/boring91/injaz/commit/12a8597723fb57f53bf76cae87f76d780208ee2d))
-   rename `tournamentId` to `tournamentIds` in standard team list method params ([0b91e96](https://github.com/boring91/injaz/commit/0b91e96d7c380c35e57acf85e29a03c84fe18c70))
-   tournament team selection ([40c4cfd](https://github.com/boring91/injaz/commit/40c4cfdce8abe25feea88f03eb449c98d63a5a5b))

### Features

-   add kpi ids filter to responses list ([631de8f](https://github.com/boring91/injaz/commit/631de8f905b75da877fdd8141763b1fdb8e77556))
-   **backend:** add counts to dashboard department ([34a87ae](https://github.com/boring91/injaz/commit/34a87ae51c70293ff0e82efc1d053aed5d7d2ff8))
-   **backend:** change statuses conditions ([feaa58b](https://github.com/boring91/injaz/commit/feaa58b40dd7aa5795841e5ff8b62418fc55253b))
-   **backend:** change statuses key to be status and allow null ([aee59bb](https://github.com/boring91/injaz/commit/aee59bb94387f5345d4d6d8b3bf86cebadd07f5a))
-   **backend:** convert statuses from array to single value ([c430d78](https://github.com/boring91/injaz/commit/c430d7835cc033944f61a7fd973d0f5ed0566fcf))
-   **backend:** statistical report statuses ([949054d](https://github.com/boring91/injaz/commit/949054d46b78e8c5212295485e435f74a385ff14))
-   **frontend-kpi:** add scroll to data entry departments and kpis tables in the details ([c4ab757](https://github.com/boring91/injaz/commit/c4ab757875806e5fc5253d94943607ad04432431))
-   **frontend-kpi:** replace the data entry requests with the responses in the kpi details ([6cbc951](https://github.com/boring91/injaz/commit/6cbc9511c6664db217315c4b756d83eac8e5a88e))
-   **frontend-statistical-report:** add navigation to the list with pre filter in statistical report ([493380a](https://github.com/boring91/injaz/commit/493380ab8a4d92932f559f8ead7e43509c6f2640))
-   **frontend-statistical-report:** add navigation to the rest of the boxes in the dashboard ([cdebebe](https://github.com/boring91/injaz/commit/cdebebe736701ebd94260906cb6f7aa7e1415605))
-   **frontend-statistical-report:** add new filter with status ([c5db704](https://github.com/boring91/injaz/commit/c5db704af139d589ff1a94f94c610fc8fb702711))
-   **frontend:** add changes in department page ([ede36e0](https://github.com/boring91/injaz/commit/ede36e04ee438283998a98231d6e7d122a1626fa))
-   **frontend:** add statistics ([0d14846](https://github.com/boring91/injaz/commit/0d14846c1a3cfa5c28a90be935a87b05db7c1beb))
-   **frontend:** add statistics ([8efde5a](https://github.com/boring91/injaz/commit/8efde5a2c57797450b46e8dc6669638beecc6aa9))

## [1.100.1](https://github.com/boring91/injaz/compare/v1.100.0...v1.100.1) (2024-09-18)

### Bug Fixes

-   **frontend:** partially fix the weird implementation of tree select component ([25ed5ce](https://github.com/boring91/injaz/commit/25ed5ce3f988631de94e969a0aea47ed285650b5))

# [1.100.0](https://github.com/boring91/injaz/compare/v1.99.0...v1.100.0) (2024-09-18)

### Bug Fixes

-   **backend-users:** change condition to return non deleted users ([d4069a1](https://github.com/boring91/injaz/commit/d4069a1ca895374468f894767f6ef411459b7b86))
-   **backend-users:** remove useless condition ([cd5a526](https://github.com/boring91/injaz/commit/cd5a526e7ed88d4441f6bb5b35629a621610e3f0))
-   **backend:** remove user query filter ([fe4d6c7](https://github.com/boring91/injaz/commit/fe4d6c766517a8f1286b539404321d41d98209ea))
-   **kpi:** only show selected kpi tags in evaluation report ([06a992f](https://github.com/boring91/injaz/commit/06a992fda50d582ae3d77b6cd05dfa190e70b8c5))
-   **kpi:** remove evaluate permission as overrider to export ([a7dc0fd](https://github.com/boring91/injaz/commit/a7dc0fde61345bc68fd4a49fa613bd3cd7f74ed4))

### Features

-   **backend:** add query filter to user ([e653fad](https://github.com/boring91/injaz/commit/e653fad5955051507fa5b7189e3d8f0eca58928e))
-   **kpi:** add excluded departments for evaluation report ([2b7f00b](https://github.com/boring91/injaz/commit/2b7f00bd5fba13be2449e2cfaf78ba01678e9e72))

# [1.99.0](https://github.com/boring91/injaz/compare/v1.98.4...v1.99.0) (2024-09-15)

### Bug Fixes

-   **kpi:** hide update buttons for period evaluation for users with no permissions ([c535d2d](https://github.com/boring91/injaz/commit/c535d2d804e803c4682731e752ec2f991675916a))
-   **kpi:** inconsistency for evaluation report columns ([c484c95](https://github.com/boring91/injaz/commit/c484c952f69636b6673bda1b18633e575efe1a46))

### Features

-   **kpi:** add creation year to evaluation report ([14f0114](https://github.com/boring91/injaz/commit/14f01147c8bc6694ee7ede3764156fd207cef93d))
-   **kpi:** add kpi tags to evaluation report ([488a1e9](https://github.com/boring91/injaz/commit/488a1e9685622f987ce6dec0205174e8c065a798))
-   **kpi:** sort kpis in evaluation report by department hierarchy code ([4a784a4](https://github.com/boring91/injaz/commit/4a784a4268f2ccbd52434e8f96c6c80880a12429))

## [1.98.4](https://github.com/boring91/injaz/compare/v1.98.3...v1.98.4) (2024-09-12)

### Bug Fixes

-   **kpi:** take average evaluation for result instead of last ([35a334e](https://github.com/boring91/injaz/commit/35a334e9fcab5ecb36fca20d06efc36a5ec3a847))

## [1.98.3](https://github.com/boring91/injaz/compare/v1.98.2...v1.98.3) (2024-09-11)

### Bug Fixes

-   **backend-kpi-evaluation:** include the `toYear` in the report result ([6c985fb](https://github.com/boring91/injaz/commit/6c985fbc6e6793d132819ea2d8d4282d5393cf1e))

## [1.98.2](https://github.com/boring91/injaz/compare/v1.98.1...v1.98.2) (2024-09-11)

### Bug Fixes

-   **kpi:** nullify evaluations before creation years & take last period evaluation ([55217fa](https://github.com/boring91/injaz/commit/55217faf4dc627ba6192698b32088f4b866bc21a))

## [1.98.1](https://github.com/boring91/injaz/compare/v1.98.0...v1.98.1) (2024-09-10)

### Bug Fixes

-   **backend-kpi:** use `SetDb` method in evaluation service to prevent using multiple contexts ([3817f88](https://github.com/boring91/injaz/commit/3817f88666c36ca751dc9815c1a05280f63b5c76))

# [1.98.0](https://github.com/boring91/injaz/compare/v1.97.1...v1.98.0) (2024-09-06)

### Bug Fixes

-   **frontend-statistical-report:** fix permission sidebar for statistical report ([36315b2](https://github.com/boring91/injaz/commit/36315b24a00dac450fcf5129b7b8ed593359e970))
-   **statistical-report:** fix statistical report list bug ([109d32a](https://github.com/boring91/injaz/commit/109d32a6ef9d270ef848c9142fa4bfcbc448bf03))

### Features

-   **backend:** add permissions to statistical report ([3930b33](https://github.com/boring91/injaz/commit/3930b3397ca661982abe3bb8821268351e39cb8d))
-   **backend:** add permissions to statistical report attachment ([46df64d](https://github.com/boring91/injaz/commit/46df64d7ca10ddde18cfae1970a6a43087572a36))
-   **backend:** add permissions to statistical report statistics ([cb97b8d](https://github.com/boring91/injaz/commit/cb97b8d529b281165e6262b7a110c103764cbf83))
-   **frontend:** add conditions for permissions ([8358f92](https://github.com/boring91/injaz/commit/8358f92f1a3307eae7bc90db8d4b1e564c4295bb))
-   **frontend:** add conditions for permissions ([44887a2](https://github.com/boring91/injaz/commit/44887a2025022dd0c4954664f7e65b69fed85b88))
-   **frontend:** add conditions for permissions ([3831e3f](https://github.com/boring91/injaz/commit/3831e3f158e8d6ebeba9ba587c09e8d3ca5c7b9f))

## [1.97.1](https://github.com/boring91/injaz/compare/v1.97.0...v1.97.1) (2024-09-03)

### Bug Fixes

-   **statistical-report:** fix statistical report permission bug ([aa6ad46](https://github.com/boring91/injaz/commit/aa6ad464098c2c074dc28d06a56811b503adc470))

# [1.97.0](https://github.com/boring91/injaz/compare/v1.96.3...v1.97.0) (2024-09-02)

### Bug Fixes

-   **backend-kpi:** return only active users ([bfe1f70](https://github.com/boring91/injaz/commit/bfe1f7048542e9689599f5c8de9e67f2e6435c39))
-   **backend-partnership:** add whitespace between words ([1b966cf](https://github.com/boring91/injaz/commit/1b966cff0ce402d0b430b1149f2d6755c91c67fc))
-   **backend-partnership:** rename missing properties ([e2ca329](https://github.com/boring91/injaz/commit/e2ca32923f6b5917d6840460e928edfc3b7786c1))

### Features

-   **backend:** add is value is null notification sent to partnership activity period ([56794e1](https://github.com/boring91/injaz/commit/56794e1ff6d98d928e1fe5bab2181f22b0acce44))
-   **backend:** send notification in partnership activity period ([2c5ed07](https://github.com/boring91/injaz/commit/2c5ed0799307ede016b17ca5d4c145df1bd67bd7))

## [1.96.3](https://github.com/boring91/injaz/compare/v1.96.2...v1.96.3) (2024-09-02)

### Bug Fixes

-   **kpi:** during evaluation export, set score to zero if no results were found for a given year ([e0dabe5](https://github.com/boring91/injaz/commit/e0dabe551b58202d50b29741b170f324ee95e9e2))
-   **kpi:** evaluation report averaging scores for none evaluated periods ([767e81f](https://github.com/boring91/injaz/commit/767e81fd379665bbb9d7f64c4e257ce607796770))

## [1.96.2](https://github.com/boring91/injaz/compare/v1.96.1...v1.96.2) (2024-09-02)

### Bug Fixes

-   **evaluation:** evaluation statistics pages throw exceptions ([5c42ea5](https://github.com/boring91/injaz/commit/5c42ea593099531bfc850435977a4edbd1af4e03))

## [1.96.1](https://github.com/boring91/injaz/compare/v1.96.0...v1.96.1) (2024-09-01)

### Bug Fixes

-   **kpi:** miscalculation of final evaluation result ([e388178](https://github.com/boring91/injaz/commit/e38817856d41e4b80886495985c8df844472aed7))
-   translations ([9d66691](https://github.com/boring91/injaz/commit/9d66691f6c7b9b4da6ec14312f04fc45f9fe388c))

# [1.96.0](https://github.com/boring91/injaz/compare/v1.95.2...v1.96.0) (2024-08-30)

### Bug Fixes

-   **evaluation:** disable writing to period evaluation if result is exempt ([cce4745](https://github.com/boring91/injaz/commit/cce474518d748ee7163a81b2ca208595b9cf8408))
-   **evaluation:** map score bands ranges to percentages in frontend ([5d0cb42](https://github.com/boring91/injaz/commit/5d0cb426147595f9410c7e22b65563e1d202ec48))
-   **evaluation:** optimize the calculation of total periods evaluations ([fb4ef65](https://github.com/boring91/injaz/commit/fb4ef651ffa1091d6445c4520ec1fbd113983fd2))
-   **evaluation:** optimized get evaluation score details sql function ([7bab275](https://github.com/boring91/injaz/commit/7bab27576bcd96fa536f95b303cd4b426a79b07a))
-   **evaluation:** serious bug for determining the evaluation details when fetching multiple periods ([7d43e3f](https://github.com/boring91/injaz/commit/7d43e3f698f4a876e893c5061eb22d5aec701126))
-   **kpi:** display correct average evaluation scores in report ([1443537](https://github.com/boring91/injaz/commit/1443537e4d0ee286519ecd0984f407a770abf993))
-   **kpi:** evaluation report score coloring ([23b94cb](https://github.com/boring91/injaz/commit/23b94cbd9c177522446c5e514a159016137978e1))
-   **kpi:** hide evaluations for exempted results on the result level ([1792cd0](https://github.com/boring91/injaz/commit/1792cd031cefd7392318dd61f66d850419d92d76))
-   **kpi:** kpi/result get endpoint crashes when one period does not have evaluation ([cc9c105](https://github.com/boring91/injaz/commit/cc9c105bd8398f5894e0c08200c8005406a7d760))
-   translation typos ([062c335](https://github.com/boring91/injaz/commit/062c335c31fa757152bd9a27146ae6f91b120ef0))

### Features

-   add ability to return kpis that are involved with the current user ([06151f5](https://github.com/boring91/injaz/commit/06151f510a52f7f396d31168a39620f723adebc8))
-   **evaluation:** add is exempt from evaluation for kpi results ([544ac9f](https://github.com/boring91/injaz/commit/544ac9ff158d76e47729b7dcbd805d7b6992166b))
-   **evaluation:** show entity count in list page ([f1a0f3c](https://github.com/boring91/injaz/commit/f1a0f3cb18713dc9fc1fda28136ad32afa41b65e))
-   **kpi:** add average scores for evaluation report ([91b81a0](https://github.com/boring91/injaz/commit/91b81a01ca5ac505dcbc5039a718a2a7d0a8b8f7))
-   **kpi:** add permission for reading period evaluations ([d4826ad](https://github.com/boring91/injaz/commit/d4826ad7b2ae55a786acc01a688e3b292de838f0))
-   **kpi:** zero evaluation scores by default ([e9bf5aa](https://github.com/boring91/injaz/commit/e9bf5aa84ccfa9cf8381096ea888272a2bf18155))

## [1.95.2](https://github.com/boring91/injaz/compare/v1.95.1...v1.95.2) (2024-08-29)

### Bug Fixes

-   query for fetching evaluation details ([fa4b421](https://github.com/boring91/injaz/commit/fa4b421726b8efbe2febc8efeb21e5583038cd6d))

## [1.95.1](https://github.com/boring91/injaz/compare/v1.95.0...v1.95.1) (2024-08-29)

### Bug Fixes

-   **backend-kpi-evaluation:** fix evaluation excel result ([c387630](https://github.com/boring91/injaz/commit/c387630751c6186a0b34557636fcdbe04ed3174c))
-   **evaluation:** show evaluation in staging server ([fdd58bb](https://github.com/boring91/injaz/commit/fdd58bb4ad208c9cf5ee56da614a45d7a525889d))
-   **kpi-evaluation:** fix kpi evaluation filters and some bugs in excel ([5ffe6b3](https://github.com/boring91/injaz/commit/5ffe6b3eef5e4765de75e2302d854fac363e822a))
-   search by kpi code in periods evaluation edit page ([4a84473](https://github.com/boring91/injaz/commit/4a844739625ca3f6492edaca60442c8901481d3b))

### Performance Improvements

-   **frontend-evaluation-report:** select default value for years in report to the current year ([7dc0121](https://github.com/boring91/injaz/commit/7dc0121cb382dd0231aa25719a18c35b497257c1))

# [1.95.0](https://github.com/boring91/injaz/compare/v1.94.0...v1.95.0) (2024-08-27)

### Bug Fixes

-   **evaluation:** incorporated evaluation id to the calculation of scores ([db7c3fb](https://github.com/boring91/injaz/commit/db7c3fb57278a0da498cf83e1841115cbe2d059e))

### Features

-   **evaluation:** add ability to link specific entities for each evaluation ([9ac4341](https://github.com/boring91/injaz/commit/9ac43416ddb57b1fd6199fc5dd0a3f27e2ff23a4))
-   **evaluation:** hide scores for disabled evaluations ([fe4a754](https://github.com/boring91/injaz/commit/fe4a754cfb262c4f4d45c1461f2e4fd1c7a2f6f6))

# [1.94.0](https://github.com/boring91/injaz/compare/v1.93.0...v1.94.0) (2024-08-23)

### Features

-   **evaluation:** ability to choose evaluation before evaluating entity ([fcb3442](https://github.com/boring91/injaz/commit/fcb3442323143a9c50fd8eb18474ad7d16866427))
-   **evaluation:** add ability to enable/disable evaluations ([3febeaa](https://github.com/boring91/injaz/commit/3febeaa28243c8bdf9ad14c234ad374e17a5a285))
-   **evaluation:** add ability to set default evaluation ([770fd18](https://github.com/boring91/injaz/commit/770fd1836ee12f212531c2f5b7fa021825743054))
-   **evaluation:** allow multiple evaluations for the same type ([077c8fa](https://github.com/boring91/injaz/commit/077c8fa19a0e451554b41592cc3a41c38fe25011))
-   **evaluation:** disable actions on disabled evaluation ([1e63c5f](https://github.com/boring91/injaz/commit/1e63c5ff02a884cb2d34b9dd1bd36b64774772da))
-   **evaluation:** select default evaluation in evaluation instance detail component ([c53783a](https://github.com/boring91/injaz/commit/c53783aac033e580466a2e01f5f11ee0b0162818))

# [1.93.0](https://github.com/boring91/injaz/compare/v1.92.1...v1.93.0) (2024-08-22)

### Bug Fixes

-   **backend-kpi-result:** show kpi result data from last year if exist ([57bc140](https://github.com/boring91/injaz/commit/57bc14022e4b863644b4ce1714a232410697852f))
-   **backend-partnership:** add missing await ([b4f7e53](https://github.com/boring91/injaz/commit/b4f7e53323086369cc00a2a21556e038457e0049))
-   **backend-partnership:** enhance partnership lock evaluation code ([d795340](https://github.com/boring91/injaz/commit/d7953405bdba6b2fd1c3cbb8406b90ce84612de7))
-   **backend-partnership:** set is evaluated locked to true by default and notify to user ([68decd4](https://github.com/boring91/injaz/commit/68decd4b9f4563e2e2f1377f36f1001936cb57b2))
-   **backend-team:** order team user link ([676556c](https://github.com/boring91/injaz/commit/676556c32af592effefd00c4fcc0328712dbcb73))
-   **ci:** add dotnet installation path ([84770b7](https://github.com/boring91/injaz/commit/84770b768b3ee1b93066582d570a8d8c3f71354e))
-   **ci:** install node js before building backend ([b1c15a8](https://github.com/boring91/injaz/commit/b1c15a8623fc37f087ddd26890a394ed2575356d))
-   **frontend-evaluation:** add missing origin for localhost ([0d12189](https://github.com/boring91/injaz/commit/0d1218936c0b05f17953ab58e0f7d285875c4831))
-   **frontend-partnership-contract:** enhance and fix some issues ([f41ceaf](https://github.com/boring91/injaz/commit/f41ceaf0b63dc7ad163ce21088a031ba0a10ad60))
-   **frontend-partnership-contract:** enhance and fix some issues ([b3f9831](https://github.com/boring91/injaz/commit/b3f98318c7b78f1fb10bfe964268b15217726aa0))
-   **frontend-statistics:** enhance UI issues ([b2ed1dc](https://github.com/boring91/injaz/commit/b2ed1dc7db514b562160dce5766a59219e0876ce))
-   **frontend-statistics:** fix statistics ui issue in dashboard ([0dc63fd](https://github.com/boring91/injaz/commit/0dc63fd8d0f9d41872afb5b1d6873eee20ab521c))
-   **frontend-table-list:** fix review comment ([d4b69e0](https://github.com/boring91/injaz/commit/d4b69e0e2e9753b5c534b145e0e2e7af715297bf))
-   **frontend-team:** enhance team user link ([ebffa06](https://github.com/boring91/injaz/commit/ebffa06d6a733a185eb4170a224982683ba731c5))
-   **i18n:** fix translation strings ([9d305c3](https://github.com/boring91/injaz/commit/9d305c35049325aca9d658fbabc9c80019fca24f))
-   **partnership-contract:** enhance and fix some issues ([edc428b](https://github.com/boring91/injaz/commit/edc428b6a835916b45a6a2c4c713e6a6dddb6b74))

### Features

-   **backend-kpi-result:** modification kpi result when change or add new year ([9ea8c8a](https://github.com/boring91/injaz/commit/9ea8c8aec88388ddfffdf3bdd8d9ed548aff98f6))
-   **backend:** add order by to partnership activity list ([2521947](https://github.com/boring91/injaz/commit/25219470558bc1e1bbde78079fad159a98bb8dcf))
-   **backend:** add year to partnership activity ([ffa68e3](https://github.com/boring91/injaz/commit/ffa68e3e5ab45793fcb82257109c725ae1b822a8))
-   **backend:** can not evaluate if the evaluation is lock ([896060f](https://github.com/boring91/injaz/commit/896060f81b4b7f6de43a9b270b06c283b5fadc9f))
-   **backend:** contract notification service and add annual If time frame is null migration ([749be7b](https://github.com/boring91/injaz/commit/749be7b95772d9da333b11d64962a1d8998535ea))
-   **backend:** migrate year of start date in partnership contract to year of partnership activity ([891402f](https://github.com/boring91/injaz/commit/891402f1a1e9b9418b027eaebff4c22892428910))
-   **backend:** partnership contract notification ([80ddafe](https://github.com/boring91/injaz/commit/80ddafe48daf6c5198eb34ccfb9018a54fc92e85))
-   **backend:** toggle partnership contract evaluation lock ([42f2f7a](https://github.com/boring91/injaz/commit/42f2f7a52032e7f285dd9750bc0059cf1404f73d))
-   **backend:** when the contract is terminated the evaluation is automatically open ([371fba2](https://github.com/boring91/injaz/commit/371fba224334a4a115f076d63334e17c3148ac87))
-   **frontend-notification:** adding new type to the notification model called partnership contract ([a6aa901](https://github.com/boring91/injaz/commit/a6aa9018a2adccf3026a835609a99a3c98f88c45))
-   **frontend-partner-contract:** adjust field in the add or edit ([ffc48e8](https://github.com/boring91/injaz/commit/ffc48e8e434869c8c69947ef3f6cefac288dabc9))
-   **frontend-partner-contract:** remove the save button in the dialog to remove the save ([9138e5a](https://github.com/boring91/injaz/commit/9138e5a3f3a4d077bdf095928ec181f9630aa5d8))
-   **frontend-partnership-contract:** add button to update the can evaluate flag ([26c140b](https://github.com/boring91/injaz/commit/26c140b633ffd92d023f719cf1b93b7a4c96fddb))
-   **frontend-partnership:** add sort to the activity api to be by year descending ([eb152db](https://github.com/boring91/injaz/commit/eb152db4c5b5195f6651031a346035ff91ca214d))
-   **frontend-police-permission:** add permission in evaluation nav items ([c75a838](https://github.com/boring91/injaz/commit/c75a838a5931bfa19e2cb3b0d568af4d0a4e63e4))
-   **frontend-system:** if no position show team member in team list ([2d63aec](https://github.com/boring91/injaz/commit/2d63aecfb6fad817a07c7ab00e202ff1f8e8dcfc))
-   **frontend-table-list:** add new field to add new communication form and make table list grouped ([a7e6b5b](https://github.com/boring91/injaz/commit/a7e6b5b8e954ce0b070e07fb98025712b1b7f4e4))

## [1.92.1](https://github.com/boring91/injaz/compare/v1.92.0...v1.92.1) (2024-08-08)

### Bug Fixes

-   **backend:** use `SaveChanges` instead of `SaveChangesAsync` to prevent modification issue ([7c7276a](https://github.com/boring91/injaz/commit/7c7276a6338609e8716b5114bcd37a463b8eef34))
-   **frontend-partnership:** fix partnership fields disable issue ([5792b9a](https://github.com/boring91/injaz/commit/5792b9a9a1576ba363d0a0a14fc51b109d0a19d1))

# [1.92.0](https://github.com/boring91/injaz/compare/v1.91.0...v1.92.0) (2024-08-06)

### Bug Fixes

-   **backend-operation:** fix some issues and enhance code quality ([3613217](https://github.com/boring91/injaz/commit/3613217a1678915ca24caffdfc9e4271920ee630))
-   **backend-partnership:** fix merge issues ([4da46ac](https://github.com/boring91/injaz/commit/4da46ac8734222f71ffe84d00fefe6e48091c4ef))
-   **backend-users-team:** modify-add-user-steam ([dff22e0](https://github.com/boring91/injaz/commit/dff22e024e9a2961d19d8db97e977c202f3633c9))
-   **backend:** fix bug in filter by date ([72fc0dd](https://github.com/boring91/injaz/commit/72fc0ddd4dd210f65f6a7c56666c091aa71755bc))
-   **backend:** partnership contract edit ([d62f68f](https://github.com/boring91/injaz/commit/d62f68fe9aca244a8511d3d11f388b8e0007fc56))
-   **backend:** pull request comments ([066cf0c](https://github.com/boring91/injaz/commit/066cf0cbf6254b795f09b618547f4cb7aa61579e))
-   **backend:** sort params ([80e0da9](https://github.com/boring91/injaz/commit/80e0da9368db31034aae355d274141cd08dfe2a3))
-   **dynamic-table-filters:** improve and enhance table list filters ([097f340](https://github.com/boring91/injaz/commit/097f340690476dbcfde2a4b26375e31c368fce08))
-   **frontend-goal-grid:** fixing a small issue in the after view init hook if there are no goals ([dd34ae2](https://github.com/boring91/injaz/commit/dd34ae2b95ae521cb12a6aef98d52ee06a6b56e1))
-   **frontend-misc:** fix wrong code ([2bef2ba](https://github.com/boring91/injaz/commit/2bef2badf6ccbbc9591939b925093e71a5c1e907))
-   **frontend-operation:** adding default to the version search ([ff54fda](https://github.com/boring91/injaz/commit/ff54fda2e783b00cda41e333322220b712342913))
-   **frontend-partnership:** remove unused status ([afb9f2b](https://github.com/boring91/injaz/commit/afb9f2b063eef086217cfdc7e9567577bc921204))
-   **frontend-partnership:** remove unused status ([37e10de](https://github.com/boring91/injaz/commit/37e10deadda43338d36b7e9326ef82000180b0dd))
-   **frontend-partnership:** show merge or create partner button in the correct status ([afaae5e](https://github.com/boring91/injaz/commit/afaae5e7882b017522a63bd1bc970aa255b5d7c7))
-   **frontend-plan:** fix `otherKpis` not showing bug ([7f1fdd4](https://github.com/boring91/injaz/commit/7f1fdd4cbafb4d48392ac64a8cf8fdb7f7bbedb1))
-   **frontend-risk-level-filter:** ascending risk level filter ([7d5a5a9](https://github.com/boring91/injaz/commit/7d5a5a96f0400d6310ab1d76a9c1a3b1e816ed21))
-   **frontend-risk-level-filter:** remove console.log from risk-list-full.component.ts ([78f7c35](https://github.com/boring91/injaz/commit/78f7c358bffcde9d3b59f37c7dc899cda7167909))
-   **frontend:** rename component folder ([b01268f](https://github.com/boring91/injaz/commit/b01268fd76530851506d07c671ddbf986a506f10))
-   **frontend:** use unused input parameter ([a262bb7](https://github.com/boring91/injaz/commit/a262bb714f59033b5628e5c5f802fd8e20f9bb03))
-   **i18n:** update translation string ([f17c713](https://github.com/boring91/injaz/commit/f17c713632a7567182cf192dd124bcce3692a629))
-   **i18n:** update translation string ([987f47e](https://github.com/boring91/injaz/commit/987f47e31d41a55e25d145f52355e8ba5a72a7d4))
-   **i18n:** update translation strings ([74e24ef](https://github.com/boring91/injaz/commit/74e24efa272dd2fbeeed0975df3eea7d58b851f7))
-   **partnership-contract:** fix some changes and enhance some code ([f65efac](https://github.com/boring91/injaz/commit/f65efac1e61ee79b10677f7d693a23b44390f2cb))
-   **partnership-contract:** improve partnership contract ([c53d1f1](https://github.com/boring91/injaz/commit/c53d1f1b9dccbe4186281d4f4bea8e69c42c679e))
-   **permissions:** add missing permissions to settings button and fix translations ([8ae6d40](https://github.com/boring91/injaz/commit/8ae6d4038288f43060c834bc6aef9a930b1c9aea))
-   update translation ([90b42ec](https://github.com/boring91/injaz/commit/90b42eced94879c31a39dea23130f4edfddd5a13))

### Features

-   **backend-improvement-opportunity:** permission improvement_opportunity_input ([54ac1c7](https://github.com/boring91/injaz/commit/54ac1c751113dfad034a910fed3f07c49daf9d77))
-   **backend-partnership:** remove `second_review` status from `partnership-termination` ([32702ac](https://github.com/boring91/injaz/commit/32702ac9a695ee2bcee99e77749f6fc1c4e96252))
-   **backend:** add filters to partnership contract ([4655bc6](https://github.com/boring91/injaz/commit/4655bc6562dbcf641585c5cce947147410f7d24a))
-   **backend:** add function to send notification and email in operation enhancement ([6e37fc5](https://github.com/boring91/injaz/commit/6e37fc50de7d118f954fb276db77188440c24a41))
-   **backend:** add send notification and email in operation enhancement ([73fca8c](https://github.com/boring91/injaz/commit/73fca8c5bc356bb158a4c3067bbcefec6da340d0))
-   **backend:** add version to operation ([bba6f1d](https://github.com/boring91/injaz/commit/bba6f1dbeb9e1243efdb96b5e153931532912846))
-   **backend:** add version to operation list dto ([8969a35](https://github.com/boring91/injaz/commit/8969a353ce76113fc392b43021972303585829a9))
-   **backend:** edit partnership flow ([fda2654](https://github.com/boring91/injaz/commit/fda2654783fcf700cefd681281bd6c875d4da189))
-   **backend:** edit partnership termination request ([587c2f0](https://github.com/boring91/injaz/commit/587c2f004051162508d9e0ff574672bd82d67153))
-   **backend:** merge partner in partnership flow ([5f15bdd](https://github.com/boring91/injaz/commit/5f15bddc50eaba62d5a4dbbf0446b40474c33357))
-   **backend:** partnership field multi select ([f8ee0ba](https://github.com/boring91/injaz/commit/f8ee0bae30265948e4619079b3915f0ddc2f9108))
-   **backend:** show kpi details if user has full access and the kpi is not active ([aecab76](https://github.com/boring91/injaz/commit/aecab76f78e43eb4282cc6c3d0885bd0222bb3e9))
-   **backend:** update partnership activity lock period ([10455bd](https://github.com/boring91/injaz/commit/10455bda8ce0e7a2395f9fd551bbb5640eff83e0))
-   **frontend-dynamic-page:** adding the option to open the actions as tabs in the table list ([331f1a5](https://github.com/boring91/injaz/commit/331f1a5b06de5bd143f26ef3ed5b67b99123fa63))
-   **frontend-improvement-opportunity:** translate permission improvement_opportunity_input ([0330400](https://github.com/boring91/injaz/commit/0330400a7dd9979257246bc59a0499b3ee79009f))
-   **frontend-notification:** redirect notification to operation for `improvement_enhancement` ([4dd1853](https://github.com/boring91/injaz/commit/4dd1853a77f45263c3a74ce1bf51923e96e1aab1))
-   **frontend-operation:** adding new filed in (add-edit-list) operation called operation version ([47e4adb](https://github.com/boring91/injaz/commit/47e4adbb7bfc83062bc4d13e93659d97f052eb58))
-   **frontend-partnership-contract:** add dynamic filters ([be5ab30](https://github.com/boring91/injaz/commit/be5ab301714a9bf16af29660301b4d5a7dd4a660))
-   **frontend-partnership-contract:** add dynamic filters ([3ad7360](https://github.com/boring91/injaz/commit/3ad7360fcd8eb364a9671167b0f6b23ce5816c72))
-   **frontend-partnership-contract:** adjusting the key name to be fields ([6f1897a](https://github.com/boring91/injaz/commit/6f1897a019175f055322c261a2db77bf4eb150dd))
-   **frontend-partnership-contract:** making the partnership field multi select in add/edit/details ([27c2c73](https://github.com/boring91/injaz/commit/27c2c7373cc484f8f438151f10a858e692dc9735))
-   **frontend-partnership:** adding a condition to the alerts in the details page ([f10cd0c](https://github.com/boring91/injaz/commit/f10cd0c7e3b12f77cc0c233946184a3ca7026fe3))
-   **partnership-contract:** add isLocked to activity periods ([f0a618f](https://github.com/boring91/injaz/commit/f0a618fd875ad39c722025651a474dec8db264e5))

# [1.91.0](https://github.com/boring91/injaz/compare/v1.90.0...v1.91.0) (2024-08-01)

### Features

-   **kpi:** add kpi result period evaluate export ([fee6b81](https://github.com/boring91/injaz/commit/fee6b810309fdf6d694dc89948de00722ed4e0cb))

# [1.90.0](https://github.com/boring91/injaz/compare/v1.89.0...v1.90.0) (2024-07-31)

### Bug Fixes

-   **backend-statistical-report:** remove statistical report testing project ([ecfc3d2](https://github.com/boring91/injaz/commit/ecfc3d2ff174c3c431a0461fd36b85fded7c7b4a))

### Features

-   **statistical-report:** add statistical report category result attachment ([fe991ae](https://github.com/boring91/injaz/commit/fe991ae0f2be09aacc948d7ea5fbb5e62343b652))

# [1.89.0](https://github.com/boring91/injaz/compare/v1.88.0...v1.89.0) (2024-07-30)

### Bug Fixes

-   **backend-evaluation:** change and translate message if there is no evaluation type ([e8a9f75](https://github.com/boring91/injaz/commit/e8a9f7576d8224c0a4722d9f062d775d78298006))
-   **backend-kpi:** check if kpi can be evaluated or not ([a31667c](https://github.com/boring91/injaz/commit/a31667c074d79a93a99d5a6211c2108fde5de822))
-   **i18n:** update translation string ([f3a901a](https://github.com/boring91/injaz/commit/f3a901a074d9a6b6f0db3fc527844ea2ba593fcb))

### Features

-   **kpi:** add kpi `IsExemptFromEvaluation` field ([5161e30](https://github.com/boring91/injaz/commit/5161e30cdf61e2287c87f6981ffa75f1cb9b5e05))

### Performance Improvements

-   **backend-kpi:** improve evaluate score query performance ([dd1f677](https://github.com/boring91/injaz/commit/dd1f67702c00ff9dc69259fba5350a9e7a1d1900))

# [1.88.0](https://github.com/boring91/injaz/compare/v1.87.0...v1.88.0) (2024-07-23)

### Features

-   **evaluate-kpi-result-period:** add evaluation to kpi result period ([669c1c8](https://github.com/boring91/injaz/commit/669c1c8ad02371e2ac37eb0262b7872c2c2ee1d7))

# [1.87.0](https://github.com/boring91/injaz/compare/v1.86.0...v1.87.0) (2024-07-23)

### Bug Fixes

-   **backend-operation:** check if the owner department exist or not ([f6f0d09](https://github.com/boring91/injaz/commit/f6f0d0935bc9b26ec931ff7e0ff96b47dc437180))
-   **backend-operation:** improve and enhance operation excel ([fa7752a](https://github.com/boring91/injaz/commit/fa7752af61ca733f093ca0ce5d454257ff2f847b))
-   **backend-operation:** rename property to match the right case ([eed9b79](https://github.com/boring91/injaz/commit/eed9b796457b1f4cff67ee58b305669d9ff3bf91))
-   **backend-risk:** use the correct dto ([f2858b6](https://github.com/boring91/injaz/commit/f2858b6a9bca6fcc53111479ee8da5a77214f55e))
-   **backend:** fix migration table bug ([b74b080](https://github.com/boring91/injaz/commit/b74b080fbe11c4c9eb528370f00375b6c0ec6ab7))
-   **backend:** fix migration table bug ([5579fa5](https://github.com/boring91/injaz/commit/5579fa5014be036e73cf2c476e47f95882ef711f))
-   **backend:** remove all extra fields from `SimpleDto` ([b6c0e92](https://github.com/boring91/injaz/commit/b6c0e92965838b8f97bf32cf066223ce999d0065))
-   **backend:** remove page number and size from plan list report excel ([b2b5afe](https://github.com/boring91/injaz/commit/b2b5afe8070450938cce24fee569cd03e9bcf751))
-   **backend:** use the constant variable instead of string ([f81376e](https://github.com/boring91/injaz/commit/f81376e99fb851bd11fafd7896f8f8e46d7008a2))
-   **frontend-plan:** return back deleted code ([becff2d](https://github.com/boring91/injaz/commit/becff2d7333b8c9a3463092175a36552384f26dd))
-   **frontend-plan:** use `planRead` permission for export instead of `planWrite` ([0a7f92f](https://github.com/boring91/injaz/commit/0a7f92fb935f4ef23be003109984042c75532e49))
-   **frontend-plan:** use `textarea` instead of input of type `text` ([387c3de](https://github.com/boring91/injaz/commit/387c3dee299b28983a71f922882a6ac1941c80c5))
-   **frontend-system-event:** add missing `plan-subtask` link to system event list page ([7803b95](https://github.com/boring91/injaz/commit/7803b95b6368bbee16f22355ab068e04a640b293))
-   **frontend:** fix some issues ([688017f](https://github.com/boring91/injaz/commit/688017fdf6fd9356b0622f3d82d271fd755c59ee))
-   **frontend:** use template file instead of inline template ([3a2c95c](https://github.com/boring91/injaz/commit/3a2c95c79028c0f9891cf500efda95141fd36935))
-   **i18n:** add missing translation string ([a2d4363](https://github.com/boring91/injaz/commit/a2d4363de0132abe5ffe97b4196971b5d2e91d66))
-   **i18n:** update some translation strings ([b2ae017](https://github.com/boring91/injaz/commit/b2ae017319a8328cecc23c8073738d72e4737d55))
-   **i18n:** update translation strings ([8ed0b65](https://github.com/boring91/injaz/commit/8ed0b6528e8c703cbc88f668338b014a1f39cae0))
-   **operation:** fix operation periodicity issue ([18deec4](https://github.com/boring91/injaz/commit/18deec47d06082dc7203bc19f6336925e854b2ca))
-   **operation:** remove `periodicity` from `plan` and add it to `operation` ([d37e562](https://github.com/boring91/injaz/commit/d37e562be3db4caec4a755599ef1f0a3989858b9))
-   **risk:** rename `risk_acceptance_level_category` to `risk_acceptance_level_categories` ([a9fec59](https://github.com/boring91/injaz/commit/a9fec591e9c15dce2d888bdbccb0f35bdf255e0d))

### Features

-   **backend:** add periodicity to operation procedure table ([52072f7](https://github.com/boring91/injaz/commit/52072f78732ffe9b50d6334cbad9ab8b7405e027))
-   **backend:** add set hyper link method in base excel file ([f4a19dc](https://github.com/boring91/injaz/commit/f4a19dcb753fc9b90f0a7882f3a4964976ded0bb))
-   **backend:** export operation excel ([400818e](https://github.com/boring91/injaz/commit/400818e5d58edef746f91c2f20e818b06bc0bd3f))
-   **backend:** plan list report excel generator ([f058fc8](https://github.com/boring91/injaz/commit/f058fc8d93fec44a294645ed8735f84a58d9d54a))
-   **backend:** plan list report excel translation ([dd546b5](https://github.com/boring91/injaz/commit/dd546b56b2245629ab15d6630d04439aecb444b9))
-   **frontend-operation:** adding export button the operation list ([67e4a9b](https://github.com/boring91/injaz/commit/67e4a9bf2782c149acea2984fc1b3e4bfb40c3fd))
-   **frontend-plan-subtask:** adding new field called procedure periodicity to the add plan subtask ([2b2ef0d](https://github.com/boring91/injaz/commit/2b2ef0d4a0ebbe808f454a8f6d3e45ce7fac79b0))
-   **frontend-plan:** adding export button the plan listing ([17e279d](https://github.com/boring91/injaz/commit/17e279dd905efc9a4fb75bdde36b746cbf425fe1))
-   **frontend-risk:** add new select tree dropdown ([2bf5192](https://github.com/boring91/injaz/commit/2bf5192709e890dc70b485a452ad974cc84c0660))
-   **frontend-risk:** group strategic goals grouped by year ([585df45](https://github.com/boring91/injaz/commit/585df459f53feb1fe38ff4bd03a7f3e2ce716856))
-   **frontend-risk:** show degree with name in misc list ([9f6341c](https://github.com/boring91/injaz/commit/9f6341ce0cb1b08456f72e9ce66c017b617be7b6))
-   **frontend/subtask-detail:** use details component as dialog and page ([36905c1](https://github.com/boring91/injaz/commit/36905c1858a47f12098733ac7dead9beb5987172))
-   **risk-acceptance-level-category:** add new dynamic dropdown `risk-acceptance-level-category` ([8c0f809](https://github.com/boring91/injaz/commit/8c0f8091fc8c4bf276f3b3b92d3ad1e2f02c2f22))
-   **risk-management-strategy:** add new checkbox field to risk management strategy ([85079f8](https://github.com/boring91/injaz/commit/85079f8eeffc382c279b29d258ba99001a344b86))
-   **risk:** add after mitigation fields to risk ([2c60ed3](https://github.com/boring91/injaz/commit/2c60ed3c851469463f37713248c28f8c9643c776))
-   **risk:** add fields to risk settings ([6168113](https://github.com/boring91/injaz/commit/61681138cac2818b0415b8ec3a6b14aa4bb675da))
-   **risk:** add new risk type field ([327a313](https://github.com/boring91/injaz/commit/327a313c94406fd722f5848febb0c699d63a352b))
-   **risk:** link risk with risk acceptance level category list ([cded83a](https://github.com/boring91/injaz/commit/cded83a9797386b34d69f63ff324fe4f8f998887))
-   **risk:** show only operations for the selected owner department ([f1c0cf5](https://github.com/boring91/injaz/commit/f1c0cf5e21106ec30063bf26155a1bc662773418))

# [1.86.0](https://github.com/boring91/injaz/compare/v1.85.1...v1.86.0) (2024-07-22)

### Bug Fixes

-   **backend-kpi-result:** fix and enhance removing kpi result responses on change measurement cycle ([42a3f33](https://github.com/boring91/injaz/commit/42a3f3390e9622c05e2f5925544c337540d2ca26))
-   **backend-kpis:** :wrench: remove kpis data responses when make edit and change MeasurementCycle ([5bc8443](https://github.com/boring91/injaz/commit/5bc844337c588b74464fc8e2c28c3dd4da07cf98))
-   **backend-migrations:** fix migration file snapshots ([18dbcdb](https://github.com/boring91/injaz/commit/18dbcdbd9f72853b1c62e8ce8d13d519e98655c1))
-   **backend-partner:** enhance partner excel file ([3ec61cf](https://github.com/boring91/injaz/commit/3ec61cff8f22495acb3e491fc53a5ce00fd0eaab))
-   **backend-partner:** export partnership evaluations list ([753af93](https://github.com/boring91/injaz/commit/753af93410b08984f2a0dfa6ebe4bc2c8c0d69ff))
-   **backend-partnership:** fix export excel bug ([c8d4502](https://github.com/boring91/injaz/commit/c8d45021a26fde6f59e9aa1740a10ee934b4b597))
-   **backend-partnership:** improve excel generator code ([9eda745](https://github.com/boring91/injaz/commit/9eda745b71ce91d1b3a0247d6aced7cc1b9bb907))
-   **backend-plan:** fix total plans and tasks count ([53e8d47](https://github.com/boring91/injaz/commit/53e8d47c5e381804cc9c56c3bc2f32925b67cd9e))
-   **backend-risk:** add missing field on create risk `Causes` ([6a896ff](https://github.com/boring91/injaz/commit/6a896ff1852b2bb33125c7ae00862ea45acbc60f))
-   **backend-risk:** enhance, improve and fix risk new generation code and order ([9c1c011](https://github.com/boring91/injaz/commit/9c1c011c5bfab2406591ef486b10354d0022f771))
-   **backend-service:** rename service link property ([fa64da7](https://github.com/boring91/injaz/commit/fa64da781417a5650797a8b5154a9b57913ada59))
-   **backend:** enhance controller attributes ([682bece](https://github.com/boring91/injaz/commit/682bece279b75f291369269c4ae2041c21688a61))
-   **backend:** remove redundant namespaces ([5bd72b1](https://github.com/boring91/injaz/commit/5bd72b12d9b2b39a11d022baab28a5db9b035a55))
-   change upload icon from fa-file to be fa-paperclip in both capability list,linked files button ([cf18150](https://github.com/boring91/injaz/commit/cf181502e682d8749bffb55e91a2b4f295ed51ae))
-   **frontend-kpi:** adjusting the permission in the kpi details ([a3eee2b](https://github.com/boring91/injaz/commit/a3eee2b354a12c6d78088f1bca5de1ae9879ad51))
-   **frontend-operation:** fix some warning ([27338a5](https://github.com/boring91/injaz/commit/27338a5a12f08905ccd4652cb1830f552437a4c9))
-   **frontend-operation:** fix try parse terminologies logic ([eb5faf2](https://github.com/boring91/injaz/commit/eb5faf27ec9c50df8a74b29be990a32509d69f55))
-   **frontend-operation:** fix try parse terminologies logic ([de8a02c](https://github.com/boring91/injaz/commit/de8a02c154d09819c8999c03ffaea7462ecf8c70))
-   **frontend-operation:** fixing the translation key in case of editing enhancement ([5a20ba4](https://github.com/boring91/injaz/commit/5a20ba4a34fca8f065a8d199a6cbdd731c5690cd))
-   **frontend-operation:** return back the page title ([8e880ee](https://github.com/boring91/injaz/commit/8e880eeba0c72152632e29a057803dc760d4fc11))
-   **frontend-operation:** use `pageSubtitle` instead ([a05ec2b](https://github.com/boring91/injaz/commit/a05ec2b0d143753ac464c845a13b3daa088365c5))
-   **frontend-partner-detail:** fix partner error in console ([4e651ae](https://github.com/boring91/injaz/commit/4e651ae58066894c94ebbbb4854657945e71cf88))
-   **frontend-partner:** add missing permission to export button ([a47ca10](https://github.com/boring91/injaz/commit/a47ca1026eda036df811669eb41d354a2d976d8d))
-   **frontend-risk:** remove unused properties from risk details ([73aefe3](https://github.com/boring91/injaz/commit/73aefe312c6350a7421c6c56530c9db470228747))
-   **frontend-risks:** add missing properties ([564e0a7](https://github.com/boring91/injaz/commit/564e0a788bf288a9cfb1d0b2ba4f5cd23a39fe01))
-   **frontend-service:** adjusting the key in service model to be operation service ([47bd00e](https://github.com/boring91/injaz/commit/47bd00e65665e78bece67cab3cbc8ec136acb500))
-   **frontend-service:** adjusting the key in service model to be operation service ([1f018cd](https://github.com/boring91/injaz/commit/1f018cd355b150a8aaf3ba8b02de7397b3e73286))
-   **frontend-service:** adjusting the key name in service details ([109ad4e](https://github.com/boring91/injaz/commit/109ad4e0c1786362a31e90bdd55db621bd379837))
-   **frontend/sidebar:** fix sidebar bug when logged in ([33b7310](https://github.com/boring91/injaz/commit/33b73103d4a2205dcb692e0eda892d2b6334afa6))
-   **frontend:** fix `pushToArray` bug ([558561e](https://github.com/boring91/injaz/commit/558561edc0baf4dfb0d577a13d3b872fabaa79c1))
-   **frontend:** fix permission issue ([904edff](https://github.com/boring91/injaz/commit/904edff5a1280d8463a1769b7505e522cecec820))
-   **frontend:** solve permission issues ([375ee67](https://github.com/boring91/injaz/commit/375ee6742582f7d126f0926a1426a2acc6565110))
-   **i18n:** move translation to `app` folder ([81948ce](https://github.com/boring91/injaz/commit/81948ce40faf194ca995fc95cc563b4cfa88436b))
-   rename `serviceOperations` to `operations` and `linkedServices` to `services` ([d63720a](https://github.com/boring91/injaz/commit/d63720ae052564d72ac988f7b7353b05479c2106))
-   replace string to the correct one ([efe65a0](https://github.com/boring91/injaz/commit/efe65a04d2f16f3b9b847f1663261b05d53fbde1))
-   **service:** add missing properties and rename some properties ([ab5e612](https://github.com/boring91/injaz/commit/ab5e612422bfd368ab1a9c7649f26674e9ddf952))
-   update risk translation keys ([47165d5](https://github.com/boring91/injaz/commit/47165d5d2e8fc7518ce8ece42e307e18f34a95c6))

### Features

-   **backend-operations:** add a field to select the services on the fourth level data update ([33a84f1](https://github.com/boring91/injaz/commit/33a84f1766d8bd3ca76a81d6a31ab04922cc6b7f))
-   **backend-operations:** add a field to select the services on the fourth level data update ([921b018](https://github.com/boring91/injaz/commit/921b01818470bbb205111309328bc672b7bf6c95))
-   **backend-operations:** add a field to select the services on the fourth level data update ([b7ba345](https://github.com/boring91/injaz/commit/b7ba3455ac3d2a7096e1434e1f9774ed79be1b03))
-   **backend-operations:** add a field to select the services on the fourth level data update ([a36c7ee](https://github.com/boring91/injaz/commit/a36c7ee2f70e4bb3882320a40585dfd50ef0442e))
-   **backend-plan:** check plan year with the year of start date ([65c7157](https://github.com/boring91/injaz/commit/65c71577a64307deed4fc4a57f197016c5225743))
-   **backend-Risk:** add new fields for risk management procedure ([f851468](https://github.com/boring91/injaz/commit/f851468b1ebbf891680ae203c694669809bc94c5))
-   **backend:** benchmark permission with involved department ([c21942f](https://github.com/boring91/injaz/commit/c21942f7e5720dbf2a290f4cbb3fc0395d256d56))
-   **backend:** changes in export partner evaluation ([bbdc1a5](https://github.com/boring91/injaz/commit/bbdc1a5e0f8ac1d808106e613b475ae0ce447651))
-   **backend:** export partnership termination request export ([50d1dff](https://github.com/boring91/injaz/commit/50d1dff0ddf2905abf708c4c7382c7ca40bd9eff))
-   **backend:** get count for plans and tasks in department and sub departments ([76b623c](https://github.com/boring91/injaz/commit/76b623cabd54fa186cee07d7fc60bf1c12fb26ea))
-   **backend:** partner evaluation export ([7a2f362](https://github.com/boring91/injaz/commit/7a2f3625ef9b050c47da27ea6a21c6b9e53541ff))
-   **backend:** partner evaluation export policy ([5ad159c](https://github.com/boring91/injaz/commit/5ad159c9532834c512d4af90c304c8b18065d1b6))
-   **frontend-operation-service:** adding a field in level four operation ([9cbefc7](https://github.com/boring91/injaz/commit/9cbefc7c104c3e5590ca0621ffe0541f3cc446f9))
-   **frontend-partner:** creating new entity in the list section interface for the actions ([d462a97](https://github.com/boring91/injaz/commit/d462a972449f16b85e6e452c9be98acae88f3f8a))
-   **frontend-risk:** adding 2 api calls for risk code and order in new risk ([cfb9a0d](https://github.com/boring91/injaz/commit/cfb9a0dbfef40ad98ec2d842e7e9276739a939f9))
-   **frontend-risk:** adding 2 extra fields in the add risk modal ([1c6c1db](https://github.com/boring91/injaz/commit/1c6c1dbc3d8bf2f7200e5f02ca4b191392d3a747))
-   **frontend-risk:** adding the 2 new fields to the list ([7dce50d](https://github.com/boring91/injaz/commit/7dce50d9c8c9a664f0bff0d3a7052a5d9ea03925))
-   **frontend-team:** adding new component non team members ([bd0ae09](https://github.com/boring91/injaz/commit/bd0ae09baa4bc90a2a21320aa52f76fe303774cc))
-   **frontend/fourth-level-title:** add fourth level title ([a4726ce](https://github.com/boring91/injaz/commit/a4726ce1a3bbee15070735d5fa672103cb41166d))
-   **frontend/sidebar:** remove image from sidebar when collapsed ([d2c1b70](https://github.com/boring91/injaz/commit/d2c1b703e8834752cfacb8a5531fedc37f2ca6cf))
-   **frontend:** adding a class to make the select in the modal overlay the modal ([0cabd64](https://github.com/boring91/injaz/commit/0cabd64874629adcfa565adac0e790aa5947a84c))
-   **style/operation-terminology:** change style of terminology in operation details page ([a12ed1c](https://github.com/boring91/injaz/commit/a12ed1c357bc96e5cc9b23f4b1cb8b9cde312f52))

## [1.85.1](https://github.com/boring91/injaz/compare/v1.85.0...v1.85.1) (2024-07-17)

### Bug Fixes

-   **backend-plan:** remove plan year validation from backend ([3572b2b](https://github.com/boring91/injaz/commit/3572b2be58f83e0d83934e6c7adafe20bc35b686))
-   **frontend-plan:** add validation plan `from` year in frontend ([3b24ed7](https://github.com/boring91/injaz/commit/3b24ed70a9d5b255dcf08fd314f01c49c98a9e1d))
-   **frontend-plan:** add validation plan `from` year in frontend ([60450ff](https://github.com/boring91/injaz/commit/60450ff65ed15265807a5f73f94249f07678c00c))
-   **frontend-plan:** fix `from` and `to` plan dates to save it without timezone ([8f0e176](https://github.com/boring91/injaz/commit/8f0e1763c0fc75749aee5e03eee609f92d4fc8e2))

### Reverts

-   **frontend:** return back all things related to date and time ([cff8e13](https://github.com/boring91/injaz/commit/cff8e130cc6b9300c6614c598cce8101612395b9))

# [1.85.0](https://github.com/boring91/injaz/compare/v1.84.3...v1.85.0) (2024-07-08)

### Features

-   **frontend-statistical-report:** show table result in table list ([899b752](https://github.com/boring91/injaz/commit/899b752846c8ba7fd84dfb25f660d4f5d16e3ec8))

## [1.84.3](https://github.com/boring91/injaz/compare/v1.84.2...v1.84.3) (2024-07-05)

### Bug Fixes

-   **frontend:** remove all rights are reserved label from footer ([dab407a](https://github.com/boring91/injaz/commit/dab407aa5914c9de4f7a12c21dbe8a3a9c03243a))

## [1.84.2](https://github.com/boring91/injaz/compare/v1.84.1...v1.84.2) (2024-07-04)

### Bug Fixes

-   **frontend:** downgrade flatpickr package ([6cebbdb](https://github.com/boring91/injaz/commit/6cebbdb6367a18c8462a1d635d0bb64d0983b218))
-   **frontend:** fix flatpickr date ([ce5b36c](https://github.com/boring91/injaz/commit/ce5b36c4d5467c617d5e9406fa84c810d4fce148))

## [1.84.1](https://github.com/boring91/injaz/compare/v1.84.0...v1.84.1) (2024-07-04)

### Bug Fixes

-   **frontend:** date time shows disabled dates ([ebf30f6](https://github.com/boring91/injaz/commit/ebf30f69e211c8b5f27bbcb2c8a528e7275d70e2))

# [1.84.0](https://github.com/boring91/injaz/compare/v1.83.0...v1.84.0) (2024-07-04)

### Bug Fixes

-   **backend-dashboard:** fix comparison operator in `DashboardMainController` for `strategicGoals` ([3a57241](https://github.com/boring91/injaz/commit/3a572410d9e956f8d8ae5958b47457d7506ecbac))
-   **backend-operation:** rename and use the is involve with department in list by parent ([a58adbf](https://github.com/boring91/injaz/commit/a58adbfd275da9bc309c72db6873fab9191af511))
-   **backend-plan:** rename properties in get and edit dtos ([b5ebe3c](https://github.com/boring91/injaz/commit/b5ebe3c2d8e0f4d52142dd1a2a83c161d3e2c2f0))
-   **backend-plan:** update translation ([6e79687](https://github.com/boring91/injaz/commit/6e79687e72aaa709494ca5f201d36d6c6bfba548))
-   **backend-system-event:** prevent return records that has no created by user id ([85bbdff](https://github.com/boring91/injaz/commit/85bbdff24dcc61df4ea3449dfbeb3e58c760f7b6))
-   **backend:** add department permissions to operation ([330d782](https://github.com/boring91/injaz/commit/330d782f310fb2392fde86d785a7fd4f9bc615cc))
-   **backend:** fix bugs in get period for edit and update subtask ([af1634b](https://github.com/boring91/injaz/commit/af1634b139a31ee16f9f20e2429458684b694b31))
-   **backend:** fix progress value in subsubtasks if approval type is final ([3dd423d](https://github.com/boring91/injaz/commit/3dd423d97f43165f5d1fcfa218205ed370de700b))
-   **backend:** fix translation strings ([22af243](https://github.com/boring91/injaz/commit/22af24366d12b0ef7cb777ce3bf53c643f7df6fb))
-   **backend:** rename plan future plan files ([3930e3a](https://github.com/boring91/injaz/commit/3930e3a967280fa05c581b990f4eba4b719bab6b))
-   **frontend-kpi-result:** fix periods target bug on fill form data ([002135a](https://github.com/boring91/injaz/commit/002135a56873e72828993a94338d3c46ac42e2f3))
-   **frontend-notification:** enhance notification UI ([39986de](https://github.com/boring91/injaz/commit/39986decc152653d613fdb162b78d39597732317))
-   **frontend-notification:** fix scss issues ([80674a0](https://github.com/boring91/injaz/commit/80674a07f254f984cad8ef42903e12bbf5395c7b))
-   **frontend-plan:** hide other partners if its disabled from system settings ([3a4089c](https://github.com/boring91/injaz/commit/3a4089cf9eb5e95b024831f84e3af3c4a69231fa))
-   **frontend-plan:** round total weight number to 2 decimals max ([8cafb1d](https://github.com/boring91/injaz/commit/8cafb1dce26df3909216ed18c0de9768751d7487))
-   **frontend-plan:** show alert only when plan is loaded and if total weight is less than one ([92c6109](https://github.com/boring91/injaz/commit/92c610956700f5ed78023aa6deb37ab33cebb315))
-   **frontend-plan:** show button if user has ability to edit the field ([51b99fc](https://github.com/boring91/injaz/commit/51b99fcc040c4e9c2c393dedcc5817623275a23a))
-   **frontend-plan:** show no partners text if there is no partners and add missing translation ([d4770fb](https://github.com/boring91/injaz/commit/d4770fb255f9d0b6c9fa1d7f94c1196711c7123b))
-   **frontend-progress-bar:** fix `progress-bar-input` component bug if value is undefined ([82345ec](https://github.com/boring91/injaz/commit/82345ec0a15f2b321b087afa215f007eb6265f9a))
-   **frontend-system-event:** prevent show link if the entity has no page ([64c513e](https://github.com/boring91/injaz/commit/64c513ea2a4ec697cc2c2cd8137363169e4494e8))
-   **frontend:** remove some comments ([122da7d](https://github.com/boring91/injaz/commit/122da7d6b92ca9631a16b4444d4560255230e2de))
-   **plan:** fix some bugs :) ([cd2e0c5](https://github.com/boring91/injaz/commit/cd2e0c5a1adb842f8577ae5b411848df0e3f2123))
-   **system-events:** skip modification records where user id is equal to null ([daefafc](https://github.com/boring91/injaz/commit/daefafc1348bb73aea5f86dd93858cb35727ef4d))

### Features

-   **backend-plan:** check plan year with the year of start date ([6d45cb3](https://github.com/boring91/injaz/commit/6d45cb3fa13d65a18719d3d22ec3c37f30e628e2))
-   **backend-statistical-report:** show statistical report cycle in statistics ([071711e](https://github.com/boring91/injaz/commit/071711e0267a92f9065b5a3ba219382a61df55db))
-   **backend:** add future plan dynamic list ([2ba3c33](https://github.com/boring91/injaz/commit/2ba3c33bf76858413c425c0255a24590f5a540c9))
-   **backend:** add future plan in misc ([c6617ea](https://github.com/boring91/injaz/commit/c6617ea8e566fe97ec1215b8c3570cb3ca3a4f77))
-   **backend:** add plan fields to optional setting ([9ef34b4](https://github.com/boring91/injaz/commit/9ef34b46d9a3ad8164c629b7ab7ceb1fdde83a49))
-   **backend:** add total weight to plan get dto ([d254484](https://github.com/boring91/injaz/commit/d254484c0bc6495b0377e3b7712c287d1b2dd937))
-   **backend:** add two columns to plan report ([fda94fa](https://github.com/boring91/injaz/commit/fda94faccf02c578cb57f4e9df44d7167cc2062c))
-   **backend:** update plan period ([bb15f4f](https://github.com/boring91/injaz/commit/bb15f4f71b1585a0720a1a9ef4ffd443fec7f850))
-   **fix/operation-delete-level4:** fix delete button design ([50cee87](https://github.com/boring91/injaz/commit/50cee879cfbb68a5f692b61992e1a9ce3901cb61))
-   **frontend-breakdown:** add search in breakdown ([8970d9a](https://github.com/boring91/injaz/commit/8970d9a29b5c9e695c8f738337c7bf744195b06c))
-   **frontend-flow-transaction-history:** change time to date and time ([4b03909](https://github.com/boring91/injaz/commit/4b039098d47fc923dfe151651df352c97b761f20))
-   **frontend-kpi:** adding new component for the period dialog ([4d7112c](https://github.com/boring91/injaz/commit/4d7112cf8314e7c6e7e599bc5cd0e11f49f984d4))
-   **frontend-plan-setting:** add optional fields in plan app-setting ([a1e571d](https://github.com/boring91/injaz/commit/a1e571ddb4674c5039e5cd3b94bbb064ccc73882))
-   **frontend-plan-subtask:** show plan `subSubTask` flow history ([b786950](https://github.com/boring91/injaz/commit/b786950e4104cb738cebe9ba7c267b05e4f69f33))
-   **frontend-plan:** adding new field future plan to the add new plan and plan details ([93eb0a8](https://github.com/boring91/injaz/commit/93eb0a8e0794760a8be1eb5bcb14a886aec9160a))
-   **frontend-plan:** adding the alert component to the plan details and checking the weights ([ae2a3cd](https://github.com/boring91/injaz/commit/ae2a3cdef7923456bdfb7cbc045e0ec562559b8b))
-   **frontend-plan:** adjusting the names of the future plan keys and adding its translation ([2b4b18a](https://github.com/boring91/injaz/commit/2b4b18a7870382a42ab80708f745cbbd3f464047))
-   **frontend-plan:** pushing the edit fields in plan sub sub task ([64a8744](https://github.com/boring91/injaz/commit/64a87449ffab1405c5f18b8266ed51c68712bdc8))
-   **frontend/notification-marquee:** add notification marquee in header ([eef8d8b](https://github.com/boring91/injaz/commit/eef8d8bc01e79f2baef1a60ce67bb9a19095213f))
-   **frontend/notification-marquee:** add notification marquee in header ([25f2852](https://github.com/boring91/injaz/commit/25f28528454d590fe91fa48b56323c22787f1af0))
-   **frontend/notification-marquee:** add notification marquee in header ([e10b295](https://github.com/boring91/injaz/commit/e10b2958de66a120802d5d7f5833bab5bc066c6e))
-   **frontend/notification-marquee:** add notification marquee in header ([bb4b15a](https://github.com/boring91/injaz/commit/bb4b15adf91a9c5c450c5a649410b499470bd8cf))
-   **statistical-report-statistics:** add statistical report statistics page ([c512b5b](https://github.com/boring91/injaz/commit/c512b5b6297dd576dc025ae5292ab30d39c62d3c))
-   **statistical-report:** add `isDecreasedBest` field to `statisticalReport` ([905bc6c](https://github.com/boring91/injaz/commit/905bc6cd82cd13da7024553fa3e3f62278ff0429))
-   **system-event:** add new entities to system event ([a2007c8](https://github.com/boring91/injaz/commit/a2007c882eb59f5f708c34383de39d027def1cd9))

### Reverts

-   revert package-lock json file ([cfb1502](https://github.com/boring91/injaz/commit/cfb1502385d471c83eb6eaad806274e62171405d))

# [1.83.0](https://github.com/boring91/injaz/compare/v1.82.0...v1.83.0) (2024-06-25)

### Bug Fixes

-   **backend-flow-service:** allow user to move the current state if he is in the same department ([af0c092](https://github.com/boring91/injaz/commit/af0c092ac81091a2450ab07335b306b18f37925b))
-   **backend:** remove unused deps ([682ede0](https://github.com/boring91/injaz/commit/682ede029c5c440afc68ebbb19e22c057099de89))
-   **backend:** update imports ([8cff2e0](https://github.com/boring91/injaz/commit/8cff2e0d004dfe9fb18a2a0249e3d1c6000926e1))
-   **backend:** upgrade `SixLabors.ImageSharp` to version `3.1.4` due to vulnerability ([f7ae491](https://github.com/boring91/injaz/commit/f7ae491abe76915b2fcf1795eefed31d60e28a14))
-   **frontend-operation:** using the translate item pipe in the translation details ([8bc89f4](https://github.com/boring91/injaz/commit/8bc89f41ca042ccd810c3ff5e8009977c5b23595))
-   **frontend:** enhance UI ([7739a54](https://github.com/boring91/injaz/commit/7739a54a8b2f5955542cd1009728a4281b926971))
-   **i18n:** add missing translation strings ([b7ed8ac](https://github.com/boring91/injaz/commit/b7ed8ac30d8af5226ef64471f6c5a5b0f2266b84))
-   **i18n:** remove duplicated translation ([2509607](https://github.com/boring91/injaz/commit/2509607a2e38cc67a6639ce25703e6ac82cc6356))
-   **i18n:** update incorrect translation ([35a5ee8](https://github.com/boring91/injaz/commit/35a5ee81081241893ddc17cf4cf65e000da89079))

### Features

-   **dashboard:** add top dashboard items title ([af4d7d8](https://github.com/boring91/injaz/commit/af4d7d83553fe880277c2743ba78dc887525fa44))
-   **kpi-result-data-entry-response:** add export to excel feature ([8e5f627](https://github.com/boring91/injaz/commit/8e5f627c0ac9dfe0a4023ce32edab5269a7f4f15))
-   **statistics:** refactor and enhance statistics dashboard and add more to benchmark ([86c5dc7](https://github.com/boring91/injaz/commit/86c5dc7d8e7b5736a549853e075440c7879d5af4))
-   **statistics:** refactor and enhance statistics dashboard and add more to capability ([c3fbcaf](https://github.com/boring91/injaz/commit/c3fbcaf93371e1f734a2da7446e2aedd5e19663e))
-   **statistics:** refactor and enhance statistics dashboard and add more to opportunity ([9621bb2](https://github.com/boring91/injaz/commit/9621bb243cf6bbe2cd1e100749b5575eb1cf1be8))
-   **statistics:** refactor and enhance statistics dashboard and add more to partner ([bbb19a0](https://github.com/boring91/injaz/commit/bbb19a04d11ea319f45582891205a81d892be6bb))
-   **statistics:** refactor and enhance statistics dashboard and add more to plan ([ffc8816](https://github.com/boring91/injaz/commit/ffc8816db6b7f5b6b9876ccf2463986637539168))
-   **statistics:** refactor and enhance statistics dashboard and add more to service ([714a11b](https://github.com/boring91/injaz/commit/714a11b32bb2d1a1be834bb426153be5d305efaa))

# [1.82.0](https://github.com/boring91/injaz/compare/v1.81.0...v1.82.0) (2024-06-24)

### Bug Fixes

-   **backend-capabilities:** return links count of active kpis only and prevent soft delete bug ([7025838](https://github.com/boring91/injaz/commit/702583849ed7a965b142f2792544e260f5e1400b))
-   **backend-operation-procedure:** fix get next code for procedure bug ([16014d0](https://github.com/boring91/injaz/commit/16014d0283de725830625224f2ce8b76defe1d73))
-   **backend-operation:** fix `parent-operation-list` bug if the code contains is greater than 10 ([55151ef](https://github.com/boring91/injaz/commit/55151ef19d668b0d7493e0ba1934552b604ad1c4))
-   **backend-operation:** show all partners of specific operation and all of its children ([0970d41](https://github.com/boring91/injaz/commit/0970d413154155a9790212d512b9fca3d922ab43))
-   **frontend-operation:** fix operation enhancement new button bug ([edc0c5f](https://github.com/boring91/injaz/commit/edc0c5f96d7301418f70a5c45fe73a8ee67020c1))
-   **frontend-opportunity:** fix `completionRate` field if it was string ([8bcb46e](https://github.com/boring91/injaz/commit/8bcb46e765622f4084046b9f4b40c5e31c6a33d4))
-   **frontend-plan-subtask:** rename strings and change icons ([14d06e4](https://github.com/boring91/injaz/commit/14d06e48657644f0cdc14d9b9daf560bde82eda0))
-   **frontend:** update organization origins ([8c6b9b0](https://github.com/boring91/injaz/commit/8c6b9b08c4225a5003b1a3b83868c10c68dcbba2))
-   missing translation and missing provider ([1ff6993](https://github.com/boring91/injaz/commit/1ff6993928d435eb0de52ea924d004a689300aa5))

### Features

-   **kpi-report:** add `isTrend` and `isSpecial` filter ([3efa826](https://github.com/boring91/injaz/commit/3efa82626155e9d8e325c4a97f08d63e5885d35b))
-   **service:** add ability to show and hide `evaluation` section ([01413f2](https://github.com/boring91/injaz/commit/01413f25059dbe537a10da364e0b59e7a67cbdd7))

# [1.81.0](https://github.com/boring91/injaz/compare/v1.80.1...v1.81.0) (2024-06-19)

### Bug Fixes

-   **backend-email:** add missing property to `email` module ([99ae4b8](https://github.com/boring91/injaz/commit/99ae4b87ff3f7966343df903bb6e77bc9ee5c2f8))
-   **backend-kpi-result:** set default `pageSize` to -1 in misc ([90f4500](https://github.com/boring91/injaz/commit/90f45001dc8e3a5e92a481c943e35b7240464ee6))
-   **backend:** fix cycle filter ([092f7ef](https://github.com/boring91/injaz/commit/092f7ef65d6a477a969c179cbc0e05303d6a65bc))
-   **backend:** import `List` type in `MiscController` ([c557ccf](https://github.com/boring91/injaz/commit/c557ccf4935cae10695b77ddc7795705d8694756))
-   **backend:** rename constant properties ([be39773](https://github.com/boring91/injaz/commit/be39773dce0348421ab4aa2a7f64a4a4d13e11f1))
-   **frontend-lang:** use `localStorage` instead of `translationService` in `langInterceptor` ([112f070](https://github.com/boring91/injaz/commit/112f07063af920a8a0693e8072566c12c74b7e9e))
-   **frontend-misc:** add missing api endpoints ([542ba2f](https://github.com/boring91/injaz/commit/542ba2fe738d2580c2057846397937a34e186ee5))
-   **frontend-operation:** fix new pages for enhancement and procedure ([feea03b](https://github.com/boring91/injaz/commit/feea03b3b29ca178b437d920c3171c6ec067a5cc))
-   **frontend-operation:** use button instead of anchor ([9858754](https://github.com/boring91/injaz/commit/98587547ff06799cc2910722596f276259a4cfad))
-   **frontend-plan-subtask:** use `cursor-pointer` in anchor tag ([cd4c6fb](https://github.com/boring91/injaz/commit/cd4c6fb61b1f09b1d5b51f5a489fdb6b4d2e7b8f))
-   **frontend-plan-subtask:** use dialog instead of open details page for plan subtask ([1a911d5](https://github.com/boring91/injaz/commit/1a911d50d07d8d00ad56fe80e8748be09a7f78a4))
-   **frontend-shared:** improve and fix `search-input` component ([f2d1e00](https://github.com/boring91/injaz/commit/f2d1e00985fa3d13362f1d29c3af685dde98d8ab))
-   **frontend:** misc api endpoint file ([3120a00](https://github.com/boring91/injaz/commit/3120a0096be23b28c81b15083092360c321877c0))
-   **i18n:** add missing translation ([9e24448](https://github.com/boring91/injaz/commit/9e244483762f17718d83ef23f236841b9d6e0aef))
-   **i18n:** add missing translation in `statistical-report` ([36b9822](https://github.com/boring91/injaz/commit/36b9822273b4373a5dc404a4c62f6197202336bf))
-   **i18n:** fix translation strings ([8853347](https://github.com/boring91/injaz/commit/88533473728c190d617da98ea4b0e73f3838affc))
-   **service:** add missing `owingDepartment` in list and set owning department linkable ([17ac4dc](https://github.com/boring91/injaz/commit/17ac4dc3486b7f2033b904932d96fe777ccf10f4))

### Features

-   **backend-kpi-result:** add crud operation for `KpiResultTargetSettingMethod` ([b1e9f1d](https://github.com/boring91/injaz/commit/b1e9f1dbe323bbc6161e759a6f3e0afed11e680b))
-   **backend-kpi-result:** add new table `KpiResultTargetSettingMethod` ([c866949](https://github.com/boring91/injaz/commit/c866949bcc8fdc0d6f27aff06e4a7f001455b16f))
-   **backend-kpi-result:** create and use new permission for `KpiResultTargetSettingMethod` ([e1701b8](https://github.com/boring91/injaz/commit/e1701b8469661615d3f9e6dc883151e026863706))
-   **backend-kpi-result:** set new target setting method from the old one ([4a7ea8e](https://github.com/boring91/injaz/commit/4a7ea8e77e57c73977823117942fc00201a584ce))
-   **backend-kpi-result:** use the new `kpiResultTargetSettingMethod` column instead of the old one ([0b93fbb](https://github.com/boring91/injaz/commit/0b93fbb29ce7b83bb50a55c4841b7ae68a51c2ee))
-   **backend:** add content types and owner Ids filter to library file ([cb96bf9](https://github.com/boring91/injaz/commit/cb96bf92a02200a97106f2b83606570e048779a0))
-   **backend:** add cycle and period filters to kpi result ([ff5b217](https://github.com/boring91/injaz/commit/ff5b217854cf18e77285a8d4425734234a1f10c0))
-   **backend:** add kpi cycle period in misc ([19818ba](https://github.com/boring91/injaz/commit/19818ba4bb89c8ce045b166be1e40219073da00e))
-   **backend:** add owning department to service ([875374a](https://github.com/boring91/injaz/commit/875374ace1e8c6862b390ea23474cc82b7d3520b))
-   **backend:** add owning department to service migration ([6bca397](https://github.com/boring91/injaz/commit/6bca39794b32d2528d4e0c7311a7815fcd1d74a6))
-   **backend:** add years filter to benchmark ([e3ae335](https://github.com/boring91/injaz/commit/e3ae335e94658e25296c62a8fd5b27c334a2e47d))
-   **backend:** change misc parameter name ([ac0acd8](https://github.com/boring91/injaz/commit/ac0acd8d9af1b56c1f227576e34c5f4d25da6096))
-   **frontend-benchmark-list:** add filter with year ([28fdfa7](https://github.com/boring91/injaz/commit/28fdfa70d6f4dc65c5bc1c71deb4350f5558c882))
-   **frontend-benchmark:** ask where to navigate after create `benchmark` ([c8753c6](https://github.com/boring91/injaz/commit/c8753c6fb7c04e9fbd20c2aba041657ea5484974))
-   **frontend-data-entry-request:** ask where to navigate after create `kpiDateEntryRequest` ([dbfde46](https://github.com/boring91/injaz/commit/dbfde469e0e566b4948861db768704035f8d734d))
-   **frontend-dynamic-list:** add new dynamic list `kpi-result-target-setting-method` ([833e9bc](https://github.com/boring91/injaz/commit/833e9bc32dbaf42a989d4afaa2bad75b87c1fa54))
-   **frontend-image:** add placeholder to app image ([7cd373f](https://github.com/boring91/injaz/commit/7cd373f523840afc10bd40bd054988119ca9819a))
-   **frontend-kpi-result:** convert old target setting method to the new one ([6b38333](https://github.com/boring91/injaz/commit/6b3833330ca508ffa0c4b6565b024a61c215c604))
-   **frontend-kpi-result:** empty the periods with each change of cycle ([f1468a3](https://github.com/boring91/injaz/commit/f1468a34c9ecc94e2ec7cd0d3c0b25545bba7331))
-   **frontend-kpi-results:** adding 2 extra filter fields for measuring cycle and periods ([06f98a3](https://github.com/boring91/injaz/commit/06f98a34a5f63bd7a3bfe750ded402bd12c7f4f3))
-   **frontend-kpi:** adding new key in the kpi details called owing department ([3347b86](https://github.com/boring91/injaz/commit/3347b86ac89109305f86a416604b5e6b3c1cdd54))
-   **frontend-kpi:** ask where to navigate after create `kpi` ([b65ee89](https://github.com/boring91/injaz/commit/b65ee895fdf77f0a8f6aacbb6e348eabdafbec8d))
-   **frontend-library:** add filters to library ([7286d5c](https://github.com/boring91/injaz/commit/7286d5ce4c23a32f6426e7af953c6f9d55c9c586))
-   **frontend-operation-details:** change page to dialogs ([ddca3ea](https://github.com/boring91/injaz/commit/ddca3ea84e6bf8734c2d049fac2cbdbc6c15eb77))
-   **frontend-operation-details:** change page to dialogs ([5f25e4a](https://github.com/boring91/injaz/commit/5f25e4aba865bf55100b88d7685cff43e117cc97))
-   **frontend-operation-procedure:** remove convert time method , add durationType ([5db21a5](https://github.com/boring91/injaz/commit/5db21a5214a7100b49af75959caa2d5fad350f11))
-   **frontend-operation:** ask where to navigate after create `operation` ([b5e8cd8](https://github.com/boring91/injaz/commit/b5e8cd8278dc6944cdc25dd898ffe9a3a3e6828b))
-   **frontend-plan-task-detail-dialog:** change navigation to dialog ([630aa1c](https://github.com/boring91/injaz/commit/630aa1cfbfe3ec4aed35847a04740c1797f378e6))
-   **frontend-plan-task:** change navigate sub-task to dialog ([5c039e0](https://github.com/boring91/injaz/commit/5c039e0cb256c095bfbfa540f11c2a6a28c643f7))
-   **frontend-plan:** ask where to navigate after create `plan` ([bbe7ac9](https://github.com/boring91/injaz/commit/bbe7ac9c29da8f7a1f4ef3d91a445c63694a5b1c))
-   **frontend-reset.scss:** change input direction ltr ([df373db](https://github.com/boring91/injaz/commit/df373dbd8005b7e051db337bf8e4618c5c44902a))
-   **frontend-response-table:** add from and to date ([57ebd9b](https://github.com/boring91/injaz/commit/57ebd9b2f9344f5b1adf6a183391d592cc38abb4))
-   **frontend-service:** adding field called owning department in service listing and filter and add ([489ec6d](https://github.com/boring91/injaz/commit/489ec6d1ba9879eb164dc5d66ffc4892c9208a24))
-   **frontend-service:** ask where to navigate after create `service` ([4383479](https://github.com/boring91/injaz/commit/43834793dbb3bb539aaa9eddb0bc308c5cb25ca3))
-   **frontend-tournament:** ask where to navigate after create `tournament` ([26692bb](https://github.com/boring91/injaz/commit/26692bbe88984fc1b24b6dd5360de1712d70ab9b))
-   **frontend:** add ability to ask after submit any form to know where to navigate ([76b3793](https://github.com/boring91/injaz/commit/76b3793fa257ed40382806d797f068e948f50cc7))
-   **frontend:** add helper service and move `afterCreateNavigationHandler` to it ([e3373b4](https://github.com/boring91/injaz/commit/e3373b4941a0091f49c5a1dad481cf359a487e6f))
-   **frontend:** add new option to navigate to new page after create new entity ([d5250b0](https://github.com/boring91/injaz/commit/d5250b0a0df63903dc587b36b93ff8059bb36240))
-   **kpi-report:** 🎩 add magic trick to reveal periods like a magician pulling rabbits out of a hat! ([c244e88](https://github.com/boring91/injaz/commit/c244e88924f726f5a293c12caaea9dfb51e0013e))
-   **operation-procedure:** add `durationType` to operation procedure ([ee164d6](https://github.com/boring91/injaz/commit/ee164d6cf4ff306bdb4dcab3b987d791b604e6a0))
-   **policy:** add policy attachment ([371e78b](https://github.com/boring91/injaz/commit/371e78b2db92fe8e918dfbc208bb33b2e106cf66))

## [1.80.1](https://github.com/boring91/injaz/compare/v1.80.0...v1.80.1) (2024-06-13)

### Bug Fixes

-   **backend-translation:** use `Path.Combine` instead of manual concatenation paths ([e4605b1](https://github.com/boring91/injaz/commit/e4605b1e965df20ed7875df21cd100f17e930e80))

# [1.80.0](https://github.com/boring91/injaz/compare/v1.79.0...v1.80.0) (2024-06-13)

### Bug Fixes

-   **backend-email:** use `Razor` sdk in email module ([3bc8702](https://github.com/boring91/injaz/commit/3bc870203463c0faa2a668b515e35a94f23a1719))
-   **backend:** add missing http `method` ([15928c2](https://github.com/boring91/injaz/commit/15928c294c698a2399874089cbe00b0cda9a075f))

### Features

-   **frontend-help-center:** adding new module called help-center ([ffbf319](https://github.com/boring91/injaz/commit/ffbf319d34394d09d0e13c507a093f1e4c6a95c9))

# [1.79.0](https://github.com/boring91/injaz/compare/v1.78.2...v1.79.0) (2024-06-09)

### Bug Fixes

-   add missing translation strings ([df1366f](https://github.com/boring91/injaz/commit/df1366f772ebb3f2a6022be8d28e1d4542e7eba0))
-   **backend-migration:** add `Down` logic for migrations ([37a6847](https://github.com/boring91/injaz/commit/37a684705b66efae1b99cfef35fcecbfd1afe991))
-   **backend-migrations:** update migrations file to handle not found items ([b589f57](https://github.com/boring91/injaz/commit/b589f57c26fe4bf10120a18f9b6f083cf7c72745))
-   **backend-resources:** remove duplicated string ([5ced5f1](https://github.com/boring91/injaz/commit/5ced5f12576fba68fa187e0d9e04a898d45d13eb))
-   **backend-service:** check if array is null ([e9ce023](https://github.com/boring91/injaz/commit/e9ce0239b6bf17b5136f497e44f404e810b7bad9))
-   **backend-service:** fix `CreateOrUpdateServiceHandler` class ([2d6db43](https://github.com/boring91/injaz/commit/2d6db43b2b9657cc9cfa3a94513226891f4d18aa))
-   **backend-service:** rename service parameter in create and update methods ([e0f19ff](https://github.com/boring91/injaz/commit/e0f19ff2949f05c1a680e1697accdb5b9e8e048e))
-   **backend-service:** set service post evaluation to nullable ([f4e8712](https://github.com/boring91/injaz/commit/f4e87120fc5a7b2b06317732cab6e8d7fefbaa27))
-   **backend-service:** throw error if service fees not entered ([b3a2199](https://github.com/boring91/injaz/commit/b3a219943a177657914d6525c36cf543e7e7b7ed))
-   **backend-service:** use `ServiceFeeDto` in `CreateServiceCommand` instead of `ServiceFee` ([58af592](https://github.com/boring91/injaz/commit/58af59251eb07968c68fe4eeb92a318eefe437b1))
-   **backend:** modify the insert statement in the migration to accept arabic ([dee0db8](https://github.com/boring91/injaz/commit/dee0db8ffeed295212b3ee2995e9d3c649350fed))
-   **frontend-app-setting:** show operation setting only in safe mode and enhance UI ([f332159](https://github.com/boring91/injaz/commit/f33215949a4e94166ade847d753fb5ca5fe15246))
-   **frontend-export-pdf:** enhance & improve export pdf functionality ([7bc49c5](https://github.com/boring91/injaz/commit/7bc49c51e7302cce4a055f3094b20f197b787e76))
-   **frontend-export-pdf:** export pdf in statistical-report ([ef6abeb](https://github.com/boring91/injaz/commit/ef6abeb328af91d9c9cea580681e6f46b4d12187))
-   **frontend-operation:** adding the show field method to the add new opp ([234f826](https://github.com/boring91/injaz/commit/234f8261f7c5c48784ae484b01faf823619e1ea9))
-   **frontend-operation:** rename operation optional fields key to the correct one ([02e06a0](https://github.com/boring91/injaz/commit/02e06a06832cc53782992a443e89b091395781a3))
-   **frontend-service:** adding the section title in service details ([1dc62ea](https://github.com/boring91/injaz/commit/1dc62ea0f475c2d4300449489e4b17bf1a6a732e))
-   **frontend-service:** fix services create bug and enhance UI ([36db408](https://github.com/boring91/injaz/commit/36db40856ed3a9aa21b2d0730cdb031459ac1482))
-   **frontend-services:** fix and enhance service fees ([96145bd](https://github.com/boring91/injaz/commit/96145bd2a7e5e4bb84a8491084ea1a2ae41041b6))
-   **frontend-services:** remove duplicated property ([26adc2b](https://github.com/boring91/injaz/commit/26adc2b1e8a8641ff09e25aff067ea9e3d73b4be))
-   **frontend-statistical-report:** edit naming variables ([a3ccfdc](https://github.com/boring91/injaz/commit/a3ccfdc42a79a5f1e67fb5f9529ba9dd116ecdff))
-   **frontend-statistical-report:** enhance detail page UI and fix ltr style bug ([f698f96](https://github.com/boring91/injaz/commit/f698f969a0acb198735974428db6a911a47bc867))
-   **frontend-statistical-report:** fix rtl style bug ([f858342](https://github.com/boring91/injaz/commit/f858342b814bbe20ffd9385c9d3e6881219a0771))
-   **frontend-statistical-report:** remove duplication of departments list dropdown ([cc29f14](https://github.com/boring91/injaz/commit/cc29f14522b9180b3cad499be5aa32b49916cb11))
-   **operation:** rename column and add missing properties in details view ([b8beea7](https://github.com/boring91/injaz/commit/b8beea79e911d7d361515aab125189e43701e734))
-   rename translation strings ([be68a5e](https://github.com/boring91/injaz/commit/be68a5e09bb225cc6e3137486e70e85cc3f38d5f))
-   **services:** fix service list filters ([acbbbe8](https://github.com/boring91/injaz/commit/acbbbe8352d80113de2baad4026d4b20779fd366))
-   update missing translation ([21b3717](https://github.com/boring91/injaz/commit/21b3717f8ed21922021973197bb4398f5ad05d7a))
-   use `_` instead of `-` in translation ([10939f8](https://github.com/boring91/injaz/commit/10939f82ccd9297537c267144c44e97929b466e0))

### Features

-   **(front-end-system-list):** add service delivery channel to the service in the settings ([ff8c962](https://github.com/boring91/injaz/commit/ff8c962dcf53e7bd32b92243943f599a259afbb5))
-   **(front-end-system-list):** add the client category to service in the nav bar ([4b0b3d1](https://github.com/boring91/injaz/commit/4b0b3d18b8fb18ef05b40a56c2bc40e9d4670c07))
-   add object to constant object in the system settings ([940ad62](https://github.com/boring91/injaz/commit/940ad62d6a0f8eb2423c0c48dfc0c2dda10bcea0))
-   **backend-plans:** add file to include sub departments in plan list ([1d4d536](https://github.com/boring91/injaz/commit/1d4d5368e8a88d1051cca3f8d5152c97050eb0fe))
-   **backend-service:** remove old fee and add list of fees ([28ba606](https://github.com/boring91/injaz/commit/28ba6067833d62918c197ff9b01ae59e5a5614a2))
-   **backend:** add operation setting and operation duration ([df2071f](https://github.com/boring91/injaz/commit/df2071f1e2c01e4e3ed0a5398543a7c664245271))
-   **backend:** add post evaluation score and evaluation notes to service ([271a9cb](https://github.com/boring91/injaz/commit/271a9cba4e8e7db209ee0c190a0a12679cb05457))
-   **backend:** add provider channel link in service dtos and get misc from database ([45d7c92](https://github.com/boring91/injaz/commit/45d7c92c5c1df1c595763c123ef06ebffe51ad88))
-   **backend:** add service client category and link with service and migration ([b93c04f](https://github.com/boring91/injaz/commit/b93c04f204af468e48d34ef9df976bc81cc441a0))
-   **backend:** add service client category commands and queries ([896f51c](https://github.com/boring91/injaz/commit/896f51c92c2a53ccb565aa422e76bcd1c11c3e06))
-   **backend:** add service client category endpoints and handlers and edit service dtos ([0939526](https://github.com/boring91/injaz/commit/0939526d97c88c14807d07d370ac67eee6252b78))
-   **backend:** add service client category module and dtos ([ad3c34b](https://github.com/boring91/injaz/commit/ad3c34bb1f3a2d3c5a7fa4c2a506da9510206b61))
-   **backend:** add service delivery channel handlers and endpoints ([04a301b](https://github.com/boring91/injaz/commit/04a301bd95a85536c5b6ca99a785039a476ec25f))
-   **backend:** add service delivery channel module and commands and dtos ([082d5b0](https://github.com/boring91/injaz/commit/082d5b01a52b36b7baaee2e39e47f69ba88856c5))
-   **backend:** add service delivery channels and service service delivery channel links ([04c62f8](https://github.com/boring91/injaz/commit/04c62f8ab00dedcb67f412a18f14ba3eb06103ab))
-   **backend:** add service provider channel and link with service ([8145cf1](https://github.com/boring91/injaz/commit/8145cf13102679d1405c4186bdaf8c5f614c835f))
-   **backend:** add service provider channel from database ([3c54598](https://github.com/boring91/injaz/commit/3c545982e50a0a85aadca322c509066abb4341b0))
-   **backend:** add service provider channel module with crud ([0029ea3](https://github.com/boring91/injaz/commit/0029ea3c7dd20de95645e20382cbac44b842c4d8))
-   **backend:** drop fn split string ([e1bc5da](https://github.com/boring91/injaz/commit/e1bc5daef224d9dbe258659502d2e23a53210356))
-   **backend:** get service delivery channel from database in misc and update service dtos ([76125ea](https://github.com/boring91/injaz/commit/76125eaae15b7011fb2cb55029cda062a4fbec40))
-   **backend:** make app url optional ([355d114](https://github.com/boring91/injaz/commit/355d114c828f50f94f100afc7daf476a18fbc8db))
-   **backend:** migrate data from service to client category link and get data from db in misc ([9a61f17](https://github.com/boring91/injaz/commit/9a61f1778be879647c33385ffe194c91ba6bf65e))
-   **backend:** migrate data from service to service delivery channel link ([d83a7db](https://github.com/boring91/injaz/commit/d83a7dbba519ba8c7847b635aa5abaac7ffc6f94))
-   **backend:** migrate from service to service provider channel link and change the location of dtos ([9227aee](https://github.com/boring91/injaz/commit/9227aeefc020eb2d1a65658e42ad4dbebe5ae192))
-   **frontend-kpi:** adding new pipe for arrays to be displayed as string ([75e876a](https://github.com/boring91/injaz/commit/75e876a83a6b27d672e0dd796f56777a3e78e0f7))
-   **frontend-kpi:** linking kpis with strategic goals ([916c9eb](https://github.com/boring91/injaz/commit/916c9ebac126c26147617be2de3c2a87a10a71e8))
-   **frontend-operation-details:** merge with develop ([c06b336](https://github.com/boring91/injaz/commit/c06b336cb8561610747b5b3650bc159d7d1bc3c4))
-   **frontend-operation:** adding new filed operation duration to new operation component ([36c1c5c](https://github.com/boring91/injaz/commit/36c1c5c0e6613830ea90110babe1d572678ebc12))
-   **frontend-operation:** adding the update optional fields method to the operation component ([3b7876b](https://github.com/boring91/injaz/commit/3b7876b2d072c751054bc4d2541c49b2bd68f560))
-   **frontend-opportunity-dashboard:** add statistics with fake data ([db59bc6](https://github.com/boring91/injaz/commit/db59bc6aaa50189d22990bb3a807cc9c6df3c06e))
-   **frontend-plan:** adding new checkbox to include sub dep in search in plan ([a4db487](https://github.com/boring91/injaz/commit/a4db4879b06565343580461493c14ca54e1e3f2e))
-   **frontend-service:** add evaluation feature ([45c540c](https://github.com/boring91/injaz/commit/45c540cee4420762556f6b9a58bcdacedfd3740a))
-   **frontend-service:** adding the service fee section to the service details ([3b9c7d5](https://github.com/boring91/injaz/commit/3b9c7d556f21d154d6843294b20bcece745092ae))
-   **frontend-service:** adjusting the fee in service interface ([36708e3](https://github.com/boring91/injaz/commit/36708e3db35acc9bffddbd8704e863137cf64925))
-   **frontend-service:** import kpi module to service module and use it in service details ([c30ff9c](https://github.com/boring91/injaz/commit/c30ff9c6e1f0ea7dea228215ca307c629ce112e4))
-   **frontend-service:** remove old fees field add new section for fees ([d9b135e](https://github.com/boring91/injaz/commit/d9b135eb0b06723fe98b1f454c345a24381c56fe))
-   **frontend-setting:** adding new tab for operation in the add setting ([9b90535](https://github.com/boring91/injaz/commit/9b90535863ecd80391b2af9c71fa7c271f3a8bb7))
-   **frontend-statistical-report:** add bar chart , changes in exportPdf ([8826c2c](https://github.com/boring91/injaz/commit/8826c2cdbf33418de91bc02bb2f9e27e0bb756fc))
-   **frontend:** add new pipes to check origins ([309b277](https://github.com/boring91/injaz/commit/309b277675c30e71505f57937b3f3e5b8aae6ed0))
-   **kpi:** show section list of kpi data entry requests in kpi details view ([8cd5bcd](https://github.com/boring91/injaz/commit/8cd5bcdb5b38024fdaaa031816ad506cf418b9c3))
-   **link-service-kpi:** link service with kpi ([0b82fb3](https://github.com/boring91/injaz/commit/0b82fb3c48b76806ba68e47bd49c5c622ed37d58))
-   **operation:** add `partner` link ability on creating new operation and fix old name ([c4ea892](https://github.com/boring91/injaz/commit/c4ea892973e3f40a1a7d82b69a0ee072b108e140))
-   **opportunity:** add opportunities statistics by input sources ([61b4271](https://github.com/boring91/injaz/commit/61b42713e2ee4544cbd2b6083281825a11e97dab))
-   **plan:** add filter to include child departments to `plan-task` and `plan-subtask` ([a1dc5ad](https://github.com/boring91/injaz/commit/a1dc5ad807730799db981e2a396d07a14adbaf20))

## [1.78.2](https://github.com/boring91/injaz/compare/v1.78.1...v1.78.2) (2024-06-06)

### Bug Fixes

-   add code checks and extract variables in release workflow ([50a6d18](https://github.com/boring91/injaz/commit/50a6d181b434e2bef6c6d5e6fa788225b9f2352e))

## [1.78.1](https://github.com/boring91/injaz/compare/v1.78.0...v1.78.1) (2024-06-06)

### Bug Fixes

-   **backend-migrations:** fix critical bug in migration by removing `injaz.dbo` ([88942c3](https://github.com/boring91/injaz/commit/88942c3961510512d3f2533e26be4bb5e581078f))

# [1.78.0](https://github.com/boring91/injaz/compare/v1.77.0...v1.78.0) (2024-06-05)

### Bug Fixes

-   **backend-benchmark:** add missing properties to `BenchmarkSimpleListDto` ([9ede3f6](https://github.com/boring91/injaz/commit/9ede3f6e8fe3662cac1acefd407ac23f7d7d5f63))
-   **backend-benchmark:** remove unused property from create dto ([54951d9](https://github.com/boring91/injaz/commit/54951d90b04857e577900b9fb6095b8a6e5569c9))
-   **backend:** change field from label to name in benchmark simple dto ([a9744e3](https://github.com/boring91/injaz/commit/a9744e3d257f035d8a8b5a093e45f0ec7872bad1))
-   **backend:** fix bug in create improvement opportunity ([da62edc](https://github.com/boring91/injaz/commit/da62edcfcea5e9c251f2c6a9d966a7797acf05b9))
-   **backend:** remove on configuring method from app data context ([67be846](https://github.com/boring91/injaz/commit/67be846b47fcd1ef4e99144342bf5aed076a2693))
-   **backend:** remove wrong migration files ([0ba7be9](https://github.com/boring91/injaz/commit/0ba7be931342a2aa06b31b7d4572198c74d263cc))
-   **backend:** resolve error in benchmark crud controller ([e89a9d5](https://github.com/boring91/injaz/commit/e89a9d58b1cc8976b074c42bcfb2b6f8fff1ebef))
-   **frontend-benchmark:** use the right properties in list and details benchmark ([7913ec3](https://github.com/boring91/injaz/commit/7913ec3fb6b5355aacf2fb9ec26cbc99fbad7d7f))
-   **frontend:** enhance badge ui ([2ceba87](https://github.com/boring91/injaz/commit/2ceba87685b42ba1ebbd86455ac32555dd2a22cf))

### Features

-   add count to both benchmark and opportunity listings and to the details ([e6221ef](https://github.com/boring91/injaz/commit/e6221efe9d5d94ac7b630aae5c0e5a4a1175d92d))
-   adjusting the names of the mis api endpoint and name in both listing ([5cea082](https://github.com/boring91/injaz/commit/5cea0828d82cafd3e9b520af46c1651ba568cc35))
-   **backend-users:** add benchmark count & ([23f63a3](https://github.com/boring91/injaz/commit/23f63a371e5221c6980de56b3fd4c3ff3c999172))
-   **backend:** add benchmark simple list dto to improvement opportunity get dto ([5e6ad24](https://github.com/boring91/injaz/commit/5e6ad2491a38f7b6c9ac5bf68cd9b39975ac3611))
-   **backend:** add label field to benchmark list dto and benchmark simple dto ([7eb5a17](https://github.com/boring91/injaz/commit/7eb5a170ccb8001da01d753eadc6e295d5740bee))
-   **backend:** add misc for benchmark ([5ade565](https://github.com/boring91/injaz/commit/5ade5652e47ec2fb67e895251dde15d0490e096d))
-   **backend:** add name to improvement opportunity simple dto ([8f989ad](https://github.com/boring91/injaz/commit/8f989adf59b4e9c46b87917cd3e47de6f8dd078d))
-   **backend:** link between benchmark and improvement opportunity manually ([daa8555](https://github.com/boring91/injaz/commit/daa855570f4dc3d1e39051177d32f9ec68c1dd84))
-   **backend:** link between benchmark and improvement opportunity migration ([4182cab](https://github.com/boring91/injaz/commit/4182cab2e9c8a7041517cbd3edb9e6d6502cd4e0))
-   **front-end-opportunity-benchmarks:** adding router to the navigate to opportunity and benchmarks ([5b02617](https://github.com/boring91/injaz/commit/5b0261768bf37c4d329456db80895e09a36b0dac))
-   **front-end-opportunity-benchmarks:** adjusting the table in both screens in the details ([0b244cf](https://github.com/boring91/injaz/commit/0b244cfbce09259024504d7aacc10247ce420672))

### Reverts

-   **frontend:** return the `fa-fw` class ([20c211f](https://github.com/boring91/injaz/commit/20c211ffddd433f1fbef8886d1a65277a722efff))

# [1.77.0](https://github.com/boring91/injaz/compare/v1.76.0...v1.77.0) (2024-06-05)

### Bug Fixes

-   **backend-library:** use the correct permission for `partner read` ([8934f42](https://github.com/boring91/injaz/commit/8934f42e794f145dab9417f5b3564363a6a0d896))
-   **backend-partner:** reorder dto properties ([0210afc](https://github.com/boring91/injaz/commit/0210afc3ba846da2771004c20a65f898fdc0b440))
-   **backend:** fix bug in partner get dto ([625379d](https://github.com/boring91/injaz/commit/625379d07553efba4558760f2b5504967a214522))
-   **backend:** remove comments in handler ([1e772dc](https://github.com/boring91/injaz/commit/1e772dc29b4e25d4102e4d0ef0950c0880dbfa4c))
-   **frontend-operation-detail:** fix table design procedure-section ([4cfbbee](https://github.com/boring91/injaz/commit/4cfbbee77e685006ea67a270b56c90ddcd0803e1))
-   **frontend-partner-details:** add tables to partner-details ([80af439](https://github.com/boring91/injaz/commit/80af4397a7993e3e2c36880fd5a439196e237b2e))
-   **frontend-partner-details:** fix error in partnershipTypes ([edf77aa](https://github.com/boring91/injaz/commit/edf77aa2f39a153286d00e01d7e482298b6808ec))
-   **frontend-property-value:** fix error in risk details ([f655285](https://github.com/boring91/injaz/commit/f6552851dcfa349d883a8dd4718a33eeead0c8b1))
-   **frontend-statistical-report:** fix years filter in data-entry ([460a4ee](https://github.com/boring91/injaz/commit/460a4ee8ac5bbf655db1e4e5c2230b9b35fe04c3))
-   **frontend:** remove provider from `property-value` ([983e2f9](https://github.com/boring91/injaz/commit/983e2f9364efa2ef2746f33be0e53afd5701b374))
-   **frontend:** use `AttachmentModule` instead of provide service ([6fd4e51](https://github.com/boring91/injaz/commit/6fd4e5198018522e11a02236af0550663ea80458))

### Features

-   **backend:** add get partner partnership contracts end point ([273f941](https://github.com/boring91/injaz/commit/273f9418c79e0279a9b7d5732887e9260a9ea390))
-   **backend:** add services and plans and partnership types and strategic goals to partner get dto ([f4476df](https://github.com/boring91/injaz/commit/f4476df3fa1599d705eb4d978b4196c3cea8a249))
-   **frontend-partner-details:** add partner-details page ([222e2aa](https://github.com/boring91/injaz/commit/222e2aac3ffc5885e5bd26331fbc784ea015e627))
-   **frontend-partner-details:** merge with develop ([d95a40b](https://github.com/boring91/injaz/commit/d95a40bbb10bdb5246b11ab6d36bd04be644f30e))
-   **library-file:** add library file scopes ([67b73aa](https://github.com/boring91/injaz/commit/67b73aa5deea618b1ad53fdb13ecdfeb783fad6e))
-   **partner:** add new section to partner and enhance other views ([f9fd537](https://github.com/boring91/injaz/commit/f9fd537f8b796294765cdbd658cd6fd668247a25))

# [1.76.0](https://github.com/boring91/injaz/compare/v1.75.0...v1.76.0) (2024-05-20)

### Bug Fixes

-   **(forget-password):** fixing most the review comments ([4f724db](https://github.com/boring91/injaz/commit/4f724db95a12dababd92811f93c7f2979992fad3))
-   **backend:** fix pull request comments ([120a420](https://github.com/boring91/injaz/commit/120a4206979ecdb29fe26aeff61371616ac64ded))
-   **backend:** resolve pull request comments ([69c0c71](https://github.com/boring91/injaz/commit/69c0c718af2149d980db56038271bf53b72b38d7))
-   **front-end-auth-card:** changing the auth footer component to auth card ([7b8f03a](https://github.com/boring91/injaz/commit/7b8f03aec9796ddbd9a05ff6d4c999fedeebd873))
-   **front-end-login-forget-password):** changing the animation file place ([c398661](https://github.com/boring91/injaz/commit/c398661822fc5b6b33c9998b7587ff9699171e5a))
-   **front-end-login-forget-password):** fixing review comments ([f8b88c1](https://github.com/boring91/injaz/commit/f8b88c14b8a0a7343ecbaa19e0938c2239300c7b))
-   **frontend-statistical-report:** change in translation , fix in top-items ([b7f68c9](https://github.com/boring91/injaz/commit/b7f68c987280d8f716350d113dabd6e8c70d7c23))

### Features

-   **backend-user:** implement forget password functionality ([2bf85d9](https://github.com/boring91/injaz/commit/2bf85d909014180bd802c847ce80969c44dc6fab))
-   **department:** add specializations field to department model ([5e81ed5](https://github.com/boring91/injaz/commit/5e81ed5e6c3875ec25508d5cfb77b474aacc4ee3))
-   **front-end-change-password:** removing not used scss files ([88e8e83](https://github.com/boring91/injaz/commit/88e8e8321718a7460b448853538fa0fb374c2d46))
-   **front-end-forget-password:** adding `ForgetPasswordService` and adding password,email validation ([9c1f446](https://github.com/boring91/injaz/commit/9c1f44674015e487f51f61c28fb97e10d79a6532))
-   **frontend-login-forget-password):** adding enter confirmation code component ([f6991d7](https://github.com/boring91/injaz/commit/f6991d7ae528b988d8540483f17a19e91313f028))
-   **frontend-login-forget-password:** creating new component for ([9e63f99](https://github.com/boring91/injaz/commit/9e63f99c8f2062281f2afdeffab6b46e3a80c3ff))
-   **frontend-login-forget-password:** removing the enter confirmation code component ([6f98a77](https://github.com/boring91/injaz/commit/6f98a7717892fbc0582f4411bbb45fbfe21f0fab))

# [1.75.0](https://github.com/boring91/injaz/compare/v1.74.2...v1.75.0) (2024-05-16)

### Features

-   **dashboard:** add new section for `dashboard-top-items` ([9312652](https://github.com/boring91/injaz/commit/931265295205d035b526ec30f5385a00ec31c66f))

## [1.74.2](https://github.com/boring91/injaz/compare/v1.74.1...v1.74.2) (2024-05-14)

### Bug Fixes

-   **frontend-operation-details:** operation details fixes ([3129df9](https://github.com/boring91/injaz/commit/3129df9edf453105e02f12d3f58d9321c795a704))

## [1.74.1](https://github.com/boring91/injaz/compare/v1.74.0...v1.74.1) (2024-05-13)

### Bug Fixes

-   **backend-partnership:** remove partnership permissions from getting partnership details ([b2b4bc6](https://github.com/boring91/injaz/commit/b2b4bc643481e025c81cff4a545714b2326d5e3a))
-   **backend-partnership:** remove unneeded permission in `export` method ([4c67ae9](https://github.com/boring91/injaz/commit/4c67ae98960ec8d0332bea966ec9bfb19e40142a))

# [1.74.0](https://github.com/boring91/injaz/compare/v1.73.0...v1.74.0) (2024-05-13)

### Bug Fixes

-   **backend-partnership:** fix partnership reviewer issue ([d780699](https://github.com/boring91/injaz/commit/d780699180ec8b558d52a514448796e98553c7cc))
-   **backend-partnership:** set `partnershipContract` required in `partnershipTerminationRequest` ([c9dd3d2](https://github.com/boring91/injaz/commit/c9dd3d233a876a55bbc30123fc16a75979925e55))
-   **backend-partnership:** update partnership status migration add missing queries ([8169e33](https://github.com/boring91/injaz/commit/8169e33d4cdfa7ef93688d87844a4c111e18cff6))
-   **frontend-dynamic-page:** add `label` to `action-button` component ([419f6ee](https://github.com/boring91/injaz/commit/419f6eec6430c1e23f1d4ec14bdfd850fe9be1dd))
-   **frontend-dynamic-page:** refresh items after change details page ([a8e75d7](https://github.com/boring91/injaz/commit/a8e75d7edcfb6741e8a6d7e145b555932c00024b))
-   **frontend-dynamic-page:** remove permission from the column options in table list ([cd2e3c9](https://github.com/boring91/injaz/commit/cd2e3c9a1bf75c25e5bc9c513e963297866eec10))
-   **frontend-dynamic-page:** use `translate` pipe to translate value ([6dfa519](https://github.com/boring91/injaz/commit/6dfa519ad28e81fc5349b509e36d89f3d26681cc))
-   **frontend-dynamic-page:** use dynamic flow type instead of static one in `table-list` component ([db2ac3e](https://github.com/boring91/injaz/commit/db2ac3ef80c4b68b01de2c4fd516edced7d57128))
-   **frontend-misc-api:** add missing params to request if no cache is used ([739af18](https://github.com/boring91/injaz/commit/739af1851500f3a73b330f04bb79193f5433163f))
-   **frontend-partnership-contract:** enhance details page ([964e738](https://github.com/boring91/injaz/commit/964e738fda3e36084653bbcd22e23129ca8b6cb4))
-   **frontend-partnership-contract:** fix init items in new terminated request page ([64cc461](https://github.com/boring91/injaz/commit/64cc4611a5e589bf3ba371560f52e1d024ff3575))
-   update translated string ([8347a0c](https://github.com/boring91/injaz/commit/8347a0c15bd082f8b4691ec7ca7dec0d1ab9a763))

### Features

-   **backend-flow:** pass `userId` param to `OnMove` method ([0b61b2a](https://github.com/boring91/injaz/commit/0b61b2af291c67f219e71d04ef01577a4170e0ee))
-   **frontend-dynamic-page:** add `mode` event emitter to `new-page` component ([c5a8357](https://github.com/boring91/injaz/commit/c5a8357fd21afec441fb356b28cecef237997f2d))
-   **frontend-dynamic-page:** add ability to navigate to details page after creation ([77e0e58](https://github.com/boring91/injaz/commit/77e0e58625fd4a378d86c1464765393359090cbf))
-   **frontend-partnership-contract:** add `terminate` button ([554080a](https://github.com/boring91/injaz/commit/554080aacad3a74bf5756247206ab91636e70ebb))
-   **partnership-contract:** add and remove some partnership contract statuses ([a82ac52](https://github.com/boring91/injaz/commit/a82ac52b9e6c85100310f34ca53af6c36cadf79f))
-   **partnership-termination-requests:** add new feature for `termination-requests` ([ae8a884](https://github.com/boring91/injaz/commit/ae8a884962f0e068d68d6bc20c72e29fa86c9c0a))
-   **partnership:** add ability to show flow button in details page ([5312191](https://github.com/boring91/injaz/commit/5312191e4cbafc8d5b64eee3a69f1b12c61e99bf))
-   **partnership:** add misc endpoint for `partnership-contracts` ([88bf3cd](https://github.com/boring91/injaz/commit/88bf3cdb81f75979d21fb4e4432ec466be68cb61))
-   **partnership:** add new permission for reviewer ([1dcf865](https://github.com/boring91/injaz/commit/1dcf865ea6ba6ed94177d90093c6dc7edc138b43))

# [1.73.0](https://github.com/boring91/injaz/compare/v1.72.0...v1.73.0) (2024-05-07)

### Bug Fixes

-   **backend-partnership:** rename `file` property to `fileName` ([0ff46aa](https://github.com/boring91/injaz/commit/0ff46aa5c14fcb96947ce55b52bdbb3539f85286))
-   **backend-partnership:** rename `partnership_activity_period_attachment` table ([543edc2](https://github.com/boring91/injaz/commit/543edc2838e1793def475b01a03956b2b733b3c3))
-   **backend:** pass department query to can create and can read ([28da96d](https://github.com/boring91/injaz/commit/28da96d2fb05dbc1e2754453649051592caf30bd))
-   correct wrong word spelling ([4fa71f3](https://github.com/boring91/injaz/commit/4fa71f3b61ffbac3fb90a73b0e5d5475944e68de))
-   **evaluation:** renaming and hide un usable filters and centralize login box in excel sheets ([6b065de](https://github.com/boring91/injaz/commit/6b065de58cba13ea3d697469818aff87c7bb3501))
-   **evaluation:** renaming and hide un-usable filters and centralize login box in excel-sheets ([8d19924](https://github.com/boring91/injaz/commit/8d19924cef981b84a1f97977bd9a53ffd6a96155))
-   fix translation strings and use shortcut in if condition ([a609ccd](https://github.com/boring91/injaz/commit/a609ccd1972a9136a81ef20aeca596b6d7a32448))
-   **flow:** add ability to check the note given the next state ([9fb4fd5](https://github.com/boring91/injaz/commit/9fb4fd5a8dc86ae697d43752bac3f356a0dadae5))
-   format kpi results table widths ([430c229](https://github.com/boring91/injaz/commit/430c229527abf23c8d9c7d63f74d6f57c7766fb7))
-   **frontend-capability:** adding new `spaceOnlyValidator` to common and use it ([72c8ec9](https://github.com/boring91/injaz/commit/72c8ec9580abb239f52fa3120074399e120c3a61))
-   **frontend-capability:** fix navigation issue in capability module ([fd0b3ab](https://github.com/boring91/injaz/commit/fd0b3ab7fa21089b4b1d2a92b0f3fcdbceb7297a))
-   **frontend-capability:** import `EvaluationModule` instead of importing `EvaluationService` ([a1cf03b](https://github.com/boring91/injaz/commit/a1cf03b2a034da7767142ab6ab6171ef278f3786))
-   **frontend-capability:** removing `evaluateModule` import ([7cfb968](https://github.com/boring91/injaz/commit/7cfb968c25f8432ad8db42fc66765a1940d02396))
-   **frontend-partnership-contract:** disable attachment button if flow is `draft` or `rejected` ([4272767](https://github.com/boring91/injaz/commit/427276719418c0451a9fe4d32927134baf2fc2be))
-   **frontend-partnership:** enhance `partnership` details UI ([b1e718b](https://github.com/boring91/injaz/commit/b1e718bf58f19a001cea743f876834e14acfec12))
-   **frontend:** provide evaluate service to entire app ([76c59c1](https://github.com/boring91/injaz/commit/76c59c13c9250b954bb337d360bfb0b3afba2b5d))
-   **library:** better formatting for library list tags ([78c20c4](https://github.com/boring91/injaz/commit/78c20c4ef2275598531da9f957d3d92246c2c1ef))
-   **partnership-contract:** add missing implementation for `partnership activity period attachment` ([0201d2a](https://github.com/boring91/injaz/commit/0201d2a34948c4f60a1e00d030147afba2142677))
-   **partnership-contract:** fix disable terms fields ([ded737a](https://github.com/boring91/injaz/commit/ded737a544317faf409af0d14b5f13c6ee5f2f2a))
-   **seeding:** remove Ajman police form seeded data ([57d76fe](https://github.com/boring91/injaz/commit/57d76fe8f163ab7c543c6dc28dba8cd977a28a7b))
-   typo in change log ([5f62778](https://github.com/boring91/injaz/commit/5f6277862b02b33bbc6343fa39ef7c9c084ce94c))

### Features

-   **backend-partnership:** prevent change `partnership` status to `rejected` without `notes` ([a257309](https://github.com/boring91/injaz/commit/a257309728d71330a8a34aa10ba887e86e705325))
-   **frontend-attachment:** add `attachment` feature ([1a6ea4a](https://github.com/boring91/injaz/commit/1a6ea4a8be0e0d7fce6930167f9df749d8835eeb))
-   **partnership-contract:** add `agreementFile` attachment to `partnershipContract` ([8712b1e](https://github.com/boring91/injaz/commit/8712b1e291556caa05e273b5e3a409b8196df627))
-   **partnership-contract:** add `terms` to `partnership contract` ([004d42d](https://github.com/boring91/injaz/commit/004d42dcc41b68584816bc2f3330a56be69cb6e3))
-   **partnership-contract:** add `time frame` field to `partnership contract` ([6104c0a](https://github.com/boring91/injaz/commit/6104c0ad670c6026829c610b2ffe9d437de25fcd))
-   **partnership-contract:** add new attachment field to `partnership strategic goal activity` ([c332f6b](https://github.com/boring91/injaz/commit/c332f6ba4a187b7084aa14b94656b0166d34a8ea))
-   **partnership-contract:** prevent change `partnership` status to `under-review` without `goals` ([a6b0c2e](https://github.com/boring91/injaz/commit/a6b0c2e467dadd998adbd87a59c2fab5558acca6))
-   **partnership:** link `partnership scope` to `partnership field` ([14b27ab](https://github.com/boring91/injaz/commit/14b27abca304dddd1715974194f2a3991e8c729f))

### Reverts

-   add `launchSettings.json` file to gitignore ([987f17c](https://github.com/boring91/injaz/commit/987f17c0ea3819744a71b6133aaddf9866ad72e0))

# [1.72.0](https://github.com/boring91/injaz/compare/v1.71.0...v1.72.0) (2024-04-18)

### Bug Fixes

-   add missing translations ([e6a19b5](https://github.com/boring91/injaz/commit/e6a19b576ce431fabb088a78942445cd2f450153))
-   better translation strings ([7a7b1d8](https://github.com/boring91/injaz/commit/7a7b1d858423bf6c3327514e67a7f44c3e38c525))
-   **evaluation:** add evaluation permissions for kpi and risks ([5544628](https://github.com/boring91/injaz/commit/5544628d526bd7920a4523a5510e8dd6dbfe693a))
-   **evaluation:** allow users with evaluate permission to read evaluations ([80d8f22](https://github.com/boring91/injaz/commit/80d8f22a5e211c6851e486f7189ae8ca3352e49c))
-   **evaluation:** evaluation instance get dto wrong score calculation ([6dc28b5](https://github.com/boring91/injaz/commit/6dc28b5b9af4610404fc84c6626181f209941830))
-   **evaluation:** update stats and list when evaluation is updated ([fa9ad8a](https://github.com/boring91/injaz/commit/fa9ad8aae0ed924ca9e48c6a5649f3d384e50a36))
-   **frontend-data-entry:** fix comments from pull request review ([e24d889](https://github.com/boring91/injaz/commit/e24d889331ea511d7de744c68348cf508f4eb1c2))
-   **frontend-details-pull-request:** use alert , merge with develop ([689a6b9](https://github.com/boring91/injaz/commit/689a6b98b2dc41763afcc22be9c70ad84df00a51))
-   **frontend-partnership-contract:** add total degree in partner-evaluation-dialog ([b19bc30](https://github.com/boring91/injaz/commit/b19bc300309d319da528829a3b0182e689f32953))
-   **frontend-search-input:** add dynamic search input ([f1eceab](https://github.com/boring91/injaz/commit/f1eceabc37b10cfbbd5e75ce840d196a0387e2e8))
-   **frontend-search-input:** comments in pull request ([70428ce](https://github.com/boring91/injaz/commit/70428cef8fcd8696ae09783e39517d6ba2d88f89))
-   **frontend-search-input:** implement search-input ([7026190](https://github.com/boring91/injaz/commit/7026190d39503146a78e3680823bdde2e166d8c0))
-   **kpi:** remove unnecessary element in alert prompt ([34de17d](https://github.com/boring91/injaz/commit/34de17d44f717c82aade66791ebd3be55bb0bece))
-   **mnm-form:** fix date input disabling and enabling feature ([6b4960c](https://github.com/boring91/injaz/commit/6b4960cb19b28c2dfb8cbe0b2676a6f3d5111196))
-   **naming:** rename national kpis to link with kpis in plan create page ([2ac9e82](https://github.com/boring91/injaz/commit/2ac9e8214609180eb2d02a1f49c4859f2fcf5feb))
-   **partnership-contract:** add migration to fill in the sector type for contracts ([122d1bd](https://github.com/boring91/injaz/commit/122d1bdc956a10f82e8c08b1b0ac43393cdd8c15))
-   **partnership-contract:** add missing pattern type ([e2da5d4](https://github.com/boring91/injaz/commit/e2da5d439f84c179d0b763442216739f5ef8da75))
-   **risk:** allow full access users to read all risks in all departments ([b2a1939](https://github.com/boring91/injaz/commit/b2a193943bf3d7607204d4b17c48a60a08d05589))
-   **risk:** calculation of section to levels ([2d9d054](https://github.com/boring91/injaz/commit/2d9d05438a79353af278ca5a1234f51f91e4695f))
-   **risk:** fix strategic goal chart parameter name ([e02f1d1](https://github.com/boring91/injaz/commit/e02f1d1ea6ab72586aaa1495e867e8ce250de78c))

### Features

-   **database:** update connection string ([1e4b9ef](https://github.com/boring91/injaz/commit/1e4b9ef02e92fd935b9d0208b6a01c8cc87f4bfd))
-   enable multiple select statuses for filters ([3ff5019](https://github.com/boring91/injaz/commit/3ff5019723456ab301ab5fe7338467b85896586e))
-   enable multiple select statuses for filters ([315d569](https://github.com/boring91/injaz/commit/315d56901966a712952b47748c2555813910de4c))
-   **frontend-data-entry:** change modal result to input ([d21c2b4](https://github.com/boring91/injaz/commit/d21c2b4c46d1f506ae1bd5994bdebf042ba50081))
-   **frontend-flow-transaction-history:** add fixed height to container ([0f0b26a](https://github.com/boring91/injaz/commit/0f0b26a3ef901f8a60827ec75ff2997a27db7681))
-   **frontend-kpi-details:** add warning when no results found ([64306bb](https://github.com/boring91/injaz/commit/64306bb6eff4229dee48a08dd861667d1ca3b67b))
-   **frontend-notifications:** change notifications position ([771090c](https://github.com/boring91/injaz/commit/771090cf5a2b85a46a588cb8ea81b9671a9f035f))
-   **frontend-sidebar:** expand item when parent route exist ([968b21f](https://github.com/boring91/injaz/commit/968b21fa8aa12629e52c3f5613d7bc1ffa41c1bc))
-   **frontend-statistical-report-:** add sort and filters by years ([83d5605](https://github.com/boring91/injaz/commit/83d56056c5d4b37942d52f700480f21ba0cbc7e4))
-   **git:** exclude launchSettings.json from git ([3fdf69e](https://github.com/boring91/injaz/commit/****************************************))
-   **kpi:** fix names and set the default value of data entry method ([5a85b20](https://github.com/boring91/injaz/commit/5a85b208119e44f4d33e905d35f208d7b0de9048))
-   **kpi:** show felid is special to police only ([de206a8](https://github.com/boring91/injaz/commit/de206a8a54a2fa07110aae05a868b067ddde68c8))
-   **partnership-contract:** add `sectorType` field to `partnershipContract` ([ad5fe69](https://github.com/boring91/injaz/commit/ad5fe69b84b0194d520f792bcb46aa1896fc0976))
-   **partnership-contract:** add ability to update the partnership title on all statuses ([351d573](https://github.com/boring91/injaz/commit/351d5735255060dedfd4a89c70f385517121c8aa))
-   **partnership-contract:** add more sheets to `partnership-contract` excel file ([2086287](https://github.com/boring91/injaz/commit/20862877f2b2b475b17fadb22bc962ec0f39445f))
-   **partnership-contract:** add some features to `BaseExcelFile` Generator ([232f350](https://github.com/boring91/injaz/commit/232f35050f9cd92fa349130b5f96beed1d5b4089))
-   **partnership-contract:** remove partnership contract `endDate` field ([e02a0ac](https://github.com/boring91/injaz/commit/e02a0ac451051864b74a3476973c9b2f4b7b2f27))
-   **report:** add status publish filter to statistical report ([b2c6c74](https://github.com/boring91/injaz/commit/b2c6c74f870189deb1442ae1720643d2fafe6e5b))
-   update status cases search filter ([b91963b](https://github.com/boring91/injaz/commit/b91963b4215f2cee4cb8e0ca26d7e78408e0c146))
-   update status keys in statistical filter report ([08ce421](https://github.com/boring91/injaz/commit/08ce421188d47a7c9b4cd5e5c8e63ef851371150))

# [1.71.0](https://github.com/boring91/injaz/compare/v1.70.0...v1.71.0) (2024-03-29)

### Bug Fixes

-   **evaluation:** add type to instances and fill it in ([ed87127](https://github.com/boring91/injaz/commit/ed8712787d38424217bb596632013a19ea6740e1))
-   **evaluation:** set english name of standards and bands to arabic if not provided ([8704a30](https://github.com/boring91/injaz/commit/8704a308694f6845ad783dac38435f82c183922a))
-   **risk:** add risk evaluation dashboard to sidebar ([cc0b055](https://github.com/boring91/injaz/commit/cc0b0550f6c35abf8f183281824af528d85ecf37))

### Features

-   **evaluation:** add endpoint for statistics ([e3c20c9](https://github.com/boring91/injaz/commit/e3c20c97dce0a6be267dd13f36f1b84262eb3f91))
-   **evaluation:** add evaluation score bands (apis + ui) ([846f09e](https://github.com/boring91/injaz/commit/846f09e61a4138d5e6d1336538f402b4790a6dde))
-   **evaluation:** add evaluation statistics component ([c0d349a](https://github.com/boring91/injaz/commit/c0d349a1100baf77d470fbeb4eed6456a43882a3))
-   **evaluation:** add filter by evaluation status and bands for kpis and risks dashboards ([cdc9d76](https://github.com/boring91/injaz/commit/cdc9d7665a1e61adf576bf923dcceb29d9959595))
-   **evaluation:** add method to return the details of evaluation score ([0c60d89](https://github.com/boring91/injaz/commit/0c60d8901e9f6604d60b769d2a896154eff20959))
-   **kpi:** add kpi evaluation dashboard ([0c48b02](https://github.com/boring91/injaz/commit/0c48b02a8b738101aed4ecca3a677daad1b9ee4a))
-   **risk:** add risk list with evaluation details to evaluation dashboard ([9eaa9cc](https://github.com/boring91/injaz/commit/9eaa9cc3a786f5d90590adf48af0113bfc85d854))

# [1.70.0](https://github.com/boring91/injaz/compare/v1.69.0...v1.70.0) (2024-03-20)

### Bug Fixes

-   better translation label ([13cf0c2](https://github.com/boring91/injaz/commit/13cf0c2b17ada0752deaa391c5e95856278784c6))
-   **frontend:** add whitespace pre line styling to capability and operation descriptions ([42969c6](https://github.com/boring91/injaz/commit/42969c619f312575e30642e0ebf11e834b035360))
-   **frontend:** revert changes to value loading component ([dbf24eb](https://github.com/boring91/injaz/commit/dbf24eb8b2fdc6222e255f708a6d538f88882db7))
-   **kpi:** swapped period chart with result target chart in kpi result and diagram component ([92b2ebc](https://github.com/boring91/injaz/commit/92b2ebc61f69a5d19942180bcadf2a80308bb4d6))
-   **kpi:** use mnm form select instead of custom select for decrease is best ([61f4386](https://github.com/boring91/injaz/commit/61f4386ee8924bf7044a594aa0254bfd5a67ad30))
-   **operation:** remove procedure prefix ([e061c19](https://github.com/boring91/injaz/commit/e061c19285644df23e105027c8c709dafe9f1b38))
-   **risk:** exclude code/order uniqueness check when code/order equals to 0/empty string ([bee6b3e](https://github.com/boring91/injaz/commit/bee6b3eb18e6a97da47dceeb488c1b523f7f0bda))
-   **tournament:** add go to team button in team section in standard detail page ([af5eb2a](https://github.com/boring91/injaz/commit/af5eb2a70982d2d95848ed4ea45b8dd8375a6322))
-   **tournament:** add overdue label for late tasks in task table ([1215370](https://github.com/boring91/injaz/commit/12153702967e1e70b99dd336a2c372da4858dd49))
-   **tournament:** spacing and overflow between sections in subtask detail page ([6ecc40d](https://github.com/boring91/injaz/commit/6ecc40dead0208010eabb22a79cc4e7690f33b4a))

### Features

-   **frontend-details:** add white-space ([076e3aa](https://github.com/boring91/injaz/commit/076e3aae6cb7d5d8dfe25522a92e6cae73cacd0f))
-   **frontend-kpi:** change decreaseIsBest to select , route to details when edit ([40fe0f9](https://github.com/boring91/injaz/commit/40fe0f9af1d006ed740bc0bdf75011c8d8fb64a7))
-   **frontend-kpi:** rearrange details buttons ([9db2ce7](https://github.com/boring91/injaz/commit/9db2ce79319cade5d8eb327c6b81dcbbdb89ac18))
-   **frontend-toaster:** change position of notification ([a565d64](https://github.com/boring91/injaz/commit/a565d64cb6308539a358873c3d697f9ef861da1a))

# [1.69.0](https://github.com/boring91/injaz/compare/v1.68.0...v1.69.0) (2024-03-17)

### Bug Fixes

-   **frontend:** `ProgressBarInputComponent` throws an error when not part of a form group ([a636245](https://github.com/boring91/injaz/commit/a636245e3c90a77addc3b27eaeddbece5d6b6ff1))
-   **frontend:** better styling for sidebars ([6e9e4a4](https://github.com/boring91/injaz/commit/6e9e4a46ec018131f827db0fa2690e74bf9275f8))
-   **frontend:** better typing for on change method of `ProgressBarInputComponent` ([ff84bd5](https://github.com/boring91/injaz/commit/ff84bd507dd3dfdc0be1faa448d82357aa975832))

### Features

-   **benchmark:** reduce list table width and re-organize the columns order ([429c264](https://github.com/boring91/injaz/commit/429c264e4cc37dcc79a54f244c575ecbdb5c6a8d))
-   **frontend-opportunity:** add ControlValueAccessor ([ba936c1](https://github.com/boring91/injaz/commit/ba936c1b08c3ddd063ff74e574941f8e2b272547))
-   **frontend-opportunity:** add progress bar in completionRate ([657bec4](https://github.com/boring91/injaz/commit/657bec4a3168b8abc96a52232708a669c2db3367))

# [1.68.0](https://github.com/boring91/injaz/compare/v1.67.0...v1.68.0) (2024-03-16)

### Bug Fixes

-   **frontend:** hide risks management for police ([139c2ec](https://github.com/boring91/injaz/commit/139c2ecd270ca08466dbdb2de8deb06d683f1aca))
-   **frontend:** system settings sidebar now uses the share sidebar ([0f5bf59](https://github.com/boring91/injaz/commit/0f5bf59a9cfe88c29562415a1a241599479db1fb))
-   **risk:** add department dashboard pages to nav bar ([57544b0](https://github.com/boring91/injaz/commit/57544b0e493f1b232cb89476b47ea0d4d6548087))
-   **risk:** link for count overview tiles and matrix cell when department id is null ([3cfb5a0](https://github.com/boring91/injaz/commit/3cfb5a0e6579cb04ca897d5a4e0d04fd5a629f76))
-   **sidebar:** revert to old sidebar and refactor to external component for reuse ([be6ea1e](https://github.com/boring91/injaz/commit/be6ea1e1d6efb0f4a77f8e789929e145c8e160c6))

### Features

-   **frontend-operation:** add operationPartner field in all levels ([e6ddbc7](https://github.com/boring91/injaz/commit/e6ddbc73d7c40841ce593ecc19974a66f25b528d))
-   **frontend-operation:** add operationPartner field in level 4 ([4c6ce04](https://github.com/boring91/injaz/commit/4c6ce04be580803173e2bda47758dd28a9ee9d84))
-   **frontend-operation:** add operationPartner field in level 4 ([82c806b](https://github.com/boring91/injaz/commit/82c806b819d88f08e576a86caafb4ba6720948b3))
-   **risk:** add department dashboard ([453af92](https://github.com/boring91/injaz/commit/453af92a7b08b2d561eeafe9f83025c44f404de1))
-   **risk:** add department profile ([018ba4c](https://github.com/boring91/injaz/commit/018ba4cf67212b97f68901096a813addbcc32835))
-   **risk:** add endpoints for department-related statistics ([79e02d8](https://github.com/boring91/injaz/commit/79e02d860bf944fba4b499d81f7bc02768f677b1))
-   **risk:** add filter by level and strategic goals ([90a4433](https://github.com/boring91/injaz/commit/90a4433aed081d19db43ac6b482b81e62fceb539))
-   **risk:** add risk level overview to dashboard ([a4e3141](https://github.com/boring91/injaz/commit/a4e314131c4643a60bc2ce7f647f1f399f7a810d))
-   **risk:** add risk management strategy overview to dashboard ([b9f7254](https://github.com/boring91/injaz/commit/b9f7254d975981eb1275b1ca0806b986c54764d2))
-   **risk:** add stacked charts for level vs category and strategic goals ([45843d3](https://github.com/boring91/injaz/commit/45843d3ec570cd5daed18737baa01ffdaa23693d))
-   **risk:** finalize risk dashboard ([2d187b1](https://github.com/boring91/injaz/commit/2d187b1b65858eaa6b6714f26c4cd9d778e3b166))

# [1.67.0](https://github.com/boring91/injaz/compare/v1.66.0...v1.67.0) (2024-03-15)

### Bug Fixes

-   **frontend:** is hidden for sidebar ([1cfbc3b](https://github.com/boring91/injaz/commit/1cfbc3b08de03b317c8eff037725712929553f64))
-   **risk:** creating first risk in the system throws an exception ([f17738f](https://github.com/boring91/injaz/commit/f17738f8b7688c357e27be23fd072ef38d11d9da))
-   **risk:** department-scoping for risk evaluation ([595e35a](https://github.com/boring91/injaz/commit/595e35afd1d9ea5787144375b98394527e32820a))
-   **service:** set ownership to `local` for new services ([0291649](https://github.com/boring91/injaz/commit/02916495b27bb315cd699c08e956a01721891797))
-   **statistical-report:** cycle filter was not working ([42746dd](https://github.com/boring91/injaz/commit/42746dd7e78b2abf360b15f0618e41f5d91bc3be))

### Features

-   **frontend-service:** remove and change fields ([24ab6a1](https://github.com/boring91/injaz/commit/24ab6a11da0302ff565f6c751aeb0c7ebd830244))
-   **frontend-statistical-report:** add filter fields ([818503a](https://github.com/boring91/injaz/commit/818503a722e7cf7675fe5befa8aa471d2266605e))
-   **frontend-statistical-report:** add filters ([2c0f919](https://github.com/boring91/injaz/commit/2c0f91947bce00d3f475b1947d43496c60e509e6))
-   **frontend-users:** add permissions button in details ([f620fe1](https://github.com/boring91/injaz/commit/f620fe1b98dbd832eaedcd03c2615e742b4a2b9d))
-   **kpi:** add evaluation ([107b5a4](https://github.com/boring91/injaz/commit/107b5a4e396c503dccbaebcdb96fc1d33a4c103d))

# [1.66.0](https://github.com/boring91/injaz/compare/v1.65.0...v1.66.0) (2024-03-13)

### Bug Fixes

-   add missing translations ([07c85c8](https://github.com/boring91/injaz/commit/07c85c888c32b1c660f7a0fca66484c8e99836f1))
-   **backend:** handle deletion of items connected to risks ([ddf77d4](https://github.com/boring91/injaz/commit/ddf77d4ae2787208bb89c70ec85a255ecc1bfab2))
-   **capability:** add missing edit button ([ce62e17](https://github.com/boring91/injaz/commit/ce62e17821f2fbbb4fbe903f6815fe735c864b74))
-   **controller:** fix Swagger errors related to HTTP action types ([c2ccaa0](https://github.com/boring91/injaz/commit/c2ccaa0a9e8abe1310edf508372e498e311ef261))
-   **department:** handle attached risks when deleting a department ([1100317](https://github.com/boring91/injaz/commit/11003172b93fb1d767f1c86a0a6f0bbb589ee840))
-   **evaluation:** add link to sidebar ([6bfbb46](https://github.com/boring91/injaz/commit/6bfbb46c3c1cd1d1fbc85718b77f013c7648fa96))
-   **evaluation:** styling ([fe62348](https://github.com/boring91/injaz/commit/fe623489aa5fe348f25c7241af0fd5968e027762))
-   **frontend-system/system-setting:** remove conflict from system-setting ([4e2f213](https://github.com/boring91/injaz/commit/4e2f2136a9308d1db953416bede7c12fe086cb00))
-   **frontend-system/system-setting:** remove conflict from system-setting ([dd1abcd](https://github.com/boring91/injaz/commit/dd1abcdc26407a00cd75d11fd6dea9a5cdcf6680))
-   **frontend:** remove unnecessary style ([25a37d5](https://github.com/boring91/injaz/commit/25a37d52ae9d06d7d310b9ed9ac8e860669c0c9c))
-   **kpi:** add missing archive and un-archive buttons ([c7a83c5](https://github.com/boring91/injaz/commit/c7a83c536b5d696920ec769a246b299daec222e1))
-   **kpi:** refresh kpi list after hiding or archiving items ([e8a5f97](https://github.com/boring91/injaz/commit/e8a5f9772e0572e6765ee94543292db9f5001473))
-   **library:** handle linked risk procedures when deleting a file ([45cc6bd](https://github.com/boring91/injaz/commit/45cc6bd7c9a9a3fadb0c2d25cb084cd8fe9454eb))
-   **operation:** exception when getting items attached to deleted library files ([035221a](https://github.com/boring91/injaz/commit/035221aec6cf2c7d8065a6bf699a156e1c7e6001))
-   **plan:** fix styling of action buttons in plan list ([ccc656d](https://github.com/boring91/injaz/commit/ccc656d96f43532f5c86fb0a125595158a9bed00))
-   **risk:** add order property to composite keys ([2a4af2b](https://github.com/boring91/injaz/commit/2a4af2bcdcbfc10c514062cd0db69cdb714eac22))
-   **risk:** add relation between risk and department ([b4e7b2d](https://github.com/boring91/injaz/commit/b4e7b2d67d9e6f16a81e4a5e0cbc463e654047f0))
-   **risk:** add risk link to app dropdown menu ([fff5f13](https://github.com/boring91/injaz/commit/fff5f136ba4226a8f1c17ea353adfd2ac6ab8055))
-   **risk:** better way to show selection in risk matrix ([576b17a](https://github.com/boring91/injaz/commit/576b17aff8f411e370f9cddfa71cd61bbc06dc93))
-   **risk:** ensure order and code are unique ([efd2ad4](https://github.com/boring91/injaz/commit/efd2ad493e1bf0d51879824aadb2757dad0fd209))
-   **risk:** make degree nullable in commands ([f0dea2b](https://github.com/boring91/injaz/commit/f0dea2b2718e6878f01faa20b75fa58570d4df37))
-   **risk:** make order and code fields at the top of then new/edit form ([922803b](https://github.com/boring91/injaz/commit/922803bf9cafd8b9e8e98c56cefcfa9d79014541))
-   **risk:** order impact and probability lists by degree ([69e8f0b](https://github.com/boring91/injaz/commit/69e8f0b970392cfa22b76b7dda7dd16d9c24cd00))
-   **risk:** rename standards to description for risk impact ([9354bdf](https://github.com/boring91/injaz/commit/9354bdfdfafc1d1be263b2b4925f7727bb4200fd))
-   **risk:** setting goals/operations/plans for the risk returns exception ([e64b37a](https://github.com/boring91/injaz/commit/e64b37a6220be6cf35308fc47070425da0f6c183))
-   **risk:** ui related with permission of the user ([b509534](https://github.com/boring91/injaz/commit/b5095341b4869416fa5a01c6d46bb8020817aeeb))
-   **risk:** update label department to owning department ([5803bd7](https://github.com/boring91/injaz/commit/5803bd76448df218d2c1a9b14f6ec2167e6f6d6b))
-   **sidebar:** add missing styling ([df52d25](https://github.com/boring91/injaz/commit/df52d2569938ed3a02fdb1725e58f158f37ed86a))
-   translation ([8411dcc](https://github.com/boring91/injaz/commit/8411dcc8bc0a9e31fb38835f6e88afe0256bff59))

### Features

-   **evaluation:** add ability to delete evaluation instances ([5ae5779](https://github.com/boring91/injaz/commit/5ae57799f45537b00945ee472af2135d66cbc047))
-   **evaluation:** add basic entity evaluations ([6d1d958](https://github.com/boring91/injaz/commit/6d1d95895d5aaca203b1573ac68dc20544ec4137))
-   **evaluation:** add commands and queries ([042261e](https://github.com/boring91/injaz/commit/042261ea9062963afd71076c720d3c9f69de23cf))
-   **evaluation:** add crud ui ([62f8518](https://github.com/boring91/injaz/commit/62f8518da96963f3c383219cd2044d2ed60654ab))
-   **evaluation:** add dtos ([a8b6c20](https://github.com/boring91/injaz/commit/a8b6c20bdbeba7d2e288cb25a38925d5ab67e60b))
-   **evaluation:** add evaluate module to frontend ([fa95149](https://github.com/boring91/injaz/commit/fa95149d1690b45eaf23a18cda480ac061a44da1))
-   **evaluation:** add evaluation instance ability support + implemented on risk ([c30e227](https://github.com/boring91/injaz/commit/c30e22768c82960f0b0b63af7b7e406ff90d7e4f))
-   **evaluation:** add evaluation types misc endpoint ([aaa942f](https://github.com/boring91/injaz/commit/aaa942f62622a89395a30fe7dc6e07f6ef331c1c))
-   **evaluation:** add handlers and controller ([85a13c4](https://github.com/boring91/injaz/commit/85a13c4cc03df6d13a79997244b24da7b40eae05))
-   **evaluation:** add models + migrations ([3dd3cfe](https://github.com/boring91/injaz/commit/3dd3cfe21426aea77e14bfeae418c7c5cadfd40f))
-   **evaluation:** add permission support to evaluation ([5c71069](https://github.com/boring91/injaz/commit/5c71069ee78eedbab8b4f18eefab80586c81de7e))
-   **evaluation:** evaluation type endpoint now scans the assembly to figure out types ([649ded5](https://github.com/boring91/injaz/commit/649ded58c7b70b38bf554c4945ba08352f900f38))
-   **frontend-sidebar:** change sidebar to nz-menu, add nz-drawer in mobileView , ([a723da7](https://github.com/boring91/injaz/commit/a723da75965ad79a5d4e53dd7274a9127bef5156))
-   **frontend-sidebar:** change sidebar to nz-menu, add nz-drawer in mobileView , ([7fe419e](https://github.com/boring91/injaz/commit/7fe419ef9a4b3991b14e8372840cbb33cb55ef86))
-   **frontend-statistical-report:** order table with name and initial year ([4f26ada](https://github.com/boring91/injaz/commit/4f26ada316e8ee9ed92572b3c68a208ac9c5c99b))
-   **frontend-system/system-setting:** add system-setting module ([37e74b0](https://github.com/boring91/injaz/commit/37e74b0f1c2107f0b2043f5456d868de56c1786e))
-   **frontend-system/system-setting:** add system-setting module ([b4bd8f3](https://github.com/boring91/injaz/commit/b4bd8f3c922f31d28e34c2d1e062123963b4abda))
-   **frontend-system/system-setting:** add system-setting module ([d487e72](https://github.com/boring91/injaz/commit/d487e725dfc7e3e440a1df5fdb31d172eb3d1ff5))
-   **frontend-system/system-side-menu:** add side menu in system-list ([4cbb078](https://github.com/boring91/injaz/commit/4cbb078ae8de60cf31fda1e9185050802b9e6199))
-   **frontend-system/system-side-menu:** add side menu in system-list in mobile view ([560504a](https://github.com/boring91/injaz/commit/560504a33c75a5687f606117d88df825e78d8a53))
-   **frontend-tooltip:** remove tooltip if string is empty ([c3c40c0](https://github.com/boring91/injaz/commit/c3c40c0237a4c6b40a204c6dd1901d613c99f9b9))
-   **frontend/dropdown:** change dropdown in tables to buttons icon ([1cd73c8](https://github.com/boring91/injaz/commit/1cd73c839d80dac8a3ffa73b5ec1bd18674777e6))
-   **frontend:** add support for color columns in dynamic lists ([bc01efe](https://github.com/boring91/injaz/commit/bc01efe12a709821cbf1f9efc158d484f5fa8a89))
-   **frontend:** add support for color fields to mnm form ([f71ff62](https://github.com/boring91/injaz/commit/f71ff62b0a20da6bbaabd9f31af22fa9083f1187))
-   **report:** add filters and sorting to statistical reports ([e8dea15](https://github.com/boring91/injaz/commit/e8dea15974cd97a2d1925c44f28819f4e70eaae8))
-   **risk:** add a link between management procedures and library files ([caae7fd](https://github.com/boring91/injaz/commit/caae7fdbe5a780a661c90c8a96f6ce98a37d6e85))
-   **risk:** add color input/column to impacts and probabilities ui ([8ff671b](https://github.com/boring91/injaz/commit/8ff671b79e2f23243de617c122de3fcc4134ba2b))
-   **risk:** add color property to impact and probability ([7a4bba4](https://github.com/boring91/injaz/commit/7a4bba465bce2cb6fed7ab2b96368d4f32fde848))
-   **risk:** add crud end points and handlers for categories ([db3e4da](https://github.com/boring91/injaz/commit/db3e4da772e310991758bf1be03aeb3d2b40b9db))
-   **risk:** add crud end points and handlers for impacts ([098dbeb](https://github.com/boring91/injaz/commit/098dbeb309646058d8c6f3eac1c57d0ea90c59eb))
-   **risk:** add crud end points and handlers for management strategies ([7789249](https://github.com/boring91/injaz/commit/77892494d6f5c0260a3b74f83b53ab47c34fcee8))
-   **risk:** add crud end points and handlers for probabilities ([9f01d25](https://github.com/boring91/injaz/commit/9f01d25e678d87c83a2453fc4953c52437ec5330))
-   **risk:** add crud endpoints for management procedure ([ac39e6a](https://github.com/boring91/injaz/commit/ac39e6a6ed741b2db12beda432b166ed105ae4c0))
-   **risk:** add crud endpoints for risks ([eb43d9c](https://github.com/boring91/injaz/commit/eb43d9cd3e5b80cfd5bd50b3bfb007f692a7e69f))
-   **risk:** add crud ui for risk management procedure ([a4aa7e7](https://github.com/boring91/injaz/commit/a4aa7e7868435e29f79bdae27fc6114b9612b581))
-   **risk:** add danger level to new/edit page ([72a27e3](https://github.com/boring91/injaz/commit/72a27e3b65c5ff40b10f6a3ad085041ff01ea1a4))
-   **risk:** add dto ([7b74e98](https://github.com/boring91/injaz/commit/7b74e98116b86051ce47556d78621b204c4ce792))
-   **risk:** add filters to risk list ([830eab5](https://github.com/boring91/injaz/commit/830eab5d1ac770dca50543d4a1535f228204f2ee))
-   **risk:** add goals/operations/plans sections in the details page ([72368cc](https://github.com/boring91/injaz/commit/72368cca5183c2db2c591f3a84d8b98e9a71003a))
-   **risk:** add matrix component to detail page ([0b24c1e](https://github.com/boring91/injaz/commit/0b24c1e718022dc20563eebe5de7fbe5b13bcbe3))
-   **risk:** add permissions ([43ac6cf](https://github.com/boring91/injaz/commit/43ac6cf9e3ad6d1766987b21f8c5b01e595d620d))
-   **risk:** add risk management links to sidebar ([318196b](https://github.com/boring91/injaz/commit/318196bc347a514f1baccdc0b0e910ef46f4ea5b))
-   **risk:** add risk models and migration ([62927c2](https://github.com/boring91/injaz/commit/62927c20456c154d41377f501f7e6f3b4b1324d5))
-   **risk:** add risk related lists to misc controller ([479845e](https://github.com/boring91/injaz/commit/479845ed1932bb70c74fc9e0fcda27dc1b1efb8d))
-   **risk:** add risk statistics page ([6b6e3d0](https://github.com/boring91/injaz/commit/6b6e3d09322b892411910b87d6f6c10c7743cae1))
-   **risk:** add ui for risk related lists ([79ca0c5](https://github.com/boring91/injaz/commit/79ca0c5b0414082ad4b2f32e7e0831509bcc16c4))
-   **risk:** basic crud for risks ([4866c1a](https://github.com/boring91/injaz/commit/4866c1a06ff4f53ec5bbb8d3bbd87a8feaf71bc9))
-   **risk:** scope risks to users involved with linked departments ([d7b57e4](https://github.com/boring91/injaz/commit/d7b57e4071bcde7c61c374765e050d81a15d7f29))
-   **risk:** seed risk related lists ([65926b8](https://github.com/boring91/injaz/commit/65926b8ead381f55629b876e19ceaf2a0f14822a))

# [1.65.0](https://github.com/boring91/injaz/compare/v1.64.2...v1.65.0) (2024-02-26)

### Bug Fixes

-   **backend-operation:** return link file in `OperationProcedureStepGetDto` & remove unused strings ([9d5578b](https://github.com/boring91/injaz/commit/9d5578b1f58206433f1f37892f8b25eba1856337))
-   **backend-partnership:** show partnership to the created user or to the partnership manager user ([8248256](https://github.com/boring91/injaz/commit/824825610f4ac95611fc012b47f9d9bb909b26e0))
-   **backend-plan:** fix filtering by `from` and `to` dates bug ([2e22f6d](https://github.com/boring91/injaz/commit/2e22f6d08751e406f2f2bb3b06b191f410a5aff1))
-   **backend:** only notify active users for plan and kpi events ([4810d3a](https://github.com/boring91/injaz/commit/4810d3aa2294ba6cf914872e44b7cbc77e868cee))
-   **frontend-operation-procedure:** add missing duration type to details page ([6ffe187](https://github.com/boring91/injaz/commit/6ffe1873f255b6c4e4dc3c87aad69334ed063483))
-   **frontend-plan:** enhance plan list UI ([664034a](https://github.com/boring91/injaz/commit/664034ae6745a385d41ff105acebd09ffebe39d4))

### Features

-   **operation-procedure:** add new columns to `operationProcedureStep` entity ([0fcc213](https://github.com/boring91/injaz/commit/0fcc2132cff10f86c567a384d58ec875d7b7bd03))
-   **operation-procedure:** remove `operationProcedureStepDocuments` model and add some fields ([b4227e7](https://github.com/boring91/injaz/commit/b4227e776292cc955b667226fbfbd3c5552a5908))

## [1.64.2](https://github.com/boring91/injaz/compare/v1.64.1...v1.64.2) (2024-02-15)

### Bug Fixes

-   disable department hierarchy code regeneration ([667b7b3](https://github.com/boring91/injaz/commit/667b7b3650e70c736f006b02b733dec659ebcb26))

## [1.64.1](https://github.com/boring91/injaz/compare/v1.64.0...v1.64.1) (2024-02-14)

### Bug Fixes

-   **frontend:** ensure 'date' in 'tableController' is 'Date' type, not string from URL ([47f83e3](https://github.com/boring91/injaz/commit/47f83e3cfb93e7ad0da294df9b52048ac6130331))
-   **kpi:** do not show achieved bars in achieved chart when kpi is trend ([9cea647](https://github.com/boring91/injaz/commit/9cea647b9e08d6b041c57996ca5850ef5f055735))
-   **kpi:** remove unnecessary code from achieved chart ([0cc36bc](https://github.com/boring91/injaz/commit/0cc36bc798c28cebd7e6afdcc2667adc44bcb348))
-   **kpi:** replace period charts with target-result chart in details page ([b4371f7](https://github.com/boring91/injaz/commit/b4371f773738193ec0cc5dc47c0e14ad32170670))
-   **plan:** search by dates ([4c72f20](https://github.com/boring91/injaz/commit/4c72f20fd3426b690ef6067651550dea46109a04))

# [1.64.0](https://github.com/boring91/injaz/compare/v1.63.4...v1.64.0) (2024-02-12)

### Bug Fixes

-   **frontend-procedure:** add default value for documents array ([320cd0b](https://github.com/boring91/injaz/commit/320cd0b5c76a585e350083fee0b8d5369c5a2f96))
-   **i18n:** add missing translation strings ([ec56d1a](https://github.com/boring91/injaz/commit/ec56d1ac3b6bb0c15e964720068ad376f57eb93b))

### Features

-   **plan-task:** add new column in plan tasks list of `subtasksCount` ([da901da](https://github.com/boring91/injaz/commit/da901dafcf82f0088203e6763ffa826a4f3d5495))
-   **plan:** add filter for `assigneeType` in plan list ([e4c73e7](https://github.com/boring91/injaz/commit/e4c73e7c97b01b3491c90620deecfcd7c22d08ba))

### Performance Improvements

-   **items-list:** improve table `orderBy` functionality so it can order by nesting properties ([40738e6](https://github.com/boring91/injaz/commit/40738e6ead582860b86024cd24272c16743c847a))

## [1.63.4](https://github.com/boring91/injaz/compare/v1.63.3...v1.63.4) (2024-02-12)

### Bug Fixes

-   **add-plan:** fix Cannot read properties of null (reading 'reduce') error ([31dd2f4](https://github.com/boring91/injaz/commit/31dd2f4a6d9a35f09762f4e0fb17015596136b03))

## [1.63.3](https://github.com/boring91/injaz/compare/v1.63.2...v1.63.3) (2024-02-11)

### Bug Fixes

-   **team:** add user to new team throws user already exists ([7e4db9c](https://github.com/boring91/injaz/commit/7e4db9c87b4aa274d84097a3ba50fd13e88bc3e4))
-   **team:** other users endpoint should list only active user ([1169594](https://github.com/boring91/injaz/commit/116959449fe7386ecdbebb2c9f11a14fc7d38fe3))

## [1.63.2](https://github.com/boring91/injaz/compare/v1.63.1...v1.63.2) (2024-02-08)

### Bug Fixes

-   **backend-misc-parent-operation-list:** correct calculation of parent code ([e31300a](https://github.com/boring91/injaz/commit/e31300a39e5c0c9828d7d6c6e365c77441746a57))

## [1.63.1](https://github.com/boring91/injaz/compare/v1.63.0...v1.63.1) (2024-02-08)

### Bug Fixes

-   **kpi:** set achievement to null if either the target or result is null ([185d289](https://github.com/boring91/injaz/commit/185d2891127134ead2b2b443ceda273e82cdb6b1))
-   **kpi:** update charts to display achieved values rather than results and targets ([41da8b2](https://github.com/boring91/injaz/commit/41da8b26754a1514ba13d2361ed2e422bfd75641))
-   log the user out if they change their password ([9050011](https://github.com/boring91/injaz/commit/90500119b7dced315c905e4c567d7ee0a7498c5d))

# [1.63.0](https://github.com/boring91/injaz/compare/v1.62.0...v1.63.0) (2024-02-08)

### Bug Fixes

-   default optional fields to not required and not enabled ([41b9ea4](https://github.com/boring91/injaz/commit/41b9ea48b9bc157d2a305f161e0365bfd4aa6ed3))
-   **frontend:** library file field does not auto select newly created files ([3d24638](https://github.com/boring91/injaz/commit/3d24638f93661ee21cf2b5928a00034a5639d337))
-   **kpi:** apply target zero in calculating achieved value ([be55a5f](https://github.com/boring91/injaz/commit/be55a5fb5c15987af609adc03e24a6167c33c472))
-   **kpi:** show warning message when target and target zero are set to zero (or none) ([5f29288](https://github.com/boring91/injaz/commit/5f29288da7d4a3f96056c5d4661a48913c0ccc71))

### Features

-   **kpi:** add target zero field to result ([d1101e8](https://github.com/boring91/injaz/commit/d1101e86ff39e0c4392778ba8a34d9bf9d4b8593))
-   **kpi:** hide target zero field for results with increasing directions ([e9f273a](https://github.com/boring91/injaz/commit/e9f273aa92764510edc33103ecc9fc0804e9d843))

# [1.62.0](https://github.com/boring91/injaz/compare/v1.61.0...v1.62.0) (2024-02-07)

### Features

-   **strategic-goal:** add more optional fields in `strategic-goal` ([1b6f886](https://github.com/boring91/injaz/commit/1b6f8867bd4cde94408c3b7da1c97011026c9526))

# [1.61.0](https://github.com/boring91/injaz/compare/v1.60.0...v1.61.0) (2024-02-05)

### Bug Fixes

-   **backend-operation-policy:** renaming dtos ([298f7fc](https://github.com/boring91/injaz/commit/298f7fcea2e24fac16330038458a6d9d778e87de))
-   **backend-plan:** add down method for `UpdateTeamUserLink` migration ([b669731](https://github.com/boring91/injaz/commit/b669731e945a2cdf7d9af56d366cc4c2ff20a6c4))
-   **backend-plan:** add missing `Risks` property to `PlanEditDto` ([157497d](https://github.com/boring91/injaz/commit/157497d39ac68b9f3d0b479ec46f41bb4d7de64f))
-   **backend-plan:** check if `policies` or `dependencies` is null before checking duplication ([0bfdf40](https://github.com/boring91/injaz/commit/0bfdf40a92a06cd01143c5249b8c613fa91178d0))
-   **backend-plan:** correct namings ([5553515](https://github.com/boring91/injaz/commit/5553515dd2d73d1473adc8094255b81d051b36fb))
-   **backend-plan:** fix issues in plan ([3e9b869](https://github.com/boring91/injaz/commit/3e9b86914e8a0aef5d4bccd33f6db32947636da5))
-   **backend-plan:** fix some bugs in plan ([70ec5cd](https://github.com/boring91/injaz/commit/70ec5cd9987050750c0f4a23bd3e5a6878679780))
-   **backend-team:** refactor team user addition function ([7874a28](https://github.com/boring91/injaz/commit/7874a288ea9c44e1623da7da381f5bd641c274e2))
-   **backend-team:** remove team user link isLeader prop ([6d6c738](https://github.com/boring91/injaz/commit/6d6c738eb0a872c80dae97ff26c1e61b9970b12e))
-   **backend-team:** Update team user positions through migration ([a6387d8](https://github.com/boring91/injaz/commit/a6387d8014bfbe1e61d49447c2cae91d010401c7))
-   **backend:** always run seeder and `sqlFunctionUpdater` ([f9294dd](https://github.com/boring91/injaz/commit/f9294ddc2ef3e21ac44e9a7426bba42d500c01da))
-   **backend:** default flow does not calculate rejections properly ([2e4e76f](https://github.com/boring91/injaz/commit/2e4e76f3b81a1f916198b0c190680a5517b20544))
-   **backend:** typo in translation ([268f416](https://github.com/boring91/injaz/commit/268f4166fb04042da1c56d6e5ef0385a2d8f35ac))
-   **frontend-kpi-update-result:** hide bar chart label in `period-breakdown-chart` component ([9257e8d](https://github.com/boring91/injaz/commit/9257e8d30a9bbbd102e0d364fe00ff8d6f263298))
-   **frontend-injaz:** enhance `list-field` component UI ([8dbc73c](https://github.com/boring91/injaz/commit/8dbc73c99cbe44e64c2048899a4e59035fcf0ce7))
-   **frontend-mnm-form:** add methods to `hide` or `show` fields even if it's section not field ([21b84ce](https://github.com/boring91/injaz/commit/21b84ce203dae32f960c6f3be06872fb4ff92a8c))
-   **frontend-plan:** add safe navigation operator to prevent issue and remove unused functions ([eab8fac](https://github.com/boring91/injaz/commit/eab8fac6e1b67b77940e9d222f0932c14c9ddcfe))
-   **frontend-plan:** fix and refactor plan kpi list component ([8b85f03](https://github.com/boring91/injaz/commit/8b85f03f7e588aab76b039f56162519e5367f2b0))
-   **frontend-plan:** fix resetting form control value in optional sections and fields ([e94114c](https://github.com/boring91/injaz/commit/e94114c875f26a64650ca0c7c59a844f3de46dc4))
-   **frontend-plan:** fix resetting value when change optional fields or sections and clean the code ([7730b64](https://github.com/boring91/injaz/commit/7730b644003795a3dc9d993945a9ba027765d5ec))
-   **frontend-plan:** show `government_strategic_goals` only if the field is enabled ([7a2fb0d](https://github.com/boring91/injaz/commit/7a2fb0d7f5d9a01eadfb8752520e8af5c7d5a76c))
-   **frontend-team:** fix and refactor `team-member` dialog ([b7b2064](https://github.com/boring91/injaz/commit/b7b20647d12aeb4b78b50eeb00cc794009097bb6))
-   **frontend-team:** remove useless property in dialog fields ([2ca6313](https://github.com/boring91/injaz/commit/2ca631342790a51e525adb69d757d29d96a446a4))
-   **i18n:** fix translation strings related to plan ([e8bed10](https://github.com/boring91/injaz/commit/e8bed10c20caf02c6c3bfb866290bcffaa841c0b))
-   **kpi:** add charts for kpi result breakdown ([d71814d](https://github.com/boring91/injaz/commit/d71814d073dc904317fec938545e703964eb7809))
-   **plan:** only show tasks section in details when plan is loaded ([ca9269e](https://github.com/boring91/injaz/commit/ca9269efc3bdc8e7e4e2aa36518cdf3f31da6b24))
-   **plan:** rename `willAchieve` property to `achievementTarget` in database ([17480dd](https://github.com/boring91/injaz/commit/17480dd4780d58750b863015ccf47e144ca102a1))
-   **plan:** saving tasks return plan not found error ([439892f](https://github.com/boring91/injaz/commit/439892fdba0d777cd952269e1eacca5c775c3425))
-   **plan:** updating subtask's periods duplicates them ([52aca89](https://github.com/boring91/injaz/commit/52aca89e9938953214fa501715c2d382b0f8c589))
-   reset ng zorro default style ([2aab315](https://github.com/boring91/injaz/commit/2aab31576501444c9d17f2b914276578d5a8d722))

### Features

-   **app-settings:** add optional sections in app settings ([1657808](https://github.com/boring91/injaz/commit/16578082c0ab99d878bb67f3db81407e385c46c8))
-   **backend-ministry-strategic-goal:** add controller ([d8fabe9](https://github.com/boring91/injaz/commit/d8fabe968a54b7dc024ccfd6eab1b90911e6f106))
-   **backend-ministry-strategic-goal:** add dtos ([0f8d5ec](https://github.com/boring91/injaz/commit/0f8d5ec38293bbc6cb04d103a5250c05b5e88509))
-   **backend-ministry-strategic-goals:** add migration ([7c2ad43](https://github.com/boring91/injaz/commit/7c2ad43c35bf0ac93e0c1de869c637d69d390526))
-   **backend-operation-policy:** add migration ([ddae3f4](https://github.com/boring91/injaz/commit/ddae3f45080d023e9b0ce82cded4e66c08175011))
-   **backend-plan-dependency:** add migration ([34dea64](https://github.com/boring91/injaz/commit/34dea6443ca0dbc58573a541ae8309ae2315e60b))
-   **backend-plan-dependency:** implement crud operations ([c49f200](https://github.com/boring91/injaz/commit/c49f2001d83301dcceae262aa34f0b42ce7c6455))
-   **backend-plan-policy:** add migration ([d02ca53](https://github.com/boring91/injaz/commit/d02ca5325869b855de4830b95d52a81286c005ac))
-   **backend-plan-policy:** implement crud operations ([3e32f70](https://github.com/boring91/injaz/commit/3e32f70ac2643dac492ed856c32ba4e4c81ccc2e))
-   **backend-plan-risk:** add migration ([231a4e1](https://github.com/boring91/injaz/commit/231a4e12419bf4aaf1f45685cc9352ba609d837f))
-   **backend-plan-risk:** implement crud operations ([756c067](https://github.com/boring91/injaz/commit/756c067ea8d03faab5a417a4c0390371119fff78))
-   **backend-plan:** add `DbJsonConverterAttribute` and apply fluent configuration ([7617882](https://github.com/boring91/injaz/commit/761788238b3576e1bd40e3e0e56c5a0ab3f531d2))
-   **backend-plan:** add `Description`, `WorkScope` and `ImplementationRequirement` props ([4539d6b](https://github.com/boring91/injaz/commit/4539d6b3603e6258f95a4982cf28a5baf5f87b89))
-   **backend-plan:** add dto and implement add and update functionalities ([14c6e96](https://github.com/boring91/injaz/commit/14c6e96a05de4c2476820f7b125d882b12d367bd))
-   **backend-plan:** add linked plan task kpis controller and handler ([ee11e7f](https://github.com/boring91/injaz/commit/ee11e7f987831d606402b4f910fda6143e69466d))
-   **backend-plan:** add migration ([c5d0323](https://github.com/boring91/injaz/commit/c5d03232165b86ade25372973176d1aa0ba71ae1))
-   **backend-plan:** add migration ([c89a24e](https://github.com/boring91/injaz/commit/c89a24eb001ab663f6e51553c4316c57cb52d583))
-   **backend-plan:** add new fields to `planEditDto` ([51a1cb1](https://github.com/boring91/injaz/commit/51a1cb129d967b04bf6c06ef74734f7656443fa9))
-   **backend-team:** implement team member positions lookup endpoint ([e22be90](https://github.com/boring91/injaz/commit/e22be908a07e413f4632d9cac7b1fb038764de06))
-   **backend-team:** Refactor team user link to include Role, Position, and Experience; add corresponding migration. ([7cd02f7](https://github.com/boring91/injaz/commit/7cd02f7cf6946bb48cf442de71e2789d49395539))
-   **backend-user:** add user rank migration ([febabb5](https://github.com/boring91/injaz/commit/febabb5e579286cad57eaf7762ee129d88ff11df))
-   **backend-user:** Include rank and phoneNumber in user data ([85625c1](https://github.com/boring91/injaz/commit/85625c1be5f03ab145ab64e146f788aee8f0d442))
-   **frontend-ministry-strategic-goal:** add `ministry-strategic-goals` system list ([613c79f](https://github.com/boring91/injaz/commit/613c79f98854260c0dbbb8d4ec8a0dba55647a28))
-   **frontend-plan/detail:** add kpi table with search ([accbb6f](https://github.com/boring91/injaz/commit/accbb6f93f498aae0b71f78793c48fc028b36319))
-   **frontend-plan:** add `dependency` section to plan ([70b22cb](https://github.com/boring91/injaz/commit/70b22cb4aff8c5efa6b846256a9acdfbf5e95929))
-   **frontend-plan:** add `plan-risks` section ([37ab438](https://github.com/boring91/injaz/commit/37ab43870712b13306eb1a5d6a8474084c6bbea4))
-   **frontend-plan:** add `policies` section to plan ([4671266](https://github.com/boring91/injaz/commit/46712661451b85ddf0757422948c763b372c0d45))
-   **frontend-plan:** add new textarea fields to plan ([fac3e07](https://github.com/boring91/injaz/commit/fac3e07a5ba3d020ca325db18eadc4294d23294c))
-   **frontend-plan:** add section note for `dependencies` section ([c1fb0d3](https://github.com/boring91/injaz/commit/c1fb0d30b4d4785d704ab48620751d8cf4cf3bd4))
-   **frontend-plan:** add total value for financial requirements ([f567328](https://github.com/boring91/injaz/commit/f567328b891dacfacdf4394fa5f76a7111a468c8))
-   **frontend-plan:** show `expected-outputs-section` in plan details page ([a44b133](https://github.com/boring91/injaz/commit/a44b133ce4fb966f5cd40baefbc09664e1c70d6a))
-   **frontend-team/detail:** add popup to add user details in team ([377676a](https://github.com/boring91/injaz/commit/377676aa848530fa092146fff913fde2189a8c36))
-   **frontend-team:** replace leader in table with rank ([5862d2b](https://github.com/boring91/injaz/commit/5862d2b3682b2177a71da5b39285fa1862ac3cb1))
-   **frontend-users:** add phone number and rank creation ([67022b5](https://github.com/boring91/injaz/commit/67022b50ab1bfdb916ede3f7b659617e5fdf153a))
-   **frontend-users:** add phone number and rank user details ([0c27fce](https://github.com/boring91/injaz/commit/0c27fce0e20e68925a9b6431468f19eb1963b3fc))
-   **ministry-strategic-goal:** add `ministry-strategic-goal` to misc api ([cc04295](https://github.com/boring91/injaz/commit/cc04295e338f9d34b117588e50dd793c9348929e))
-   **plan-expected-benefits:** add `expected-benefits` section ([6e4701a](https://github.com/boring91/injaz/commit/6e4701a29d1eaee3e1464a1939ede449cba6f8ff))
-   **plan-financial-requirements:** add `financial-requirement` section ([dc80396](https://github.com/boring91/injaz/commit/dc803963f7372f94bdea8823a40219fce0d143ae))
-   **plan-lessons-learned:** add `lessons-learned` section to plan ([8d5bb0c](https://github.com/boring91/injaz/commit/8d5bb0cbdb49014f3daa7f2376c117e4dea22f95))
-   **plan-ministry-strategic-goal:** add `ministryStrategicGoal` to plan ([227cf83](https://github.com/boring91/injaz/commit/227cf83026f726bba20d480805e98dca669cd133))
-   **plan:** add `challenges` section to plan ([a41b4bd](https://github.com/boring91/injaz/commit/a41b4bd8b72155beee3f677b4c22ef581fe3a0e3))
-   **plan:** add `communicationProcesses` section to plan ([3f3fe8e](https://github.com/boring91/injaz/commit/3f3fe8e332c94002e2297ff543afcc701aa75729))
-   **plan:** add `expected-output` section ([80a3ede](https://github.com/boring91/injaz/commit/80a3edee4fe0ea91b0d2de0e1921cf0288b5b5e0))
-   **plan:** show kpi results of last years in plan details page ([713f5ce](https://github.com/boring91/injaz/commit/713f5ceb0c787eb60f9e0d658cd9870ff3ce686b))

### Reverts

-   **backend:** return back the condition for seeding in backend ([03317e2](https://github.com/boring91/injaz/commit/03317e2f85eaf5d10629a9a402df22debdd0584e))

# [1.60.0](https://github.com/boring91/injaz/compare/v1.59.0...v1.60.0) (2024-01-23)

### Bug Fixes

-   add missing translations ([e5d9d86](https://github.com/boring91/injaz/commit/e5d9d86ac863ba3834540b547eadc0ac57ae727c))
-   **backend:** remove repeated dependency from main project ([e37ceac](https://github.com/boring91/injaz/commit/e37ceac1c8a7b4020b6f57dc0dc29adf1287ec71))
-   **backend:** use ldap settings instead of env during authentication ([4e121f4](https://github.com/boring91/injaz/commit/4e121f4ed38c7ab45ff9e19975f6bda74558de8d))
-   **frontend-dropdown:** hide dropdown on click button ([b9bd7c0](https://github.com/boring91/injaz/commit/b9bd7c0fcb965ab1b181f434b629587378bf65f1))
-   **frontend-dropdown:** install ng-zorro use nz-dropdown ([268edd9](https://github.com/boring91/injaz/commit/268edd97b2a02e339de6c79ce53368fa73d5d5ef))
-   **frontend-go-back-button:** prevent user to navigate outside project ([ecc80dc](https://github.com/boring91/injaz/commit/ecc80dcaf4a7727d32ee09eb024cf2664245fee3))
-   **frontend-kpi-result-report:** fix from and to year filter bug ([bbfc1e3](https://github.com/boring91/injaz/commit/bbfc1e33dafa91039dc72b7e4c2675cb560b0f20))
-   **frontend-shared:** fix `go-back-button` to prevent user from going outside the website ([1b9172e](https://github.com/boring91/injaz/commit/1b9172eb805a31af465bae18d12aff4c2df470d6))
-   **frontend-style:** remove tilde from scss import files ([cf22d71](https://github.com/boring91/injaz/commit/cf22d7104d4b6ef17f7103b97f030d9f32aa2774))
-   **frontend-style:** remove tilde from scss import files ([4220d3f](https://github.com/boring91/injaz/commit/4220d3fe17b25de0703a1d4781e7d36c7b1d7795))
-   **setting:** remove ldap settings from app settings json file ([a570bd2](https://github.com/boring91/injaz/commit/a570bd2d5efe58606a1cc1eae15200f64a40bce6))

### Features

-   **setting:** add ldap as field to app setting model ([1308e98](https://github.com/boring91/injaz/commit/1308e985f0cfd24636bf6fcdc51e723ee7cc93e9))
-   **setting:** add ldap setting endpoints and fields in dto ([c6c6db5](https://github.com/boring91/injaz/commit/c6c6db5644bccfc36d82a2cee9d793102134de76))
-   **setting:** add ldap test form ([0a7eee3](https://github.com/boring91/injaz/commit/0a7eee3d54b8013d8314561a0efdd4ea9269e4fa))
-   **setting:** build ui to edit ldap settings ([aea2a11](https://github.com/boring91/injaz/commit/aea2a11d4cd6d93e55b328601468be318d862f49))

### Reverts

-   **backend-appsettings:** revert ConnectionString ([f33a00c](https://github.com/boring91/injaz/commit/f33a00c1e344774aadd102c4785f05be875fa277))

# [1.59.0](https://github.com/boring91/injaz/compare/v1.58.0...v1.59.0) (2024-01-22)

### Bug Fixes

-   **kpi:** add ability to select all result breakdown subcategories ([69db361](https://github.com/boring91/injaz/commit/69db3615963faba43baa4cfc79ee8d6461f73996))
-   **operation:** make in charge as list of names and roles ([27ddf2c](https://github.com/boring91/injaz/commit/27ddf2c1c1e3be18c6c623816645aeab2b8172ab))
-   **report:** translation for kpi report fields ([b0e213a](https://github.com/boring91/injaz/commit/b0e213a2d5f8bf797d2d7253ff4e5eea923ccee2))

### Features

-   **operation:** add `duration`, `type`, and `risk` for procedures ([1db6921](https://github.com/boring91/injaz/commit/1db6921bbc90527329534ea62481b41d64564bc8))
-   **operation:** add `terminologies` field ([240fe9b](https://github.com/boring91/injaz/commit/240fe9bc3903ef0c2e157d22aa25eac0d7f9bd28))

# [1.58.0](https://github.com/boring91/injaz/compare/v1.57.0...v1.58.0) (2024-01-10)

### Bug Fixes

-   **plan:** search by department returns direct and descendants ([7236c12](https://github.com/boring91/injaz/commit/7236c128753d3b07e56ac2a2c764592d616b33f0))

### Features

-   **partnership-contract:** change field to drop down ([0762de3](https://github.com/boring91/injaz/commit/0762de3fc944f7f911518729aca166a1a9c09f87))
-   **plan:** add is repeated field to subtask ([598852e](https://github.com/boring91/injaz/commit/598852eaef3cd0b84a47264daad2b1e46ecf1e56))

# [1.57.0](https://github.com/boring91/injaz/compare/v1.56.3...v1.57.0) (2024-01-09)

### Bug Fixes

-   **plan:** search by strategic goals ([bbb55e5](https://github.com/boring91/injaz/commit/bbb55e5f014939b400f1ab5e31bc9b6b823fb22e))

### Features

-   **kpi:** add export button to category breakdown ([916f33a](https://github.com/boring91/injaz/commit/916f33a4e956c07d48f3a140816db937865bb328))
-   **kpi:** add handlers and endpoint for exporting period value breakdown ([0cf6d86](https://github.com/boring91/injaz/commit/0cf6d86dd1726a4e306d611af70efb3d0092c65b))

## [1.56.3](https://github.com/boring91/injaz/compare/v1.56.2...v1.56.3) (2024-01-09)

### Bug Fixes

-   update github `release` workflow ([cdbb066](https://github.com/boring91/injaz/commit/cdbb0662a76f2d679fd6954c319690d231520f3c))

## [1.56.2](https://github.com/boring91/injaz/compare/v1.56.1...v1.56.2) (2024-01-09)

### Bug Fixes

-   update `repository` property in `package.json` file ([4aeb47c](https://github.com/boring91/injaz/commit/4aeb47c34fa78e34be72aff1cf130fc64db08a97))

## [1.56.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.56.0...v1.56.1) (2024-01-09)

### Bug Fixes

-   update `repository` property in `package.json` file ([aad8d70](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/aad8d70137559f64933f14072f6d090e01d5cb56))
-   update semantic release node version ([680019f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/680019fa213b58674e2ca3f43cc62b23021d440c))

# [1.56.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.55.3...v1.56.0) (2024-01-09)

### Bug Fixes

-   **plan:** add missing translations ([60ed3db](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/60ed3db8b4a513d1cd1df4e55e114d628fa56083))
-   **plan:** update duplicate option icon ([e8f9543](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e8f9543f70b026eb57ab93da4c68af1c20709f18))
-   update semantic release config to run in staging ([7aeb9d3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7aeb9d3b7222db55338e1bcc8c18501a9ece797e))

### Features

-   **plan:** add duplicate option in plan list ([389e820](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/389e820a9692bb32ce4ab096bbb8af89e5b47c6e))
-   **plan:** add duplicate plan command, handler, and endpoint ([0165ce4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0165ce439670dec7c60e27a6db68a2220bdcca76))

## [1.55.3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.55.2...v1.55.3) (2024-01-08)

### Bug Fixes

-   **backend:** increase Kestrel `MaxRequestBodySize` limit ([e139fee](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e139fee2cdb70ac97c38a9e8c2119791d72d1328))
-   **frontend-service:** use `any` to prevent eslint warning ([06e21e0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/06e21e02745e1d47b5891b9121ad2cdaf3238b0a))
-   **plan:** add missing end point `/plan/next-code` ([adfe15a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/adfe15a91ae929d0f1a9e2fce583aedd1d0274cd))
-   **plan:** move plan expression service to module ([5722ab2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5722ab2829499c1f496ff9e75bb75e0928f1781b))
-   **plan:** return success message on removing plans and tasks ([8abd1ac](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8abd1ac52b8b56e30e7302de626be94bda7ea690))
-   **plan:** update subtask did not work ([e320e95](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e320e950681617cd78a6a8bc68c2dde6baef33f5))

## [1.55.2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.55.1...v1.55.2) (2024-01-05)

### Bug Fixes

-   **backend:** increase file upload limit from 60 to 70MB ([1f80b34](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1f80b34a88d83c1a2f7dc56eb2400706027fc233))

## [1.55.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.55.0...v1.55.1) (2024-01-04)

### Bug Fixes

-   **backend:** add on library id for kpi dynamic data entry requests ([acd7b67](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/acd7b67d4ad6949f0d095673397fda78e1c84b6a))

# [1.55.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.54.1...v1.55.0) (2023-12-26)

### Bug Fixes

-   **backend-i18n:** remove total `KPIs` and `departments` from i18n files ([4dbb01c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4dbb01c44813f66792ccc70b0419871cfb406f0d))
-   **backend:** change `MalwareDetectorService` to return true for all calls ([0f77e64](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0f77e640fd84d6691a128dc548459b77ce3959bd))
-   **frontend-library:** remove `export` from `FilterData` interface ([922e355](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/922e355abfcf6790c0b30c385ab17f3180852482))
-   **frontend-library:** remove order from library ([234c580](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/234c5800aa671f077d067a10441723aaed6c8d04))
-   **frontend-models:** temporarily use Date instead of string (team leader's request) 🗓️ ([e86f5bb](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e86f5bb5156ee7be4acbbae82bc274a679611283))
-   **frontend:** add translation and edit back button ([d4869ce](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d4869ce248412bb759b379c5ad66c91753139247))
-   **frontend:** enhance UI ([490bea8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/490bea8459a63138301fd74027792bc0395579ae))
-   **frontend:** remove accidental changes ([d649426](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d6494265cc026b1ad5fadf378bbc8c9285ac43de))
-   **frontend:** remove accidental changes ([3c17a69](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3c17a69779869408976ac94fc51ec6d67fe01b52))
-   **frontend:** remove accidental changes ([7e6ccea](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7e6ccea07fff37e7c91bf05565c18caa013d9b69))
-   **frontend:** remove useless property ([4fa2cad](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4fa2cad18d95a98fe6337bdc2d52390234d3d417))
-   **kpi:** creating new results by non full access user does not work ([2785552](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2785552b29d8403a3ee2d440728edb0b346ff7b0))
-   remove clam service from docker ([b97553e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b97553ea0103e98c11f75e3c0455c2243527c19a))
-   remove clamav dependency in other docker services ([6b21687](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6b21687cdc20a03b3e6f30d92e3fa6a783380905))

### Features

-   **backend-strategic-goal:** add sorting to library list api ([a0d899d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a0d899d4940d2cce21de833bb3a08996e85e38c5))
-   **frontend:** add file size column to file library list ([16ebb01](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/16ebb0120a79d68e2403134cc23c22ed56cf63ec))
-   **frontend:** remove accidental changes ([2cc6db4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2cc6db446cf30fbba561567fcc76d9f7e1ba24f6))
-   **frontend:** sort files library ([9305213](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/93052134d69e65fdefe3cdd62e032aa251709991))

## [1.54.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.54.0...v1.54.1) (2023-12-18)

### Bug Fixes

-   **backend-partnership-contract:** refactor permission list for `PartnershipRead` ([ba5fe6c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ba5fe6c29b84ace8d4802bdd7cad37670d5e27a3))

# [1.54.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.53.1...v1.54.0) (2023-12-13)

### Bug Fixes

-   **backend-strategic-goal:** remove unused dto ([40c8d53](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/40c8d53a8ab9faa38404870c66d2ec8dd55de0de))
-   **flow:** null exception when formatting the notes field in history component ([9a2e74c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9a2e74ca27a3568539d99b62548e0d77e7fc684d))
-   **frontend-misc-api:** use cache in `getList` misc method ([697f03e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/697f03ee38ae065778bd964113af74e3e4140a9d))
-   **frontend-strategic-goal:** add and use `strategic-goal-category` on misc api endpoint ([8debdb8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8debdb817b78aeaa6a1798ca9732f17d5016beb2))
-   **frontend-strategic-goal:** remove unused params ([f10cf98](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f10cf98f90bbd9166c53244c98f284a1250178fb))
-   **frontend-strategic-goal:** rename `details` component to `detail` ([35765fa](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/35765faaf002a2b76a165aaa7de63d5128425c4d))
-   **frontend-strategic-goal:** use `category` instead of `categoryName` ([eb012b0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/eb012b02f57faedf1ce78608c3b56d7f1b2f5424))
-   **frontend-strategic-goal:** use `translateItem` pipe for `strategicGoalCategory` in `detail` page ([da5657f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/da5657f44e5c861fe6f45a0d2f115ca9988f6595))
-   **frontend:** fixed apiUrl link ([e2bba77](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e2bba77f0a9d1ddbef126260d070f4e5751ecd2d))
-   **frontend:** fixed details page in strategic goals ([e6f7df5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e6f7df54933a01a73a2823a450844f4b84a60063))
-   **plan:** add parentheses for is involved expression ([c5ac7ce](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c5ac7ce5ddde2cf436094022b50a572135c83405))
-   **plan:** dropdown menu for subtask approval ([9f64da7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9f64da70984f003e418f31159d62615320e5439c))
-   **plan:** filter count of task and subtask lists inside detail pages ([1ec117d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1ec117d40e0723a1aba06c7a92f8f42cd979fb05))
-   **plan:** set progress status to empty string before sending to api ([bb5f434](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/bb5f43464665db4965e3065082403b9d447f9503))
-   **plan:** task and subtask deletion apis prevent deletion when draft ([af9faf8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/af9faf8c8a3bbf4f8646c7eb730057b6d736fa25))
-   remove useless properties from `ng-select` ([514f1b9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/514f1b94a69d56cafad9b9baa8ff939e05fdb3c6))
-   remove useless rule from eslint ([ec029ec](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ec029ec3c10bdee898539de7e7e5c3f9aab19350))
-   **strategic-goal:** rename `goal-category` to `strategic-goal-category` endpoint path ([79acaaf](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/79acaaf85dca5f3a2bddafaf5cf8bba7b95d64fc))
-   update `categoryName` property in `strategicGoal` list dto ([1ca1255](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1ca1255b781b7a1249800a497e883ceb2416bcad))

### Features

-   **backend-strategic-goal:** add update order functionality to strategic goals ([90e313b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/90e313bc70ab55b8a53d1bfb08bba8e9f12bc768))

## [1.53.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.53.0...v1.53.1) (2023-12-12)

### Bug Fixes

-   **frontend-dropdown:** remove unused property ([7f8a527](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7f8a5279561e11c43c7282c799131d462c0ce551))
-   **frontend:** center dropdown's button ([6a47b55](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6a47b554136005eeca8a5496b091bccd4a715c87))
-   **frontend:** change property name ([963e45f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/963e45f0c33b879f1bd8e76194b32cc3de17adb0))
-   **frontend:** convert scss to tailwind classes ([3dae74a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3dae74a750ac9a67e3a78a65ad77e4b20611bee5))
-   **frontend:** enhance code ([a49906f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a49906f2df628336eeba3779c20909066f34fcb8))
-   **frontend:** fix height dropdown greater than page content ([0bbf784](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0bbf784d38da3b845955e6c6fa6ab4352dd621be))
-   **frontend:** fix hidden dropdown in scrollable ([dfa9e50](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/dfa9e5036190f622b5c88d0ae8995d56e542cd33))
-   **frontend:** fix parents scroll directive ([5a062bb](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5a062bbbc100610f4c6b67a03ccaf5d32cb8e1c8))
-   **frontend:** fix responsive class in tables ([4782288](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4782288fb2e26887e0153865dd162a593c516891))
-   **frontend:** remove center classes ([19b0b1e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/19b0b1ec49e516a3c65da25ef511baf697be562e))
-   **frontend:** remove useless class ([9d764d6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9d764d6063d40ec4fa6b798e6e3bfc5692a4fc5b))
-   **frontend:** remove useless class ([1ca6ef8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1ca6ef82c0612af2fd2293cdc431b3232e30c0fb))
-   **frontend:** remove useless class ([679579c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/679579c2bf76fc2d07b07579ed4df4b680834ed9))
-   **frontend:** remove useless element ([10f08f5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/10f08f51ab61e7f3e09fd068d81f74044385d6b5))
-   **frontend:** remove useless property ([7b3b656](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7b3b6562b54dde1ef78bfe893e834468e0583631))
-   **frontend:** return responsive class for tables ([cca8037](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cca803736ba7ec8dfa9e4830d7e5c7f276190bd7))
-   **plan:** tasks and subtasks deletion permission ([37eaa5d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/37eaa5d9fe67fe9639b58cb65668ccf8d727ac40))
-   **report:** add tag ids filter for kpi report ([7b366fe](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7b366feba9a17df99a32c3496f8205d8b95ab11c))

### Performance Improvements

-   **frontend:** enhance dropdown component ([a3f0a5b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a3f0a5b1d81971134c0eb2b36f55a253aa7c8aed))
-   **frontend:** handle menu position ([6dd9a88](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6dd9a8872a8f9ef1882bc8cba5b2bcdf350e72b3))
-   **frontend:** use scroll parents listener directive ([afc00a3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/afc00a3999a51aae4a56ed28750dc59c1182b057))

# [1.53.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.52.0...v1.53.0) (2023-11-30)

### Bug Fixes

-   add missing translations ([77c3902](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/77c3902ed0a677d7cd57114a65758b7c9ff690cc))
-   **backend:** add missing translation strings ([eed79d2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/eed79d2cf8aa25d4ad83e383c78b054f289ff4de))
-   **backend:** better handling permission check when no user is passed ([70d30ad](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/70d30adbc17ed419e3ba4172fb186ca34ea8a0d8))
-   **backend:** update use count expression for library files to account for dynamic data requests ([732e815](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/732e81511a0554c1c77f38755eb56c52a5d231e9))
-   **flow:** add `isWrapped` option to `FlowTransactionHistoryComponent` ([e52ddde](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e52ddde22f876b72406f7e05371b06863628fdc0))
-   **flow:** add kpi dynamic data entry request as a flow entity type ([742a67e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/742a67e0574a0098e10b4909cb5437fd72911e93))
-   **frontend:** update modal animation ([e66e868](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e66e868909d9a0247ad79f82195c824ed0736027))
-   **kpi:** add authorization for kpi dynamic data entry endpoints ([b950b35](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b950b35497c3db5deea2d9c652b29a22b00311e4))
-   **kpi:** add data entry method to edit dto ([a0edb6e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a0edb6e036b315bb347079aa098b832a596f9594))
-   **kpi:** add dynamic data entry request link to nav bar ([af32b53](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/af32b533a8a1b7b630e79717878ecc35269a7a93))
-   **kpi:** add more info to summary dtos ([96e0d9f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/96e0d9f8f9aabddd4fbf7700cd2e6b2c7e6e40f2))
-   **kpi:** add status to the frontend method `list` for the dynamic data entry request service ([ab24df1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ab24df18e8b931b1a5d96f6f4465165d5b1daf51))
-   **kpi:** allow full access users to approve dynamic data entry without involvement ([ca9d317](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ca9d31764a794d200a8a4a73a623858dd6762c75))
-   **kpi:** disable a, and b data entry for dynamic data entry kpis ([a7509e4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a7509e4aa2e4b50e8e2af2fcd349dafc020c1b1b))
-   **kpi:** format a, and b values in dynamic data entry requests table ([c26d14b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c26d14b5a8fbe45aaef7a2f36f1925fd2dc2b5ca))
-   **kpi:** kpi dynamic data entry does not require involvement with kpi ([d679856](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d679856d6b88b8e59d24a5314e2458caec005ac1))
-   **kpi:** limit data entry requests to kpi with data entry method set to upon request ([db3b880](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/db3b88002c6f95d62265eb06454d877d2444dcbf))
-   **kpi:** loading attached library files for dynamic data entry requests ([441edab](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/441edab5306baf018288fb0ba2871a468896b3c7))
-   **kpi:** make update a and b values handler create a request instead of a direct update of values ([bdacfba](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/bdacfbae53c4ca71b95206f64bb30317ab3a8002))
-   **kpi:** remove double header from period breakdown dialog ([02c52df](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/02c52df0f7ba58fc0a775ac3ea5abf1945ca1aac))
-   **kpi:** set previous kpi records data entry method to upon request ([8c37a96](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8c37a96cf12dd6f1d9a28466eb00e0a54f211379))
-   **kpi:** update kpi result after finally approve dynamic data entry ([7611cea](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7611cea925532b07e600295d63c2128b5f58d77d))
-   **kpi:** updating results returns an exception ([a07e3a4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a07e3a4508ad139e497b969483df92283a0b1bee))
-   **permission:** overriders for kpi result dynamic permissions ([342713c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/342713ce5827e66adb0ffbb54c0f842bfcff3e07))

### Features

-   add kpi data entry method misc api endpoint ([64fb271](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/64fb271d5d5fdce9767f92aa2e0bc2d8fa0b5a11))
-   **frontend:** add kpi data entry method misc api method ([96bf075](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/96bf0752380493852e65743d073eb1987d108a0a))
-   **kpi:** add a controller for dynamic data entry requests ([b109a9d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b109a9d3e695e82333fc22352f51524b2cc22099))
-   **kpi:** add data entry method field for kpi + migration ([f70f8a7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f70f8a7d44589d3fbef555f6ede86256312c4ff5))
-   **kpi:** add data entry method for kpi dtos ([c2b5b47](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c2b5b47e12784583378b1ac1e5622598f80d6653))
-   **kpi:** add data entry method to detail page ([a20870d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a20870d7144ba45aed8541c96eff55edc912e06f))
-   **kpi:** add data entry method to new/edit form ([b5f6736](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b5f673633de9c75fbf7c14a984f78aa1bfbe4e0d))
-   **kpi:** add dtos ([338ad56](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/338ad5662ef7a33ddcfcd9a0d188c633c6aba3aa))
-   **kpi:** add dynamic data entry request frontend service ([78f8770](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/78f8770dbdcee375a1434b8a2af284da1db8d729))
-   **kpi:** add dynamic data entry request models + migration ([ccbbc73](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ccbbc7381d2e46d22ab894af19e9b7d65ebb8313))
-   **kpi:** add filter by status for dynamic data entry requests ([0895028](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/08950282da34885c9de609df66ff99daeb050537))
-   **kpi:** add handlers for dynamic data entry queries and commands ([fd5c52a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fd5c52a67e015c4965a954872d899a3bd66e96ce))
-   **kpi:** add list for dynamic data entry requests ([b7ce918](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b7ce9183665adedf8f6fb799ceddde9d1bb4e3ba))
-   **kpi:** add permissions for dynamic data entry ([0bfe735](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0bfe735a4f75a955a7add41a2e309ae8463f623b))
-   **kpi:** add queries and commands for dynamic data entry ([ab105fe](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ab105fe0b9602a546be24f87d393e7aea39bd5cb))
-   **kpi:** create a dynamic data entry flow service class ([e8fd765](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e8fd7652ccde27f45e12ac0a0301649e95be61dd))
-   **kpi:** handle data entry method during creates/updates ([635914d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/635914d0bd4bfeda6ff92f6235c495f88b9a8b08))
-   **kpi:** sort dynamic data entry requests by creation time ([9f809a9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9f809a99c2f2dde7d312326a2241a295133f1dab))

# [1.52.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.51.1...v1.52.0) (2023-11-29)

### Bug Fixes

-   **backend-i18n:** fix wrong translated string ([30e415d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/30e415d933e8eb99218801192cbd04280bd19025))
-   **backend-i18n:** translate missing strings ([682d349](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/682d349e7a25c1f31b4c3ae8878f098544acc219))
-   **backend-partnership-activity:** on delete activity delete all periods related to this activity id ([4b59335](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4b59335c6c624b7560f8f8c7cc6fed4f2e37ddbb))
-   **backend-partnership-contract:** let `kpis` optional in `CreatePartnershipStrategicGoalActivity` ([5f67dc9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5f67dc9bb6908f3024d21f40aa2905ab2756b350))
-   **backend-partnership-contract:** remove unused imports ([0c59eaa](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0c59eaabf222d98977cb7aa80544f34a5377c9ba))
-   **backend-partnership-contract:** remove unused queries and handlers and add translation string ([d29b7eb](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d29b7ebab1177056afe09c57d88fc3002ae9fc50))
-   **backend-partnership-contract:** rename `properties` in partnership contract table ([8a9853c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8a9853c79d2a65e63ff113c6825d0f939f5597bb))
-   **backend-partnership-contract:** rename partnership controller file ([f9c3c1f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f9c3c1f89f36d661971b2562ec2f078c4ae99a2d))
-   **backend-partnership-contract:** update translation strings ([5a9ea57](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5a9ea57651a9e8820724449d472dedf57c4de2b1))
-   **backend-partnership-contract:** use `OperationWithLevelDto` instead of `Operation` in Create ([1aa465c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/****************************************))
-   **backend-partnership-contract:** use `partnershipGoalId` when deleting `partnershipGoalOperation` ([dc575f5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/dc575f52ebb3160b8ed9a290dbb2cd4b8d1b6a50))
-   **backend-partnership-contract:** use `partnershipWrite` permission for delete methods ([890aa23](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/890aa239e70d188daec0c517dd422f53f242bb8f))
-   **backend:** add `And` extension function to expressions ([fd656a7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fd656a788bdc7641c809416b515104421320c2e5))
-   **backend:** add mechanism to resolve sql function dependencies ([fab2672](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fab267267f9bdb2438caf2306fdc911c37786753))
-   **backend:** add missing approval_state for plans table in migration ([24aba13](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/24aba13e186b4e63bee46535da15a400343ddb0b))
-   **backend:** add missing migration ([80b0ec8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/80b0ec829fb26724e54302433ecdec932b537660))
-   **backend:** disable automatic seeding during development ([0658ecb](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0658ecb811c23132e0cb98c361ee1d479c5b2ae9))
-   **backend:** migration rename ([9f32303](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9f323035eed8bd882381b40ff98be50c27381535))
-   **backend:** reset app settings for dev environment ([bff4f33](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/bff4f334f8697e1a2210f94da993e263908b85e1))
-   **backend:** update ImageSharp package ([d7ecc59](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d7ecc59c5dfce7f635e97bb6dbfd46627f4dcb61))
-   **backend:** update oauth library ([238f9e1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/238f9e1ee783574929366270af0470a0675a237c))
-   **flow:** add `fn_get_flow_transactions` sql function ([400f454](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/400f45475be945a0c2c9d19ac8bdd59cc15ce7d2))
-   **flow:** add `id` to flow transaction dto ([8f0a7db](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8f0a7dbbf53e48fd28eb083b40ca6d5ac5bfa093))
-   **flow:** add ability to default service to not reset the entity ([715f1f0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/715f1f0a21a8f80221ce53fc345f516959812a8a))
-   **flow:** add cqrs support ([eb626ae](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/eb626ae1db401a5c9a48452bc3b7373b50334258))
-   **flow:** add tooltip for flow button ([414e23e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/414e23ecc6ece6ffa0fde905007cd32a411f846b))
-   **flow:** add transactions to db contexts ([82092f2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/82092f298dfa48a840084c09a04836b71fa6b8e0))
-   **flow:** change default approval count to 1 ([cb336c6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cb336c65ef9c5a40d4f03c0d25566c0c958fda27))
-   **flow:** change order permissions to flow permissions record ([d39db34](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d39db345643da4fa8f951867d68f7ce973fc4f71))
-   **flow:** get note from move endpoint ([94eac5b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/94eac5be52b13055d9d578971f2c7d0d3d58466d))
-   **flow:** give users with full access permissions the ability to take any action ([a983673](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a9836737d1f3be9515ff7434c5d554f2a9d25232))
-   **flow:** handle approved:immediate properly ([7cc093e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7cc093e949f83bd598db9f189ebcfb9c6df9e29b))
-   **flow:** note now display break lines in history ([c02700b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c02700bef89521008723689677a8af0bce220e6d))
-   **flow:** remove `approval` from flow classes ([7f8bf09](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7f8bf09855629064cbfd0e44195531c90bafe6f9))
-   **flow:** remove default service dependency on plan ([622be06](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/622be06d0ce8af48d38aa1ab97b7cbb3b15af98d))
-   **flow:** remove flow from capability ([b6c6088](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b6c6088aa6e348ff966fa489391b0508236e6084))
-   **flow:** rename approval flow transaction to just flow transaction ([31fca90](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/31fca908de2c67589d479fb6fb8b1ea7cee5a0f3))
-   **flow:** rewrite `GenerateAvailableActionsExpression` util function ([0cf2f3d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0cf2f3de482baeaa1cbc3c91fea25a050eb6978c))
-   **flow:** wrap flow apis responses with `Result` ([cd823f4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cd823f4672108eacf7cae5ced46064b216770050))
-   **frontend-common:** in `getNestedProperty` return `null` if property not exist ([c527384](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c52738465f96f8377d4bce45b131ee4827edd20c))
-   **frontend-dynamic-page:** add `flow-transaction-history` in detail page ([186e997](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/186e9971e4dd2b313432897aca29eb2ba43f79d8))
-   **frontend-dynamic-page:** add `relative` to action button container ([d35152a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d35152a0decfcab1084d097efcbdffc3740d2d4f))
-   **frontend-dynamic-page:** add new property `hideNew` to prevent new button ([d66cb30](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d66cb30039ff7ee2b88e9bc2c9c393abe2fd62fa))
-   **frontend-dynamic-page:** center buttons in table actions and use td outside component ([72c2fb1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/72c2fb1ec8f5b992d1cf64c1a3bb9ccc5d845c53))
-   **frontend-dynamic-page:** fix modal overflow bug and set some inputs optional in `list-section` ([685bcc3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/685bcc30d63055bd57f4b5bdbe13e74de48a9050))
-   **frontend-dynamic-page:** in `property-value` hide value if is empty ([49fbeef](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/49fbeefffa1013c3aa12d9c2108ed1e3aadbf726))
-   **frontend-dynamic-page:** move style from `detail-page` to `detail-section` ([16d82d3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/16d82d3e59d079c3b5cbfaa3bb068edd8e101c1f))
-   **frontend-dynamic-page:** remove `isSubmitting` from inputs in `new-form` ([df7cdb4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/df7cdb4f5310e355d9b6be67c08715c530860644))
-   **frontend-dynamic-page:** remove useless method and use the other method to show detail dialog ([cbe7e18](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cbe7e18fc73232151bbacc6c75c7f695fe67a068))
-   **frontend-dynamic-page:** use `primary` as default button type in `action-button` ([665fdef](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/665fdefcadd740f096bd8d42646e0809218e3e4f))
-   **frontend-dynamic-page:** use `record` type instead of object ([6adca82](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6adca824e4a8f0dbf9e42c9043882785daa64a7c))
-   **frontend-dynamic-page:** use write permission for `new` button and fix delete callback bug ([66974e4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/66974e431748a4a4722c3982db0a5144bd139588))
-   **frontend-flow:** refactor flow interfaces and add default flow type and move them inside flow ([b5c11ee](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b5c11ee0bba869f57cced865e3907e97e70d7fda))
-   **frontend-http-crud:** check if table controller exists before stopping it ([9846c7a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9846c7a67198f6d8f3f8e4b7460ae5869bffc41a))
-   **frontend-http-crud:** extend `T` in `HttpCrud` class with `id` ([bd01022](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/bd01022881d643c0f034218a4960ad127fbac31c))
-   **frontend-http-crud:** set `url` getter to public in `http-crud` class ([0831a8c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0831a8c5d62423e567190227c85a4b495b0b9b1c))
-   **frontend-injaz:** set line height for title in `content` component ([b55b957](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b55b957127efa4897236002635786168a6de5321))
-   **frontend-partnership-contract:** adjust disabling fields in `partner-activity-dialog` ([09f6b64](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/09f6b647ccf1d737354a0551e2e090ee9e1d159a))
-   **frontend-partnership-contract:** change `activities` string to `events` ([4a91463](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4a91463e2edb38b8202e6ab22eb4cc540353316f))
-   **frontend-partnership-contract:** disable partnership activity attachment button ([8efe50e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8efe50ed52a919ac981c3eb87c657dfa6a6861cb))
-   **frontend-partnership-contract:** separate partnership evaluation degree and total ([42e3ba2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/42e3ba2d8a08595737dbf35f13f43af230567d59))
-   **frontend-partnership-contract:** show `alert` if there is no partnership contract activities ([8018d35](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8018d351c3072078e7dd140279baa7d48f5180be))
-   **frontend-partnership-contract:** update `strategic goals` section title ([407b51f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/407b51fc2d139a70c70ab4a5e650ff9fcaab2dba))
-   **frontend-partnership-contract:** update translation strings ([7aab264](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7aab264e4db3d5ebf78142f4e7b9ae766d1a2498))
-   **frontend-sidebar:** use `partnershipContract` permission for partners section ([9589f31](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9589f3105174965063823b67bbf5b6464d0df0e1))
-   **frontend:** remove useless class in `appComponent` ([7136ff7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7136ff718b7b34dae477f003c8f75d1d1abf80ad))
-   **frontend:** use local api ([e490096](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e490096c53518e7e728ff297bd1a5735f72fcbe0))
-   **frontend:** use record instead of object interface ([8db2c7e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8db2c7e768b6f395e03a8a4c28c6e74208334f9f))
-   **library:** delete related user requests on file deletion ([fdb15b3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fdb15b3f1c9a83ae7fe215c03ad7cdc1983e37c2))
-   **partnership-contract:** show and hide actions buttons only when have access to them ([71e729d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/71e729d38a38af8079ca65f55d79b6b4869d4c0b))
-   **partnership-contract:** update translation strings ([131fe35](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/131fe350931c6b15fcfd3a6b86dca4c4a82d80dd))
-   **partnership-contract:** update translation strings ([72b25af](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/72b25af1b9f9139269daf0ec039333af5e50efbc))
-   **partnership-contract:** use `partnership` permission for sub list ([d8d9a7c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d8d9a7c1ea678fa491fc31a992d311c232328fd0))
-   **plan:** add flow button to list ([8ab31e1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8ab31e193211358aa1d11e1aeae925c11a98b802))
-   **plan:** add flow-related properties to list dto ([cbc5a97](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cbc5a97026af0e8a716fd3a03f6c9b42566ee666))
-   **plan:** approval badge in details page now uses the new flow system ([783aeb4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/783aeb4829230d9ce9e2753d0e314158d9bce5f6))
-   **plan:** flow data migration not reflecting correct state for final states ([7bc9ea5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7bc9ea54451eb5349e8d9992b1ab8e4f968e2b93))
-   **plan:** move approval data from old to new flow service ([46e10e0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/46e10e062c0ce96f275c9b08105d1de1a8ac0cb6))
-   **plan:** remove previous approval process buttons ([64258f0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/64258f0ae08906f0ddff153779f7a961faf9f328))
-   **plan:** translate flow states ([b750f77](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b750f7701d280b25f7d1eb227c352a70ee6ca29e))
-   **plan:** use flow transaction history component in details page ([d0fb059](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d0fb059a75e6e6c878e61aa5b94c728f90b45692))
-   **plan:** use next approving department from flow in list dto ([0d675ea](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0d675eafaddbe5ce5b0e1eb1ed8520728550bc3e))
-   **style:** change the border radius of login card ([317d6bf](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/317d6bffe286d46a30623861e9f0e21e60e520ca))
-   **translate:** add missing translation ([a1b570e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a1b570ef25fd0b64ad6e37df3e67c44d70d896d3))
-   **translation:** add missing translations ([0e245b1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0e245b1e1e3ea0557552f69025ef6c1f51550dd8))
-   **translation:** update translation strings ([a210869](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a210869c8a88c167826a07d545d08f6b8eee9e62))
-   **user-request:** remove items where main entities are deleted ([18ab8f1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/18ab8f14e1d7025874c5e9a2d988f0a807d67aa7))

### Features

-   **backend-misc:** add `PartnershipStrategicGoalService` controller ([f2e4653](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f2e4653b1e1556cc49870931f5b4b9de80bc8f66))
-   **backend-misc:** add `service` request to misc controller ([5aebc45](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5aebc45bcd1719bb61df69ca8de034275091801e))
-   **backend-partnership-contract:** add `export` method with handler ([475774a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/475774ab3b7ed439e271f357901c548621371a0c))
-   **backend-partnership-contract:** add `nationalAgenda` linker to `strategic goal` ([02a294e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/02a294effe7854b49045d7688fe2685a5c900218))
-   **backend-partnership-contract:** add `partnership-operation` controller ([d1e1f73](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d1e1f731c7f2d7bec312b4190171905f7f0a9a72))
-   **backend-partnership-contract:** add `partnership-partner-evaluation` controller ([17b14f3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/17b14f3d174b670115d9c2dd55e0f40d37ea8189))
-   **backend-partnership-contract:** add `partnershipStrategicGoalActivity` crud operations ([013aef5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/013aef5d242c305a965d4a3c9f35a78ccc992d04))
-   **flow:** add `ApprovalFlowEntityLabelAttribute` ([7fd4ad3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7fd4ad3ece26a0c1b97995a738b278a1ad621449))
-   **flow:** add `ApprovalFlowResult` class ([fe3a6c5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fe3a6c51c4406196ae03afdcfb5b9284ab60efc2))
-   **flow:** add `DefaultFlowService` ([f3113d9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f3113d9d464bc1ee6235ee112827a67f157d8506))
-   **flow:** add `IApprovalFlowEntity` and make `Capability` implement it + migrations ([5fe3dce](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5fe3dcea012c76f971128c80e7f9e2f9954dc359))
-   **flow:** add a service collection extension that adds flow services to the dependency container ([5aac3e9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5aac3e9f5ff01e41314181de8f68ad2050e7a494))
-   **flow:** add an endpoint route builder extension to include all endpoints for flows ([8f84054](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8f840548d399bfb8325b42fa8442da1f221536ce))
-   **flow:** add an interface to represent flow services ([e2ece98](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e2ece98bf01b379d2426f3f147c32ef0c0fa09b0))
-   **flow:** add base flow service classes ([7004fbd](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7004fbdadbce539b56a5c03d30b1fd856a9bd846))
-   **flow:** add department id as part of the flow ([b030b30](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b030b30fe2e612049782d8c11377bd431fa65c93))
-   **flow:** add flow item interface ([45f1dd0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/45f1dd0b0f90b6552bab08f68c189e667dd17097))
-   **flow:** add flow move button and form ([279a208](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/279a20842d00c5dcd936035ea09e1a1c1011e9c8))
-   **flow:** add flow transaction model ([f0870ee](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f0870ee72cbeee695689bd1bd7ecce42600019f6))
-   **flow:** add runtime expression builder for action ability ([1eaddf1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1eaddf1b5cda4ed2b3fece6e4e858aee680a09b2))
-   **flow:** add transaction history support ([fd3d2dc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fd3d2dcd41338ab8c5f9e32a8d0a1fa4e349c649))
-   **flow:** apply flow to plan ([7b175aa](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7b175aa030a567680537b83d814129ac79ed7066))
-   **flow:** finished flow transaction history component ([c7b6028](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c7b60285d3d39999d982dce50a25867dc6c74e89))
-   **flow:** implement capability flow using the flow service ([4351d6e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4351d6eadd18e108b9ffe6445cb589ecb0281c9e))
-   **flow:** use service collection and endpoint route builder extensions ([46de832](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/46de832feeb09d254b0b0344618dfdc37f8515a0))
-   **frontend-dynamic-page:** add `badgeColor` property to `property value` ([d2a5643](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d2a5643ebb946c9716580669495b71e3e95219a1))
-   **frontend-dynamic-page:** add `canEdit` & `canDelete` to show and hide buttons ([2f5fba5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2f5fba5b255c0107bc21a08a9f8e9deefff0549e))
-   **frontend-dynamic-page:** add `dialog` to `table-list-data-actions` ([d6eda8b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d6eda8bfbac6c29bf3bbbbcc0ea8f4a1a4183371))
-   **frontend-dynamic-page:** add `flow-move-button` component to table list ([22c6c3e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/22c6c3ed3196d8ce6fcb3332d05479466978e6fa))
-   **frontend-dynamic-page:** add `form` property to table list button ([21c1412](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/21c141208e32c1480337e5c3ee60c78722792a7d))
-   **frontend-dynamic-page:** add `table-list-basic`, `detail-sections` components & refactor module ([a5a04b3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a5a04b3f8f3d31a6c41b92a2be80d53e0005d12f))
-   **frontend-dynamic-page:** add action button to `table-list` component with dynamic linker ([5ab9e9c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5ab9e9c5a4ecc0d9ad1a4b8ac83e16dd4738c2b2))
-   **frontend-dynamic-page:** add new properties to `action-button` component ([d4252ab](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d4252ab9ed493ba0f44b91dccef0d7903d0a729e))
-   **frontend-http-crud:** add `onSuccessCallBack` on `delete` method ([c7073ad](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c7073adcd26a86ed2fc25b9c7667c89bb64906df))
-   **frontend-misc:** add `operation` endpoint to misc api endpoints ([7974745](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/797474540cafe11d965ffd0c57373d88dbf7c1a1))
-   **frontend-misc:** add `service` request to misc ([4b6317c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4b6317cd7322ea05f95fdc548b18cbc4161c30bb))
-   **frontend-misc:** add mapper for specific end points like `kpi` and add new `MiscItem` type ([3e4f60d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3e4f60d98ac07258e615958b11fd138852010ab4))
-   **frontend-partnership-contract:** add `export` button to `partner contract` ([cc1c82a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cc1c82ab3cf89b3ef4c96b6a18c09ecdb9e648a0))
-   **frontend-partnership-contract:** add `nationalAgenda` linker to `strategic goal` ([d90c42d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d90c42d07ad90fc757cdd4c26d30c75b29a368e2))
-   **frontend-partnership-contract:** add `partnership-partner-evaluation` dialog ([a7998f2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a7998f2aed7e7084a9a3ad8b2db83703dad91369))
-   **frontend-partnership-contract:** add `status` field to details page ([0973719](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/097371935ad0539fefbc7525ae9ec700c4ced8d0))
-   **frontend-partnership-contract:** add `strategic goals activities` to detail page ([9ce5031](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9ce50315f8710cbae8d41d43cca5d0f8c0bd137e))
-   **frontend-partnership-contract:** add flow transaction history to detail page ([8028578](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8028578c03d16ef55f549cb37e558096cb63360e))
-   **frontend-partnership-contract:** add link `strategic goal` with `operation` ([8ffbc2c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8ffbc2cee1a415335a0bea007a523bfaee14b6e8))
-   **frontend-partnership-contract:** add link with `services` in partnership strategic goal ([2017586](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/201758645f4cec5394b660295aa837a96a8ea6f1))
-   **frontend-partnership-contract:** show partnership contract list in sidebar ([43fc66d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/43fc66d5ffe4d8fb3d3070b551fcdb9820ee2854))
-   **frontend-partnership-contract:** use badgeColor in partnership contracts list ([ed72a02](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ed72a02ab7730f068c72a567e55f3c3509deea2b))
-   **frontend-shared:** add new `alert2` shared component ([c3952dd](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c3952dd06d77aa86997ef2c65e6ad915c0058f23))
-   **frontend:** add full screen to departments tree ([6523b63](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6523b63bcf6a9b1e6fcabe5b17200cebb67bd288))
-   **partnership-contract:** add `createOrMergerPartner` method to partnership contract ([53a57d2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/53a57d253dee225c263bc9dba2cf4765121f29b6))
-   **partnership-contract:** add `partner-evaluation-standard` in `system-list` ([b99e732](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b99e732c0eb38845d81d012f8dbbb0e7edeedfeb))
-   **partnership-contract:** add partnership strategic goal initiatives ([4180d0e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4180d0ee8f6d572a283bbac0957bfed3812d0d9a))
-   **partnership-contract:** allow user to edit partnership only if state is `draft` or `rejected` ([adfed75](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/adfed7534b20c43fde12fa063ab6210a3a2eaa16))
-   **partnership-contract:** use flow state in `partnershipContract` ([248450c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/248450cc0b4be183cb844447fbc7875b474a468e))
-   **partnership-strategic-goal:** add `partnership-strategic-goal` module ([cbf8fa9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cbf8fa9ca225043812c75bff1dd47054caa629ca))
-   **strategic-goal:** add `strategic-goal` to misc api ([1138027](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/113802789d289dc9681926735d0ce4be882034de))

### Performance Improvements

-   **frontend-dynamic-page:** avoid memory leaks by stopping listeners on destroy `table-list` ([1a6b23f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1a6b23fe773c0f2b7eb4906011976959c4a1d410))
-   **frontend:** improve `property-value` component performance by using `OnPushStrategy` ([f34cdb9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f34cdb927b7a80310ec57bc8d4789df16cc0c3d7))

## [1.51.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.51.0...v1.51.1) (2023-10-12)

### Bug Fixes

-   **library:** add missing allowed type for word files ([c039c3f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c039c3f5ee108032258ca586d3f355e30f4c16d2))

# [1.51.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.50.0...v1.51.0) (2023-10-10)

### Features

-   **frontend-kpi-result:** add `isSpecial` & `isTrend` columns to kpi report excel ([74fe27a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/74fe27aaa30b01be4e6a5eb43a8d98d085496ebd))

# [1.50.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.49.1...v1.50.0) (2023-10-07)

### Bug Fixes

-   **capability:** filters now load items when select is opened ([e5f71b8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e5f71b802746b77df7e91cce7d1643be4ec958f3))
-   **kpi:** `formatKpiResult` pipe now rounds the seconds to the nearest integer ([42de0ad](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/42de0addc320b7b02b2a1350c31a5ba8418fa905))
-   **kpi:** kpi result reminder runs every six hours now ([81d204e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/81d204e243322b3ab08c74751853085ac63b4e5b))
-   **kpi:** listing responses for a data entry request does not work with filtering by status ([723f535](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/723f5354d19bb5ce73d8acab1fca17a432ce2a3e))
-   **plan:** finalizing a subsubtask does not require a progress, and is set to 100% ([093829a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/093829a52d88e37e946410a2d56e4e77b05fbb59))
-   **report:** measurement cycle was not considered while generating kpi reports ([a47dfe7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a47dfe7f717cab1277bf7539141f0f41937f0e75))
-   **setting:** enhance result reminder configurations ([1ad8190](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1ad81901935fd621472562474327ac4721f95810))
-   **system-event:** add `benchmark`, `tournament`, `pillar`, `standard` ([e099bb0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e099bb031304a5bf390a09c1944907ff60bd5775))

### Features

-   **kpi:** adopt kpi result reminder configuration during the actual process ([fac548c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fac548c288e46817cfd0340e17eff6811e46f5d9))
-   **setting:** add ability to enter preference on how result reminders work ([3e471c0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3e471c00f5527736696af99c7aae6393fcc2c137))

## [1.49.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.49.0...v1.49.1) (2023-09-14)

### Bug Fixes

-   **library:** add identification to email files ([da36e45](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/da36e45afdbb138bccdb8c38b92acabe46c36f60))
-   **library:** limit upload to certain file types ([af982fc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/af982fc559ac8bcb85216ea237545d8579b6318c))

# [1.49.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.48.1...v1.49.0) (2023-09-11)

### Bug Fixes

-   add .gitignore to cspell ignore paths ([3f3510c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3f3510c5ccd11e3fa62f16135ac114e69e00eaec))
-   add missing translations ([49308f3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/49308f33870fea71a26475fae605b9eab87c0a93))
-   add sonar dir to gitignore ([ecc5583](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ecc5583b8a4cd3c475d78062c3ecccb6c962d79e))
-   **benchmark:** add filter by approval status ([38271c4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/38271c4a4cfac7da657ca3b193273af91ee43de2))
-   **benchmark:** add filter by approval status (endpoint only) ([354c018](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/354c01893107f62f63fe30c99a263441cfe73cfe))
-   **benchmark:** dashboard tiles now filter list when clicked ([fe618c7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fe618c7d78f1a2b92f998d8558eaf4f44725cfdb))
-   **frontend:** add missing links for nav bar ([4ebc7d2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4ebc7d2445e0484efab550fe4608850b3157defd))
-   **frontend:** add missing statistics models ([eadd71d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/eadd71d469677a8043cfebdc936df24bc6cdaa82))
-   **frontend:** allow table controller to receive filters from url ([185808e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/185808eb738410193be08e3be7b193ba5a7af1ed))
-   **frontend:** enhance typings for statistics page ([1ee50ca](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1ee50ca679a646bfa3ac00351b87368344e0d119))
-   **frontend:** handle no statistics data ([4e13970](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4e13970d69462fd4bd2d8c6bcb222da815612e19))
-   **operation:** add missing translations ([0c72cb8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0c72cb8dbdc237b2db3689d85a7a7b6acd107cef))
-   **operation:** proper filter by department (endpoint only) ([69d553e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/69d553ed99a80b2bcda760fffbf03c3c29ea70ad))
-   **operation:** return child count for statistics dto ([dca4f53](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/dca4f53873ab6aa2a8ec16b772a8e556415a0e6b))
-   **operation:** search by department + viewing with missing departments ([8ea640c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8ea640c9b7d0759749d81a65131feba1d4f2ce67))
-   **opportunity:** add filter by closing and initiative statuses ([b9cec8f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b9cec8fd8ff6082c5ec62201f0753c52bba1d218))
-   **opportunity:** dashboard tiles now filter list when clicked ([4209d53](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4209d532564399b7880310960bdb8fd336b4921b))
-   **service:** dashboard tiles now filter list when clicked ([0bb820d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0bb820dfac696299643cf816956612e8a562e447))

### Features

-   **benchmark:** add api for statistics ([b28539c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b28539ca37149a159ff6cf51161a54a5fba0a59e))
-   **benchmark:** add statistics page ([b5b47f7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b5b47f7c502ac659aa4e471fea95bfb92c5f5bca))
-   **capability:** add api for statistics ([c59de9f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c59de9f9eee571e4aaf70045a0ed65f48e6549c1))
-   **capability:** add statistics page ([29739cb](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/29739cbd67deb48bbcc95e7fb26ef35959ff3ec6))
-   **operation:** add api for statistics (general & department) ([390dd4b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/390dd4b93dc565144f7a85afddf0a9c5a2b59bc9))
-   **operation:** add dashboard page ([b249e4f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b249e4f4101e712defb4aab9611e1b24ad2a2765))
-   **opportunity:** add api for statistics ([6686d7b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6686d7b6b0d81f40c58cdbce1df7e6074e1259bb))
-   **opportunity:** add closing status and initiative status filters ([dbc3ad5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/dbc3ad5edb303a932e9ff93bd8f0aefc18e95fab))
-   **opportunity:** add statistics page ([e0502b7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e0502b79349f67dd2207f5a35f4d6999bc89360f))
-   **partner:** add api for statistics ([8c62d9f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8c62d9f38ae2d1ee58f87507dd3852eb44305358))
-   **partner:** add statistics page ([b5d4351](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b5d4351fa81e27396598374b63c24c2ed71e6c58))
-   **service:** add api for statistics ([72256cd](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/72256cddda22435e2efbcf385af1f955df4da65b))
-   **service:** add statistics page ([35e70f3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/35e70f32357d2223a3b40539bb940d542e588bb2))
-   **system-event:** add plan, partner, opportunity, and service resources ([843a651](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/843a6516f0033acf8b884e29d51d493922aa342d))

## [1.48.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.48.0...v1.48.1) (2023-09-10)

### Bug Fixes

-   **docker:** use boring91/tika image ([83a704d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/83a704d0c6bf0b7c33e8333977eb6b139329a561))
-   **tika:** build tika image with custom configuration ([7122e15](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7122e154e00827ebf94a9e704303f475fc7f6aa4))
-   **tika:** move to faster `/detect/stream` api instead of `/meta` ([9848fb6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9848fb62ef5873c5a3dd78f7dec40183aaea78c6))

# [1.48.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.47.0...v1.48.0) (2023-09-08)

### Bug Fixes

-   **kpi-result:** set kpi result attachment size to 50MB ([6522156](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/65221561eff52fc3209c76445edac07c2dcddd30))
-   **kpi:** add translations for `minute` and `hour` units ([ae9e06d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ae9e06df49176e3994c315b6cc46c0878fa02337))
-   **kpi:** update format kpi result pipe to accommodate `minute` and `hour` units ([971e1ca](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/971e1ca9d7689dc02f9f19c9067d625be61d44c7))

### Features

-   **kpi:** add `minute` and `hour` measurement units ([55342c8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/55342c8c6ac0b765ddfd429bee94bd775d0046f4))

# [1.47.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.46.3...v1.47.0) (2023-09-06)

### Bug Fixes

-   **backend-fluent-apis:** add missing global query filter for `improvementOpportunityInputCategory` ([feebc09](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/feebc096b4df8f4a396e4fd90bb487652e673455))
-   **backend-migrations:** use `GET UTC DATE` db method instead of `DateTime.UtcNow` to fix issue ([2ed80ff](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2ed80ffc65eff85141edcfbc7b14d76f1a3ac3ec))
-   **backend-migrations:** use static status instead of using status variable inside migration ([6422d59](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6422d5981aa7db907827fc24212a9a8faf02ad8f))
-   **backend-partnership-contract:** set `partnershipType` column to nullable ([3aa49bc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3aa49bcec42c31f2c7d7e80ebd6d7d6f27efe620))
-   **backend-partnership-partner-standard:** add regular expression to `sectorType` in create command ([0d6a681](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0d6a681ba83f9f2b3df5d9be999f8087d067d386))
-   **backend-permission:** remove duplicated permission ([efb7e58](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/efb7e588d1826266e899199682b62a6daf081c0e))
-   **backend-user:** on update user check if he has `MandatoryPasswordChange` before remove all claims ([40b98e1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/40b98e1c72373be984ea9e25df48c48ec0e0879a))
-   **backend-user:** update `employeeNumber` on create or update user ([1e462f9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/****************************************))
-   **backend-user:** update missing user status and type in database and update admin seeder ([e6efef5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e6efef5c9575794942e2fb70fff7b2595105ed5d))
-   **frontend-app:** import missing modules for app component ([dbb9ebc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/dbb9ebc73c24bde1820b805cf7f8333624fe0d51))
-   **frontend-http-crud:** subscribe to `delete` method ([619e265](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/619e2651bf317ceae6114671ba69a25c5d852561))
-   **frontend-innovator:** remove unused service from `innovator` module ([6f71ab1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6f71ab1bd48944397fe4ca88509c7df870e9e858))
-   **frontend-kpi-result:** hide target columns if the kpi is trend ([f51d219](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f51d219e2c56b7bffac7c3a412c9ae1936650558))
-   **frontend-kpi-update-result:** show `achieved` column only if kpi is not trend ([3386b42](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3386b420b11b1d05997d79185b409792ad5d43e1))
-   **frontend-partner:** adjust control buttons td width ([7aad457](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7aad457f0190f91add5bb70e13407f298c15aa19))
-   **frontend-routes:** remove duplicated `benchmark` route ([6fb83e0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6fb83e008aa72cc040e503d290cf7e3f7edbf72c))
-   **frontend-statistical-report:** remove unused component ([e50b60c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e50b60c645bf2775178eaa4f52fbac79fdc6c30d))
-   **frontend-system-list:** hide preview button in system list ([dcfc409](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/dcfc4093596c4dfc6a1f164c0f9a4231b8107867))
-   **frontend-user:** show user list actions if user is not deleted ([e8da579](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e8da5797a2f36451fe144b054d8a2252e8adff02))
-   **frontend:** remove unused package `ngx-perfect-scrollbar` ([c14bef0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c14bef04adb17b814470cd0c49b0a7050a3b1ac1))
-   **frontend:** remove unused properties ([7782603](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/77826032044c67cb825f63290529208ae69394f0))
-   set kestrel max body size to ~60MB ([0cf2bc6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0cf2bc6b826cac732471a92cf2b7811cc388284f))
-   **system-event:** allow only users have the `fullAccess` permission ([2b39942](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2b399426bf542e30a9aa00c16162f0619228eea6))

### Features

-   **backend-extensions:** add `ModelBuilderExtension` to set global query filter for derived types ([c743056](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c74305671c0152830571226a01c69130272400bf))
-   **backend-i18n:** add `partnership-contract` i18n folder ([2445555](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2445555a51bbe3c5a8c61ed81b54c9244aeb0ff7))
-   **backend-partnership-activity:** add `partnership activity` module ([df785b2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/df785b24f2215ed4d791784fcb2a4f0c8305412b))
-   **backend-partnership-contract:** add `misc` endpoint for `partnership-contract` ([cffd651](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cffd65122ca33716285ebdb1946629c3cf54364d))
-   **backend-partnership-contract:** add `partnership-contract` module ([b6834c8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b6834c85fe982e25ffb8244c0c57dee212ca5ce8))
-   **backend-partnership-contract:** add `PartnershipContract` model and create migrations ([9d9f70f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9d9f70f35b4544aae765dd829082005f24f45133))
-   **backend-partnership:** add `PartnershipActivity` model and create migrations ([3154cc1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3154cc1219cbfab23b08e9a3f06e2efbffa45949))
-   **backend-partnership:** add `PartnershipFramework` model and create migrations ([a18a4aa](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a18a4aa45dcaedb5eceddaeb910c603443983744))
-   **backend-partnership:** add `PartnershipGoal` model and create migrations ([f37e37d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f37e37dbc7f84f06fa086172d2ac6000923f76aa))
-   **backend-partnership:** add `PartnershipGoalActivity` model and create migrations ([36ce3a6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/36ce3a636709fb854f0a471be8ea4f0bef3ef21c))
-   **backend-partnership:** add `PartnershipPartnerStandard` model and create migrations ([cc4ed04](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cc4ed0435ce61fa665d715556cdc84c2499441e2))
-   **backend-partnership:** add `PartnershipScope` model and create migrations ([b0d7037](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b0d70373d3aa58c68ff59dbf6af2e435b569a83b))
-   **backend-partnership:** add `PartnershipType` model and create migrations ([a3acd21](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a3acd21d990e9d13d586969689d7ad361fbe98c3))
-   **backend-partnership:** add new class library `Injaz.ModulePartnerShip` ([75bb0d1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/75bb0d12816af64d1a1e1da58555ba24df5c0fb2))
-   **backend-partnership:** add partnership permissions ([242d843](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/242d843836d150e4b6e6fdf47bc8ddee1b0bf411))
-   **backend-partnership:** link `PartnershipContract` to `Department` ([c2eb7bd](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c2eb7bd716b6da2ada4bf350560cd30881847402))
-   **backend-partnership:** link `PartnershipContract` to `Partner` ([19c670d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/19c670d37684ab12322d50cde2ef4e5e8d6ba2fe))
-   **frontend-dynamic-list:** add `dynamic-list` module to automatic manage dynamic lists in frontend ([5e258af](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5e258af530305569a77ed2a3a0e8aeac20d2c883))
-   **frontend-dynamic-page:** add `action-button` to automatic generate button ([1a17108](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1a1710819a3b64f8d8d75c4523c4645d67990292))
-   **frontend-dynamic-page:** add `dynamic-page` module ([b980fb6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b980fb6225b3648a76b40b4d2c212080dd016d53))
-   **frontend-dynamic-page:** add `new-form` component to `dynamic-page` ([bd1e06f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/bd1e06f7e59721ec5521aae091ea2c1badf77223))
-   **frontend-http-crud:** add `http-crud` class for calling CRUD methods ([ecc5ece](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ecc5ece5b6010af3babba1a6a0e2bae095a40ae5))
-   **frontend-injaz:** add `detail-page`, `list-page` & `new-page` components to dynamically show it ([567e5d8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/567e5d84c0a2914d3ed39cffe0c27bfa77f9e89a))
-   **frontend-misc:** add `MiscApiEndPoint` type to automatic get list, item and set misc item ([89805f9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/89805f986c04bd303d81f1e79842a8659caeadd5))
-   **frontend-partnership-activity:** add `partnership activity` section in detail page ([c97c336](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c97c3366cabfedc321f4c1ad3df3184ed8b69953))
-   **frontend-partnership-contract:** add `partnership-contract` module ([845fec0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/845fec0150c6bb04341c00b41fc3fb2e3df86e4b))
-   **frontend-shared:** add new `nested-property` pipe to get nested property from object ([7eb0cc5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7eb0cc547dd3a0fcb25aff35848d0de527da3e3f))
-   **frontend-shared:** add shared `delete` and `edit` button components ([b4d741c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b4d741c6924e865ddec81faa1a7aeb74e20a8882))
-   **frontend-shared:** add shared pipe to concatenate two arrays ([d0d90c6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d0d90c63f619b614901350d233397869fa92fa11))
-   **partner:** remove all fields and all relations from partner except name ([75ebc38](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/75ebc3893f59801001c17f74a9013c3f93287e56))
-   **partnership-activity-communication-tool:** add it in frontend and backend ([a1d0fc1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a1d0fc1dccf0ba1533551fcd278d023324773352))
-   **partnership-framework:** add `partnership-framework` to system list and its controller ([2c04aff](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2c04affd2072975a1d24d367700f7ada92cd665d))
-   **partnership-partner-standard:** add `partnership-partner-standard` to system list and controller ([361f83c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/361f83c7eda103a59d716a17322fb4a05120a78b))
-   **partnership-permission:** add `partnership` permission in frontend and translation ([1e22f7a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1e22f7a2397a6f5364080e56aed72eae7bc5407b))
-   **partnership-scope:** add `partnership-scope` to system list and its controller ([a23e2a7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a23e2a75b9543d76e3a2236e3453fff6c189414f))
-   **partnership-type:** add `partnership-type` to system list and its controller ([5854c0e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5854c0ee1a18fb102252438f17720bdd85a966dd))

## [1.46.3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.46.2...v1.46.3) (2023-08-25)

### Bug Fixes

-   **kpi-result:** add migration to remove dangling entry responses ([c15fa94](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c15fa94bcb89db0693b30e7c2d95e39406aa9cd5))
-   **kpi-result:** remove associated entry responses when result is deleted ([f850bb8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f850bb86adf22f45709c1194ed57d10d7e094aa7))

## [1.46.2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.46.1...v1.46.2) (2023-08-21)

### Bug Fixes

-   **kpi-result:** exclude results with non-active kpis from data entry responses ([df76cac](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/df76cac2397e3fd1cbad9e8bccbcc6b63edcd936))

## [1.46.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.46.0...v1.46.1) (2023-08-16)

### Bug Fixes

-   **kpi-result:** data entry response submission clears out next approval field ([2f42161](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2f4216146c9cde10f4d59423ca9fe569f0c0db84))

# [1.46.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.45.0...v1.46.0) (2023-08-15)

### Bug Fixes

-   **backend:** split data entry responses load into item loading and results loading ([c673242](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c6732426f5ef0042c591f6a16e221570e673462c))
-   **frontend-data-entry-response:** update item fields after update it from backend ([409c693](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/409c693ed35256e2f2e2a366a6aa7dde7b0297bb))
-   **frontend-kpi-period-breakdown:** show `N/A` if there is no actual value ([d47b052](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d47b05242b3d5ceac4198fff7a2250a68b155a61))
-   **frontend-kpi-result:** use target from form instead of kpiResult variable ([27223de](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/27223deecce4e294a9052478e60eb9f72c125ff8))
-   **frontend-shared:** remove `flatpickr` instance on destroy directive to prevent duplicate elements ([a3a3db1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a3a3db1ca9d6030659d08daa8df44cf50ed1e86d))
-   **frontend-style:** remove default style from input of type `range` ([36479c4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/36479c4266ea25c896bca50754ed15cff2b931d0))
-   **frontend-update-result:** prevent error if the control is not exist like `measurementMethod` ([637b492](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/637b49294ad616fb97e98bc25c7efeac2f737c62))
-   **frontend:** in `achieved-container` component use `ngOnChanges` instead of `ngAfterViewInit` ([a6e618d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a6e618d0faf6f61a70d08411a47d4502e6b65232))

### Features

-   **backend-kpi-result:** add `improvementProcedureCompetition` to kpi result ([980fb11](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/980fb117eeebe72d588d5980e081cc5b1343a4be))
-   **backend-kpi-result:** add missing properties in kpi result dto ([d500f70](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d500f70f3d2209f55c17ecdfe2bb73a202a95dbc))
-   **backend-kpi-result:** add optional period parameter to filter the attachments by period ([f227f78](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f227f7849fb6892055f97f4a450fcccce3192194))
-   **frontend-kpi-result-attachment:** add `updated` event emitter to kpi result attachment component ([fa3a957](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fa3a9571ec3f0e1b832df807b094309384a7a098))
-   **frontend-kpi-result-period-button:** add new button for kpi result period actions ([8c426f8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8c426f841f9703615a9081e753aace251ecd2c6e))
-   **frontend-kpi-result:** add improvement procedures completion percentage and date ([cefa31e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cefa31e788ad8fcd948f4e98d5583653cfeed1a4))
-   **frontend-kpi-result:** big refactor in kpi result periods field to periods table ([0325776](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0325776e6b57222b0501e37f1f19af42b943da7b))
-   **frontend-kpi-update-result:** add `updated` event emitter to capability list ([9de0e7d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9de0e7d50757494854b03abdf9338ac56718e5db))
-   **frontend-kpi-update-result:** add `updated` event emitter to period breakdown component ([b037fad](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b037fad3e13aefef8211220de1cb7e7b25519378))
-   **frontend-kpi-update-result:** hide periods field if the mode is equal to new ([a9fec96](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a9fec9646ce8791d2593b66a59cfb4b95bf764b5))
-   **frontend-kpi:** add `improvementProcedureCompletionDetails` properties to models ([3c9b688](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3c9b68826633d9a4f7356bc3e41a4444a045e9c3))
-   **frontend-kpi:** add new optional param to filter attachments by period ([9e2a507](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9e2a5073bd2c1dab8f42657fa1e3fdae47badbd6))
-   **frontend-shared:** add new pipe to ensure user has permission ([89f5a8d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/89f5a8df59f6a3184532f446b3dd97ee7d059444))

# [1.45.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.44.5...v1.45.0) (2023-08-01)

### Bug Fixes

-   **backend-kpi:** use `DateTime.UtcNow` instead of `DateTime.Not` ([6f0a273](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6f0a2736d4e7c21425f5ba28838a3f139003e35f))
-   **backend-migrations:** set `kpiResultPeriodBreakdown` to inherit from Model not Modifiable model ([1231342](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1231342ae0cc605cf91c772654da3eb98fb8088a))
-   **backend:** remove kpi result period breakdowns when clean periods ([5df1534](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5df153453094edb7f6e6bc924d4813a24dde489f))
-   **backend:** remove unused params and imports ([4038972](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/40389721719b550f21552d7e63c305e7560b9c97))
-   **frontend-statistical-report:** remove repeated years in years filter ([6517add](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6517addea4626ef232d889546256ead8138e2057))

### Features

-   **backend-kpi:** add new reminder hosted service for kpi result to notify users ([944e502](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/944e502e48df5e82ad46935e1b825f835f672726))
-   **backend-notification:** add notification metadata column and generate migration for it ([0e5e98c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0e5e98ceadbebc62eb8d50ef19bf49e8b1b0025a))
-   **backend-notification:** get notification `TargetMetadata` for specific dto ([1ef9b15](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1ef9b15a60adcd56d0177561667f2a0e6e6806bd))
-   **frontend-kpi:** add new graph for kpi to show each period result and achieve ([f79d0bf](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f79d0bfabe9159d927d5687957d45a160b102c2a))
-   **frontend-kpi:** show kpi results period graph in results popup ([f17a797](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f17a797d5ade6f656198b099416d4beed2426d6a))
-   **frontend-notification:** add new type `kpi_result` in notification types ([d82e51c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d82e51c170f7c8e2c6408e99b4ca403cc297ddc8))

## [1.44.5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.44.4...v1.44.5) (2023-07-13)

### Bug Fixes

-   **notification:** hide global loading bar when fetching new notifications ([710424d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/710424dbe008f6309c1aac9f8edfbcc3fa8384a3))
-   **notification:** retain notification scroll position after closing pop up ([a8bd258](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a8bd25851c8175690994e808248ecbf0d3282f9e))
-   **notification:** retain notifications after closing popup ([7bd8479](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7bd847939584d517beda0e32ce75e8b0028f883e))

## [1.44.4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.44.3...v1.44.4) (2023-07-12)

### Bug Fixes

-   **permission:** update description for plan write permission to include tasks and subtasks deletion ([e4edf42](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e4edf42ae023cadf8385ee54e99a5687d81b0eb0))
-   **plan:** reset approval cycle does not count approvals properly ([1d86b76](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1d86b76182bb6586a0d209c4fb4af8e74f0f7ea1))

## [1.44.3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.44.2...v1.44.3) (2023-07-11)

### Bug Fixes

-   **frontend-notification:** enhance notification list popup and format time ([2ba4f36](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2ba4f36835fc8194d54719141eb730dd143f92b6))

## [1.44.2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.44.1...v1.44.2) (2023-07-11)

### Bug Fixes

-   **backend:** breakdown migration ([90686ee](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/90686eee4b2139cfbc743f92f481bc6d35a9a9b1))

## [1.44.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.44.0...v1.44.1) (2023-07-10)

### Bug Fixes

-   **frontend-kpi:** show period breakdown if the mode is not set ([180263b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/180263b7fff12b7d7a5f8488f6991633475c30a2))
-   **frontend-plan:** fix plan gantt issue if there is no tasks found ([d88b593](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d88b593cdd256133c7583ab974c23694a8b40096))
-   **i18n:** move translation strings of notification popup to app module ([6ffa447](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6ffa4474a045537e04291269e9200b694d9aab41))

# [1.44.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.43.0...v1.44.0) (2023-07-10)

### Bug Fixes

-   **backend-migrations:** remove cascade from `kpiResultBreakdown` columns ([5032539](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/503253941f8be1fd0acbee34fc061b5177fabb6e))

### Features

-   **notification-target:** add notification target type and id and add notification popup ([58ad635](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/58ad6354610a93f09d51c62ed1f526bf2ca8ebb1))

# [1.43.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.42.0...v1.43.0) (2023-07-06)

### Bug Fixes

-   **backend:** update dev database credentials ([9361393](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/93613936bcdb8e614a576742c44e83ca50c038d2))
-   **department:** add missing translation ([ed5b1af](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ed5b1af380264f96ccbe3bc1bb231a3648cee786))
-   **department:** change incorrect `hint` to `note` ([92d8651](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/92d86517f851e261a5e4b584b6e260cce816b916))
-   **frontend-tournament:** fix standard detail link ([9da67a4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9da67a46c605324c29a48c5923ed0cb664ae9ab1))
-   **frontend-tournament:** show progress circle instead of progress number in main task detail page ([2d81cba](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2d81cbadd95e75adee6d2769f5d50c3d9f3d8d95))
-   incorrect translation ([4d04353](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4d04353bfc5144977449d55c097ab71e2b9ed1c1))
-   **kpi-result:** add missing should match origin value field for breakdown ([4c75be3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4c75be3f97c047ffaab58c4f8b0c1ce8896eae58))
-   **kpi-result:** add missing should match original value to command and handlers ([18b7b15](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/18b7b151470283740f928aae3ed766914504aa72))
-   **kpi-result:** add missing should match original value to dtos ([b2cc7a7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b2cc7a7ec430a422df38e986cf2f42429807c46b))
-   **kpi-result:** existing breakdowns are not being updated ([7623dfc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7623dfc0d22857f851aa5778b4bfbf495f6a1736))
-   **kpi-result:** period breakdown subcategories are not shown in expandable section ([3572e30](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3572e3097fde0143248432cf821ff3f221fdcea6))
-   **kpi-result:** result breakdown subcategories are not shown in expandable section ([26d9c5f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/26d9c5fdf7d1bf6df2ce78becc59fe6a8087645a))
-   **kpi-result:** switch places of attachments and result breakdown ([1b6550b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1b6550b757725513b7e3dc1792d96649d4933174))
-   **plan:** add expected progress to subsubtask model (frontend) ([c0b09f0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c0b09f05630636b5e5283376e4bc6f410c4c71d3))
-   **plan:** translation for plan inputs (Arabic only) ([030d231](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/030d231fd2227c68096694659297f1c7bdca0ad1))
-   **plan:** update statistics endpoints + add back authorization to controller ([8829868](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8829868717f64e5607cf143ecf3c549704e856be))
-   set prettier version number ([4dc44bc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4dc44bcf894e50b0958aa14045fd8f0391dbe77a))

### Features

-   **department:** add `isMain` property to department model ([2c7d153](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2c7d1535ab2a566631047cf272ade8122f9d7f0f))
-   **frontend:** add easter service and evader easter ([1371fb3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1371fb3cc7fb40fafdf1c89d2bad110904cc554f))
-   **frontend:** add evader to login button and language switcher ([217cbd5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/217cbd559bd6712119f25f863c6ff36b96c00cb9))
-   **frontend:** change type of department for `DepartmentPlanProgressChild` ([8668f16](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8668f16d7cbb4fb4d6de68b3bb3c3d93d978146e))
-   **kpi-result-subcategory:** add search by main category ([647f9bc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/647f9bc008084e5c097ac5fcb985c22f141fce9c))
-   **kpi-result:** add `ShouldMatchOriginalValue` to kpi result breakdown ([1e2f36b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1e2f36b271284d8f1dbf631e71af2dc0c2a6d0d4))
-   **kpi-result:** show actual and aggregated values in the update period breakdown dialog ([43a72c2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/43a72c288f8f63eec119b1f44babb9d89e6a0e3d))
-   **plan-dashboard:** add plan dashboard page ([567cb65](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/567cb65a0aac2c9c9cfaec704354afc06f60d164))
-   **plan:** add ability to compute expected progress for plan entities ([9b9e47e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9b9e47e69945986d4d7e05434ea5955cd75e57cd))
-   **plan:** add an endpoint which returns a detailed breakdown of department plans ([72ec5ca](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/72ec5ca8d93d9f806cb0f69305fa251996ea4a88))
-   **plan:** add general and progress statistics endpoints ([08540ce](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/08540ce205ad8cef837530d7f868a5f705e444c7))
-   **plan:** add hierarchy dtos ([4a7ff7a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4a7ff7a19d887f86d35e1947971671d969d20d12))
-   **plan:** add initial dashboard component + routing ([ae8bdac](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ae8bdacc1eb95ff54e99be62625c8bf8a7b259f3))
-   **plan:** add plan statistics model + expected progress to models (frontend) ([1f2048f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1f2048fa54e9022b7d423c66d670f04453e48bd5))
-   **plan:** add statistics api methods to service ([c3bf2c1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c3bf2c1eb4c2bbb45dac87db384cc7d9077772c6))

# [1.42.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.41.0...v1.42.0) (2023-07-01)

### Bug Fixes

-   **frontend:** plan assigned entity check for null for assigned department ([e73f310](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e73f310a2b9630c82da35f62ae63e0dd0bfabccb))
-   **frontend:** use department link component in assigned entity component ([3d900b6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3d900b6943f8117d3636ed6cf47df9d5b0e48a05))
-   **plan:** add `PlanKpiLink` model + migration ([1257eb0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1257eb026483c3161eca92b2cb24f34ec2cde741))
-   **plan:** move plan kpis to kpi links (data migration) ([dafaf9b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/dafaf9b9b2d1b6def082555ef17c62e833f3f020))
-   **plan:** remove kpi column ([5121369](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/51213691f1169517bf5ddd96fc7e12543d748f88))
-   **plan:** use kpi links in dtos and controllers ([513d117](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/513d117bbd9ee14a10ad3b74635a7ff9f526add9))
-   **plan:** use kpi links in frontend ([7e8e416](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7e8e416ebc2c2da9a3f5c6e3e8e12aa6b40b5f34))

### Features

-   **frontend:** add kpi link component ([396228c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/396228ca0b42374f1ea763cb873acbcb80beeb57))
-   **frontend:** add operation link component ([82d60c6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/82d60c6502f6f9723bead50d458ccd1a742519e7))

# [1.41.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.40.1...v1.41.0) (2023-06-27)

### Bug Fixes

-   **department:** hide deleted users from department lists ([20f2137](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/20f2137c52771d5f744e71a1c7aa8f707be42049))
-   **plan:** add tasks risks to exported plan report ([6a7f60e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6a7f60e6f86fcc8e06c38588e50d1f846f0764de))
-   **plan:** remove red color from exported report ([b13dd15](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b13dd151dc26569561310d22642e85ada4ce9fbe))
-   **plan:** remove risk from plan ([f69ffec](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f69ffecaca6b90bc385333822faff5b6cfc71034))
-   **plan:** show approvable subsubtasks in pending subsubtasks page ([d69f343](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d69f3437fed4a3eb448493342996cd0758b342ed))

### Features

-   **plan:** add filter by status for plan list ([3c55b7e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3c55b7e1e3d5ac091e04c255d90055f505315432))

## [1.40.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.40.0...v1.40.1) (2023-06-26)

### Bug Fixes

-   **plan:** users with read plan can initially submit (they should not) ([9aa253d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9aa253d8e7cea9f56c23b2b6e9b641082647b2be))

# [1.40.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.39.1...v1.40.0) (2023-06-26)

### Bug Fixes

-   **backend:** uncomment misc result breakdown parameters api ([1c71901](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1c719015f78f932bf290cef0f19300b2b5bbcfef))
-   **kpi-result:** add endpoint for deleting breakdown ([7b3c04c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7b3c04c2c3461c032d2dcc13cd7544c1b8a93c98))
-   **kpi-result:** add kpi result breakdown mapping strategy ([1d4b283](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1d4b283b73c4a73d3c25444cadefa81ccf0695a5))
-   **kpi-result:** add kpi result period breakdown mapping strategy ([960bba5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/960bba5ecc671099b0bf917fb89fafa216184509))
-   **kpi-result:** add missing translations ([a1b4a5a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a1b4a5ae1617de96072f5045aa4f5cd45e1ee39b))
-   **kpi-result:** add permission support for breakdown ([33c2d12](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/33c2d12074deedcecebc5879b2233f3dc8f3a239))
-   **kpi-result:** check aggregated equals to period a/b when updating breakdown values ([341db44](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/341db44cd2ccfd075d3c0092b6142adc1a933733))
-   **kpi-result:** check for breakdown value nullability and aggregation ([dcb5217](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/dcb52170bd026874ef5899b6b9836e29ea1a89cb))
-   **kpi-result:** endpoint parameter rename ([50c8464](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/50c8464a70ea6cfab236517eb6af4b34edd155cb))
-   **kpi-result:** ensure that all breakdown values are filled for mandatory subcategories ([4226e5c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4226e5c041d4a064f861a29f366c46bb0c6c3ced))
-   **kpi-result:** hide breakdown when disabled in the settings ([081ed99](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/081ed99f3f4783fb9ca5847de13aaae9cd96e6e5))
-   **kpi-result:** load subcategories of a breakdown when editing ([fc91ec1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fc91ec1610aa98dcdc89cb0382610cee125d55ea))
-   **kpi-result:** remove period value breakdown feature ([2c8e0b3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2c8e0b356804c56963fdf229d46feb555b34ca4e))
-   **kpi-result:** remove unused unsubscribe subject ([847d17d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/847d17d0af1434be2e947fb201d84a715005f0cc))
-   **kpi-result:** resync period breakdowns on result period updates ([f10ecda](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f10ecdad828597341291cebabcfe96cb487d0814))
-   **kpi-result:** send operation successful message upon breakdown deletion ([90fd372](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/90fd372ed667915becc801ef83dfc296f2687bce))
-   **kpi-result:** update period breakdown on result breakdown update ([94408c9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/94408c9f28362a13bafe4adb0f0e0cec79ce8265))
-   **kpi-result:** updating breakdown resets values ([62f7df7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/62f7df730866a75aca64ccfb421b21346d2ea265))

### Features

-   **kpi-result:** add breakdown commands and queries ([2b7b731](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2b7b7317a5e5233c110f1b41d0293ed6fa507a3c))
-   **kpi-result:** add breakdown dtos ([bf01286](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/bf01286ab3a65b286642a03b1cbaf2da582fc35a))
-   **kpi-result:** add breakdown endpoints ([266fe8f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/266fe8f7d1e4f5a5e1bea2d1dbd3c92c2ab94646))
-   **kpi-result:** add breakdown handlers ([9fade50](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9fade50628579000a5c9c975f76f5580eaf2221b))
-   **kpi-result:** add breakdown models (frontend) ([933da72](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/933da727e82015ec6fef2374933d1017b1dc00d1))
-   **kpi-result:** add breakdown models + migrations ([57587ca](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/57587ca5a4136e1f89b147850ced00707f95eabd))
-   **kpi-result:** add breakdown service ([c388a3f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c388a3f689b9c55452788c1d4cf589fe3782e822))
-   **kpi-result:** add result breakdown component and new breakdown component ([3fbe866](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3fbe86670bb86f63ccc8391630f7a82f9d512ceb))
-   **kpi-result:** add update kpi result period breakdown value ([89343eb](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/89343ebcd74d5c889204c3368299ac0b3ba6654e))

## [1.39.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.39.0...v1.39.1) (2023-06-13)

### Bug Fixes

-   **frontend-tournament:** use pipe to reverse array instead of reversing it directly in html ([f7e7157](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f7e7157bbcf2900fb288744a33d21beb45726c96))
-   **plan:** show linked files in finalize-able subsubtasks page ([157babe](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/157babe67fd953492b87ad16e7cbfd0c73252715))
-   **plan:** subsubtask can be finalized by any user in the hierarchy line ([ddf54f9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ddf54f9070b4d3b98acb5696f2ac20cfd8ebe444))

# [1.39.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.38.1...v1.39.0) (2023-06-09)

### Bug Fixes

-   **backend:** created a service that regenerates hierarchy codes between runs ([1a9b1a0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1a9b1a04063a61643a4de272081825f1816206e1))
-   **backend:** missing translations ([0a391d0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0a391d0edf96ee70d959ac7c2062014f94177fa6))
-   **backend:** move excel generators to the core library ([ebc06ec](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ebc06ec4b7ac90761815d163f70ebd8b5a1c9e60))
-   **department:** clean hierarchy code inconsistencies ([16ecb6d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/16ecb6d0bcb311006d55ae21e373f1b4dc91688d))
-   **department:** prevent setting a child as a parent of the current department ([1961912](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1961912ae419384f3f703141d1b3ab01a6a17496))
-   **frontend:** add user/department link components ([3499e70](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3499e705f34e67323516e1fa3a56a4ab46983af0))
-   **plan:** final approval of subsubtasks require permission only (no department dependence) ([c88a8b7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c88a8b74613410f8a9602721ab3b9480bdd1fa3a))
-   **plan:** users with plan write permission can delete tasks and subtasks ([3045d08](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3045d088dd82b618298bfcaefe875abf190e2079))

### Features

-   **backend:** create an excel generator for hierarchy users ([357704e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/357704e73fe69e797e72c41e2fd30b141095ec96))
-   **department:** add list of hierarchy users to department detail page ([a8c9bee](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a8c9beea1deb795501dc3517c478ad353125895e))
-   **department:** command and query for export and getting hierarchy users ([155de4c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/155de4c51373fc56c0e300f14be532b57f089561))
-   **department:** create actions to get and export hierarchy users ([6a14a36](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6a14a36134161311d432623105f28c77372e5dd1))
-   **department:** create handlers for export and querying hierarchy users ([c2565e4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c2565e49c66c4d77426f3a3e3034b120940ef7bf))

## [1.38.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.38.0...v1.38.1) (2023-06-08)

### Bug Fixes

-   **frontend-sidebar:** automatic scroll view into the active link in the sidebar ([106fd66](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/106fd6641f76fe218345eb76cedb21f9757b8e03))
-   **frontend-sidebar:** set padding and margin for only sidebar items ([d545e87](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d545e872e0ab8104a42cbf11ada064d8975f93b9))

# [1.38.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.37.2...v1.38.0) (2023-06-07)

### Bug Fixes

-   **backend-translation:** upgrade `DotnetOmar.Translation` Package to the latest version ([996240a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/996240a012eb77b638a76ff935be0cd74a28f175))
-   **backend:** change pipe to plus ([1f58b27](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1f58b27210cdf1a16e6d3381a7766c6f6d463424))
-   **kpi-result-category:** add category with type dto ([8c56c38](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8c56c38fd5074cd902b2cee15465f418771fc048))
-   **kpi-result-category:** add misc api service function to get types ([6dd488f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6dd488fd9237c707501c06ce0fd366519bbcbdee))
-   **kpi-result-category:** add misc api to get category types ([367501c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/367501c26aabe47cd8326b501221c049d0a897a4))
-   **kpi-result-category:** add missing translations ([0498218](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0498218d8b5c03f688e3b33375e5ec309f541675))
-   **kpi-result-category:** add modules to settings module + add links to dropdown ([e2b555a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e2b555a413440a53a1f93e0ebc8458a49e9c8b8c))
-   **kpi-result-category:** add type property to category model ([e5b5202](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e5b52020c34d881b60d3232557798c7770854289))
-   **kpi-result-category:** add type property to category model (frontend) ([f2a146b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f2a146b4ac28a89bfea4de861295307f38690649))
-   **kpi-result-category:** add type to list of categories ([a0b02de](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a0b02deda4d39a89fbafd42fbc77c29546b10da1))
-   **kpi-result-category:** add type to new/edit form ([8bad318](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8bad318869dd56775084bec93a7a27b1e649ab79))
-   **kpi-result-category:** adopt type property to dtos, commands, and handlers ([7f8954a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7f8954a80ed5c70498fb71322594655ebc1d4b9f))
-   **kpi-result-category:** check for linked resources before subcategory deletion ([07707a7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/07707a77125f462e727b54f75973f8c740a83809))
-   **kpi-result:** add breakdown model ([b135c7f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b135c7f380ae6829eb963bb1ca980961d0d1c5fe))
-   **kpi-result:** add missing `AsExpandable` to breakdown handlers ([4ae2936](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4ae2936fa06169387fa9561286b906010728d4c4))
-   **kpi-result:** add missing misc api service method for getting breakdown potential parameters ([61faaeb](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/61faaeb9c4e7a12fe0639866a25f4988b084354d))
-   **kpi-result:** add missing parameter to breakdown form and list ([9831334](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9831334fa477f19bb57ea2c1974beda26b361a29))
-   **kpi-result:** add missing translation ([73dfff4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/73dfff44d9e4670e96d35679f26bca393dac3d63))
-   **kpi-result:** add missing type field when creating breakdowns ([3c00e42](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3c00e420c2a4425e436499007886015df87002f5))
-   **kpi-result:** change period id to period object ([a9d5816](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a9d5816d5ccfa5dce31e0f2612b4d694489a5aed))
-   **kpi-result:** hide breakdown if disabled from settings ([7fa6b38](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7fa6b381f497fb359d6a988cb02f80b1b58fd326))
-   **kpi-result:** notify for inconsistent breakdown ([c2bdd80](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c2bdd80be4ff37d361c5c23963e11da69e4027c0))
-   **kpi-result:** remove a value breakdown on period deletions ([eb22871](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/eb22871c988deb88d3757e605a3dc50d8361c705))
-   **kpi-result:** return id of breakdown in dto ([3c3b9bb](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3c3b9bb40eeb2a078385e96aae8466ed194a6c91))
-   **kpi-result:** scope breakdown api endpoints to period ([90fe43c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/90fe43c056a1c854e520750b5c7554c0746e0b8e))
-   **kpi-result:** update kpi result a value breakdown to kpi result value breakdown ([cbccf25](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cbccf251e4c573ff5cd58e5fd73128c2fefb115b))
-   **kpi-result:** use alert component to notify user of periods ([0fefa9d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0fefa9d88e43ddb34d86c0ca76c3b7741de32597))
-   **library:** add missing translations ([986a7e6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/986a7e6d98bbbb2bc8ce6610e77f3a6384b407f7))
-   **library:** add needed metadata to resource dtos ([3176a45](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3176a45f367da2d7ed6dd76afb212aff2987d3c3))
-   **library:** only show links where permissions allow for linked resources ([f305635](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f3056356520bfbb21a9e651cced8db8a13fbfed5))
-   **plan:** always color subtask cells in exported excel file ([ac960f2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ac960f2912a562566d4e2a8f07f86d8633e50375))
-   **plan:** calculation of subtask weights ([9589fac](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9589facca4b42bdb4501a806af6a59be63661bc3))

### Features

-   **frontend:** add alert component ([5f4ac9b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5f4ac9b933b038805caaf52f2e1561c7cbcf5aba))
-   **kpi-result-category:** add controllers ([b4c6af0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b4c6af071db9e3969ce3442e458c7cedba3ace66))
-   **kpi-result-category:** add cqrs commands ([62cf382](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/62cf38269b876d4a4ed08cceaf035365b365e44e))
-   **kpi-result-category:** add cqrs queries ([c621773](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c621773d97e2f47d880946842f41bb4e95238e61))
-   **kpi-result-category:** add dtos ([5e64a69](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5e64a693e28ee2d3f028145288e8da4313ac60ac))
-   **kpi-result-category:** add frontend modules ([c97c197](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c97c197875e57a2537e409ee7cea546521984bda))
-   **kpi-result-category:** add handlers ([01dbc80](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/01dbc8036e446814deb8f8dadd6428dbd421ef49))
-   **kpi-result-category:** add misc api for categories and subcategories ([55686a6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/55686a601339862a0b03a9bb3112af03511a1175))
-   **kpi-result-category:** add models (frontend) ([d027c42](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d027c4255b85ea1f3d97e03c22474878f8093cfa))
-   **kpi-result-category:** add models and migrations ([65927a4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/65927a4dd4f2d46bab97ffb96658bf70a5dba93d))
-   **kpi-result-category:** add module boilerplate ([36179c0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/36179c0b29a1cc45b38a6247442606d81fe85d62))
-   **kpi-result-category:** add permission ([cd24725](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cd24725d9bc520793a25fe3bc256fb6e2eba4c7c))
-   **kpi-result-category:** add permission strings ([70a787c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/70a787c9de6a4f23a578d2f5bccbec308080ba69))
-   **kpi-result:** add + update dtos to accommodate breakdown ([82c785f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/82c785f0dd311c077584ab77caf1a25991a5c2a1))
-   **kpi-result:** add breakdown crud into update result page ([dd06b98](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/dd06b9889918d6e73fce0f2090abde79f039af3c))
-   **kpi-result:** add breakdown model (frontend) ([bbb2678](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/bbb2678e93246ec82badb6b797fed0e3970217ed))
-   **kpi-result:** add commands for crud-ing breakdowns ([7588746](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7588746abb6b63fd01172334e80bb1805932d70f))
-   **kpi-result:** add controller for crud-ing breakdowns ([d4bb0cf](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d4bb0cf2444890c596870646da8ba0a4420a3386))
-   **kpi-result:** add handlers for crud-ing breakdowns ([7e7219f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7e7219f1c985809917637296a965180e794d9927))
-   **kpi-result:** add kpi result period a value aggregation model + migration ([f3a7178](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f3a717880c07a1a2c30d6a798bf3c542205ff4d2))
-   **library:** add ability to get linked resources (backend) ([756ee8e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/756ee8e4f7c8be1fb95bf26df32ec47c46504848))
-   **library:** add usage list dialog ([c0a94c6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c0a94c6b2aab512cb51e570c7e986bbc3d9d9109))
-   **plan:** add `SecondaryAssignedDepartment` to plan subtasks ([b4d5bf3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b4d5bf3992643c399157164551da479073a04405))
-   **plan:** add `SecondaryAssignedDepartment` to subtask dtos ([999515d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/999515dbb32fa680cb12c34696a29151e54f36b5))
-   **plan:** add logic to handle secondary department assignment (backend) ([621a9cb](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/621a9cbb9c7688d59ed74aa8b095bcfcd0950e17))
-   **plan:** add logic to handle secondary department assignment (frontend) ([9f8972b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9f8972bbfec4df91cffd0dc8c7e1f51f63b3f399))
-   **plan:** add secondary department in subtask detail page ([33ab1d4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/33ab1d4e1b37837e7971a26394aaea0c985a8f1c))
-   **setting:** add option to enable kpi result breakdown ([ebe428b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ebe428bbb0d2fd27127049ab41f29c7f9cd87577))
-   **setting:** added plan optional field `secondary_department` ([3c1b89c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3c1b89cdae82ee4aa3d5b187bafdd045c9206a64))

## [1.37.2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.37.1...v1.37.2) (2023-06-06)

### Bug Fixes

-   **plan:** add risk field ([fcf19f1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fcf19f1cf53a87f08f87ee4aca0a94b7a721e23d))
-   **plan:** add task kpis to generated excel file ([2bcdc8f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2bcdc8f165538f214b5f5df6f2036c1c3c1550d7))
-   **plan:** only show entities with subsubtask during export ([c86c4d5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c86c4d55456e7c958e75525034932c49bf86205d))
-   **plan:** remove check for task weight ([038e1b5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/038e1b5af5fcbc174140d9dfd380b06e74fb142a))
-   **plan:** remove kpi linking from plan (frontend) ([d6860f4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d6860f4b917f6b6b46710efd230c391bd418250a))
-   **plan:** remove link between plan and kpi ([af6ea4f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/af6ea4f58861fc9278bae77e3decce20e5f8afa0))

## [1.37.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.37.0...v1.37.1) (2023-06-01)

### Bug Fixes

-   **backend-translation:** prevent duplicated translation keys in db and improve syncing performance ([f1efe9d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f1efe9d4fc55af62958ce137f66c8a78d965ab61))

# [1.37.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.36.0...v1.37.0) (2023-05-31)

### Bug Fixes

-   **backend-translation:** upgrade `DotnetOmar.Translation` Package to the latest version ([e63b952](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e63b95249f6ae2b4e2df922174d8cfe2836cdecf))
-   **backend:** change pipe to plus ([a79bc08](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a79bc0867658ea15f65eb4a873b967b63c0bbefd))
-   **backend:** update translation library ([36a47d3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/36a47d364f494e578e0eeced3d1a4bafe5e83161))
-   **library:** add missing translations ([298a80e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/298a80e510c0e8fecffebeb4c992d557d9a86bd0))
-   **library:** add needed metadata to resource dtos ([1f05b34](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1f05b34134eea7195705c22aa1d4831e73040b59))
-   **library:** only show links where permissions allow for linked resources ([fe45ac0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fe45ac0410981721fe8405e694fee82ea2410a4e))
-   **plan:** always color subtask cells in exported excel file ([983ff0f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/983ff0f66d3d000e5202bea3811fab6ee98d5b7e))

### Features

-   **library:** add ability to get linked resources (backend) ([7a7a9c2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7a7a9c2178c1313e3090bf3ffe5570703a736da8))
-   **library:** add usage list dialog ([1285296](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1285296c79ca8f48017907c2503b2723180af55d))

# [1.36.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.35.4...v1.36.0) (2023-05-29)

### Bug Fixes

-   **backend-i18n:** add missing translations ([de8211d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/de8211d134d5cb7e4e15e0c5217eb90fb474878a))
-   **backend-i18n:** add missing translations ([fe53d1a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fe53d1a6113ce53deb538e6eaaf0a14695bc8bf8))
-   **backend-standard:** add edit dto handlers and use it ([4263d00](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4263d004ed8d3faf657167fbf5dfcbb21f564ddd))
-   **backend-standard:** change endpoints name to prevent multiple endpoints with the same name ([0fba612](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0fba612a97ce5185670cc0764c3453383d7f90de))
-   **backend-standard:** on create new subtask send task id in body ([a1b3d3a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a1b3d3ab26c7b6c938f114feb8efab13e73783a6))
-   **backend-standard:** take list of users instead of single user to assign to the subtask ([6236793](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/62367936fc4bd135b40f6f26a986b383ee72ffd1))
-   **backend-tournament:** on delete pillar or standard or tournament delete its tasks and subtasks ([f43bf54](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f43bf54821df3286ae9fe6699908bb0b87f685af))
-   **backend-tournament:** remove unused controller with its commands, queries and handlers ([9b94e72](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9b94e7202b8164ddcb2c1de4d309c82a3971fd3f))
-   **backend-tournament:** remove unused permission from tournament ([c6a7524](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c6a7524eb7604e0cdafbc6726fed303da84aeda3))
-   **backend:** introduce validation service instead of `IsValid` extension method ([a78fac5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a78fac53a4da0ae770b6bac6049befe4ae31a8ea))
-   **backend:** remove useless conditions ([24efe86](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/24efe86cacce822fddb4760e2dde17257f9f4cae))
-   **backend:** return `GetDto` instead of `ListDto` in `CreateOrUpdate` method ([7401af8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7401af859ba5bd3947dc09867c037dcddeab1a90))
-   **backend:** use `validationService` instead of Object Extension to validate the modal ([d09b45e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d09b45ede9569e30c1bfdc485eed2387800413a7))
-   **frontend-library-file-linker:** refresh custom fields on change inputs ([2f8c4a4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2f8c4a4b3fd28f617b370e2a7a270ced1238d624))
-   **frontend-tournament:** fix subtask actions issue in subtask detail page ([81adfd5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/81adfd575e3058bab6c4b54705c5780b298a9055))
-   **frontend-tournament:** rename path to `my-task` ([bc5fe90](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/bc5fe90b67e9b3ba64cb712fcde870f8b7a14cd0))
-   **frontend-tournament:** show `no items were found` if length of standard teams equal zero ([0899f1f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0899f1f6fdc1524c785009f340e443057a205ecb))
-   **frontend:** add custom css class to prevent modal overlay ([08f0a39](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/08f0a397d77d685870c488ededda2a6cc2688f6c))
-   **i18n:** add missing translations ([2ceab5a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2ceab5a06c7565dbf92795e65ff213a302ff48df))
-   **i18n:** revert translated strings in tasks and subtasks in arabic language ([319eb85](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/319eb85eaf18b4db0d99fad2f741833201f4e777))
-   **i18n:** update translation strings ([1c7c306](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1c7c306a56622cfe350868f2c3da696724a4ebc6))
-   **plan:** calculation of subtask weights ([bca11ea](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/bca11ea013bf79a9fc5cc29f1550bf359ee3f27d))
-   **table-result:** rename table result `extra` to `statistics` ([a3e45ce](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a3e45ce15de4807a1529d1c84c42c10bf90841d7))
-   **tournament-backend:** add new action for subtask `canAddDetails` ([b0dfe90](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b0dfe9026c82e214c5950e9f26a743b80a3bc234))
-   **tournament-backend:** check if user has tournament permission instead of set it static to true ([87abe06](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/87abe0613a2be0f8ca0ada7f89bb5a7cffd6bf00))
-   **tournament-backend:** rename `finalize` endpoint to `final` ([b54b7a1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b54b7a1a704cb951f16812963054659dbd01a990))
-   **tournament-backend:** unset default tournament before deleting it ([a9ece5c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a9ece5c1a45cc96dbfa2a54a8ad8523a926b26fe))
-   **tournament-frontend:** add missing validation ([8cf408a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8cf408ada2dc0219dbcce998546de1b3cca03b03))
-   **tournament-frontend:** change cancel with edit in progress description ([5b95a09](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5b95a090ad1b677646f31a4f64892a2b3fc6daf2))
-   **tournament-frontend:** enhance standard-team-list page UI ([7cda425](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7cda42574444044ae93fee89de7fa11e1eb99f00))
-   **tournament-frontend:** show `new` tournament button only to user have the `tournament` permission ([5efe555](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5efe555564f87ed0a611cb6d4606cbe051ae633d))
-   **tournament:** add expressions to compute tournament entities' progress ([1e5e466](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1e5e4664dd6bf29676721880bb4664f4870010cf))
-   **tournament:** add new actions to subtask enabled actions & check user permission before delete ([2938229](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/293822931a4833551cdf174a8a4c7d771856c782))
-   **tournament:** add progress to standard dtos ([90dc1ef](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/90dc1efc52814c7ef4929b04a2f0d7c67af29d67))
-   **tournament:** add progress to tournament dtos ([ec6425f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ec6425f22a7412ddbdc5462c9c8a74c000b4e1c1))
-   **tournament:** add status and enabled actions properties to subtask dtos ([49a2504](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/49a2504f35574309e21b32cbb46df3780a4216e2))
-   **tournament:** add task and subtask db sets to db context ([c6a3b31](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c6a3b3165f545bb81317c33944d45b49fa01b9d4))
-   **tournament:** add task new property to check if the user can create subtasks ([3d319cb](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3d319cb79412e7cffa1eaacf38257117f20898b5))
-   **tournament:** add tournament read permission and adjusted actions permissions ([67b9099](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/67b909996b8a6fae9e9b30159826e03f010c73de))
-   **tournament:** adjust to new tournament permission ([6cbb18f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6cbb18f49e4510de3e3bd84412db410f7eec547b))
-   **tournament:** allow only `subtask_creator`s to create subtasks ([66add53](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/66add53ac3fdd73e960da8bf52d4683199a78bfd))
-   **tournament:** change roles in standard user link to array ([a7182b8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a7182b83fca6afe5c7753c5243c4d3c85a2a98e5))
-   **tournament:** convert roles from array to string ([bb3432b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/bb3432bc0dc82381c2d4014619b3d2c17e66076a))
-   **tournament:** enhance team member ui and adjust translations ([3b566b9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3b566b974f1b11189001c696fc9c8e266c8ab012))
-   **tournament:** enhance tournament ui and adjust translations ([fdbc3bf](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fdbc3bff6d82f92f83196780bb2e15202683df60))
-   **tournament:** improve subtasks list page and fix some issues ([a4633e9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a4633e9500d3def6559ccedb1325fce8325fbac8))
-   **tournament:** remove standard id when assigning a member to subtask ([d2702ee](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d2702eee7a08acd7badec0aa92b20e73514df4e5))
-   **tournament:** remove standard task and subtask summary ([942dab1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/942dab1741dc2ec42b5a743a4a6716ccd94745cf))
-   **tournament:** remove unused imports ([2926851](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/292685171e7021ca6e66a38ac5257dd437a06a53))
-   **tournament:** update `my-tasks-count` to `my-task-count` ([e5d417e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e5d417ecfa3e51cfbeee6db413fe85b1f6af1533))
-   **tournament:** update edit icon ([887da3b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/887da3b1b308c124e570ef0305878f9fc7792c84))
-   **tournament:** update translation strings & add `add new member` in tournament detail page ([eecc607](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/eecc607a71b50857729ce4f4a4fd8081fc0941f9))

### Features

-   add api for standard subtask approval types ([567df49](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/567df4927d85eac7547cf013427b688e6337a0a8))
-   add task and subtask misc apis + frontend implementation ([5686b98](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5686b98e6845283da65b1812096ccb48a20f5b21))
-   **frontend-mnm-form:** do not close ng select automatic if it's multiple ([e4a22af](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e4a22af911ff91112a1ddea10c94522f107cb243))
-   **frontend-standard-tasks:** show tasks and subtasks list in standard details ([bbac204](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/bbac20463277bdccccc717477862869976b0f206))
-   **frontend-tournament:** refresh my tasks list page if any subtask status has been changed ([0278701](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0278701481bda843892df584a26a8f180fd2c512))
-   **tournament-frontend:** add subtask actions ([3915b59](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3915b590de6b57626b0408cacaae997786379fff))
-   **tournament-frontend:** show file library with subtasks ([e221698](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e221698e219f72561a32749e5a2ab91eaf78f6bd))
-   **tournament-frontend:** show subtask comments with manage it ([a0092dc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a0092dcc64c14cd23f278954c651a2880762395a))
-   **tournament:** add assigned and pending subtask list handlers ([fcacc6c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fcacc6c5f52be9c9dc910e686371239470cf29e0))
-   **tournament:** add commands/queries for tasks and subtasks ([e990639](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e990639c2ce5083b30317ab23fa28814ea734e8a))
-   **tournament:** add controllers + done with backend ([3d079b1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3d079b12c42273d10765ce3dbc57550c9db75a95))
-   **tournament:** add default tournament to list all standards ([f878144](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f87814473dd851e87e9e3f36f0b85cdbc9a38d83))
-   **tournament:** add dtos for tasks and subtasks ([eeaba74](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/eeaba7461382ae5a802762c1e88246134a602528))
-   **tournament:** add file linking to subtask handlers ([338c721](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/338c7219f25b347dea54ac6a8cde8835ca478b39))
-   **tournament:** add filter by statuses in subtask list ([34c64e9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/34c64e96f755ee1cb10b75caaab378846d0907fe))
-   **tournament:** add frontend models ([6c2196b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6c2196bdd81a846a5629c6de6df7edff9023e006))
-   **tournament:** add handler for updating progress description ([9c78cad](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9c78cade16a1632b64144fb01b8f894dc86f7e5d))
-   **tournament:** add handlers for get tasks and subtasks ([96e9bf6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/96e9bf61de507f1d06b163e861ebcca1472eb99b))
-   **tournament:** add handlers for managing subtask comments ([9ca581a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9ca581a60532cc8e501c6e6301115a364e4f16b9))
-   **tournament:** add helper expression to get subtask status ([77353c9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/77353c9bf89158571b164c84e561ddcec38303e3))
-   **tournament:** add main tasks pages `create`, `list`, `item` ([b4bee48](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b4bee4822f9e4c6b692137b8403ee447201b2442))
-   **tournament:** add models for tasks and subtasks ([49dd2ee](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/49dd2ee4794838ec386daf8794d804981ab9641d))
-   **tournament:** add my tasks page ([6574148](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/65741483df524d7366d788d067f163d52cb21ce9))
-   **tournament:** add new standard subtask status `assigned` ([ebe6110](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ebe6110f007d360331f6ec9c9397652e7f8b3946))
-   **tournament:** add notification count for my tasks in sidebar ([79e39c0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/79e39c0ffc1abd384bed7740fb3c258e08e64ec7))
-   **tournament:** add notification count for my tasks in sidebar ([f614eb8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f614eb82a387b5662c64db3317c050c8e732ba2c))
-   **tournament:** add option to show only my tasks and tasks that need action from the current user ([3371951](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/33719517a64bfa4b6ebedc5ecc82e98a0807c9c9))
-   **tournament:** add progress description textarea ([bde8cdc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/bde8cdc0f0bef993230ec644683e0d1146c52657))
-   **tournament:** add standard team item page ([eebae7b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/eebae7b944b5075c77546dca6343fe9d970320d4))
-   **tournament:** add standard teams page ([f40a48f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f40a48fc3f7a319a5518b1f661b0b4f8502bc320))
-   **tournament:** add subtask approval commands ([afca579](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/afca5794495ab0dd265880ca97c6677725953915))
-   **tournament:** add subtask comment commands ([d436b8c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d436b8c2c00fdfe84422592606b9a8a34c507b9a))
-   **tournament:** add subtask comment dtos ([b4d5914](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b4d5914befb859148d336a7864f5afe1b2e32449))
-   **tournament:** add subtask comment model ([2fb6ec3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2fb6ec3b7fe7df49afd2d97c6c1fa0009c63c0b3))
-   **tournament:** add task and subtask details page ([d803a00](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d803a00d015cacdc02e3f4a2aeedb148bc7e2fde))
-   **tournament:** add task and subtask repos ([17002fa](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/17002fa1af323397c29a2fecbde6c64ede2762f5))
-   **tournament:** add team member to tournament standards ([e8d6b37](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e8d6b372d712a416d07dfd3d262007b0a984e527))
-   **tournament:** adjust standard subtask workflow ([2f5ea21](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2f5ea21e8879fddc194ac5d6a04198b28c45bd99))
-   **tournament:** enhance & add more features to standard task and subtask ([5cb5f52](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5cb5f525657a4c7af2c7363123746d658bdf8d0c))
-   **tournament:** implement handlers for members and tasks management ([9552bfd](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9552bfdcfa272d9b5e059f2369284c63f5f02500))

## [1.35.4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.35.3...v1.35.4) (2023-05-18)

### Bug Fixes

-   **kpi:** all capabilities are returned as linked with kpi ([b8b755c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b8b755c2164f5723c6d904425c3ff465a18de11e))

## [1.35.3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.35.2...v1.35.3) (2023-05-18)

### Bug Fixes

-   **kpi:** achieved is not being calculated (always null) ([71da678](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/71da678153b100117083707323b3706ecf3a30f1))
-   **kpi:** main kpi results do not have their a and b values aggregated ([11f8ee5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/11f8ee514213c1d597999d73a95408044869617a))

## [1.35.2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.35.1...v1.35.2) (2023-05-17)

### Bug Fixes

-   **kpi:** change active periods highlight to secondary instead of amber ([75f3661](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/75f3661e78f68de0724852dd56bf5ebfee576825))
-   **kpi:** computing achieved produces wrong results when measurement method is last ([f2a40d7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f2a40d76fac1dc5189f8e9b9ad4651db5c05b857))
-   **kpi:** wrap text of a and b description in periods field ([92363a0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/92363a0386dfcb620bb40f0cd89206856cf706b4))

## [1.35.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.35.0...v1.35.1) (2023-05-16)

### Bug Fixes

-   **back end:** add capability project ([91dcd80](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/91dcd809b3a1f487769e411fe4e8ab26f0043e13))
-   **back end:** add capability repository ([a4ecf32](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a4ecf328c8e11c7c6551a474b831f60a6ca0dfb3))
-   **back end:** add db context configuration ([40f1ea7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/40f1ea73d8e48c1f5382e06a615e510717ef6167))
-   **backend:** add capability controller and handlers ([77411fc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/77411fc4382c6892738a62cfb5598963bb285d19))
-   **backend:** add capability kpi handlers and controllers ([ef962e1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ef962e11668fc7398fda68c5b94a9b9ddef418c0))
-   **backend:** add capability kpi repository ([7b22c34](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7b22c344892b267be018ee3bab72c0d29b71c03d))
-   **backend:** add capability library controller ([be21693](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/be21693ec5c84ecd47ecf68a3f10142db83f34e6))
-   **backend:** add capability library repository ([9b1c1e9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9b1c1e9e15ab93a7535b0a82901231e947a228b6))
-   **backend:** change the name of the file to match the name of the class ([a1c73aa](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a1c73aabd3711bd2a89cbda39fc689e8aa56ae12))
-   **backend:** fix another notes ([1d32b20](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1d32b205b519138e5b1f21dd44d991d789a898ed))
-   **backend:** fix notes ([c69a0bc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c69a0bc0c68643c017b69e66154b2a3a49073adc))
-   **backend:** fix startup ([047f0d8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/047f0d87cc576e322d9af7e4a04db10099db2b74))
-   **backend:** remove capability kpi controller ([5d3ea1e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5d3ea1e2f4a12b516e320e4472861dc6518ab898))
-   **capability:** refactoring + fix ([86fbc5e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/86fbc5ec3f803af741df1d41908020ad090c0c9d))
-   **capability:** remove capability create dto ([959a140](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/959a14018449bef6da0b3a665b333ca52badb0e2))
-   **kpi:** a, and b aggregation consider only previous periods ([c510c7c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c510c7ce3c713597374d0b5a0a677fc9f897b737))
-   **kpi:** consider target of previous periods only ([9616cb6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9616cb63f78470a7ac9b686840af2363705f5564))
-   **kpi:** highlight periods included in calculation (kpi detail page) ([de1c477](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/de1c47725df90621b0ef326ca888f6fd8f08b3ee))
-   **kpi:** highlight periods included in calculation (update result page) ([949a977](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/949a9770e68073e6f160d90c02a3bb1492b2a03f))
-   **kpi:** only consider previous periods when computing overall results ([2cc588b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2cc588b4af42df393044a24ffc71623969c671d0))
-   **kpi:** switching between two years for a result resets ([b19f03d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b19f03df6cad044aacce09b981dc168e19b6b1ec))
-   **kpi:** unify current period functionality (database only) ([470ab29](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/470ab29cdee73e7c3444d0fce04c9706dd08ba60))

# [1.35.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.34.0...v1.35.0) (2023-05-14)

### Bug Fixes

-   **kpi:** center progress ring in data entry request list ([20ba6a9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/20ba6a906786fe43d39b11296fc9949204b384fe))
-   **plan:** saving and updating subtasks error ([f1b6b7e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f1b6b7e78029e5b1cfaa0ff3f3ba4dea51e729a3))
-   **report:** current period not included ([6ec02cb](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6ec02cb109a9194a60116619a176c04da85943ce))

### Features

-   **frontend:** add kpi code badge component ([76b9f7b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/76b9f7b56a88fec3fd342f6768054fa91dbb90a7))
-   **kpi:** add filter by department for kpi result data entry request ([19653a1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/19653a12d7134a72b13cce9763639e1bd61ce249))
-   **kpi:** add kpi type and code to data entry response details ([09a5096](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/09a509649a70363e5257888bd40ff35838670607))
-   **kpi:** add kpi type filter to data entry request list ([ff1180a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ff1180a22ce4556d86b6c783fe51b42122e8a205))
-   **notification:** add mark all as read button ([98d8d2f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/98d8d2fda002b977af8e8c507623ccb835679100))

# [1.34.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.33.0...v1.34.0) (2023-05-10)

### Bug Fixes

-   typo ([3039117](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3039117bdc4e761b508dcdfc427f0d646788ce2e))

### Features

-   **kpi:** show data entry response details in a popup dialog ([b446bbf](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b446bbf94ae8e8788819a8765ae7c0fb4c1dc9f9))

# [1.33.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.32.1...v1.33.0) (2023-05-08)

### Bug Fixes

-   **plan:** create, correct the goals drop list that must be enabled by settings ([56666bc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/56666bc3413b6ca56ae6e13e79d1f08d5a804b5d))

### Features

-   **setting:** add government strategic goal as optional field ([7926de8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7926de80f413565d99a4ff50e6d184bc181b9917))

## [1.32.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.32.0...v1.32.1) (2023-05-07)

### Bug Fixes

-   **kpi:** chart year-to-year difference computation ([c1a610a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c1a610af27998a070401d3b0b9865d629757da6d))

# [1.32.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.31.1...v1.32.0) (2023-05-04)

### Bug Fixes

-   **kpi-result:** ensure that a, b, result analysis, and attachment has been added before submission ([0c1470e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0c1470e76d291101450022cef85ada19dd45e4f2))
-   **kpi-result:** remove reasoning and improvement procedure ([91bd112](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/91bd112972e529ed20be06c93a71cf793af76c80))

### Features

-   **statistical-report:** add department filter to list ([08e2679](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/08e267946aaef3aa92d44899e0e551194a4130ca))
-   **statistical-report:** add improvement procedure to kpi result data entry response periods ([c0390f8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c0390f881af36f60abd3c627712c2aedd2dc8828))

## [1.31.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.31.0...v1.31.1) (2023-04-25)

### Bug Fixes

-   **frontend:** remove unused code ([a0d79fe](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a0d79fe176f41c00fd69b7e5657e47136d76c9bd))
-   **translation:** add static translations to `i18n` folder in frontend ([00f89d4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/00f89d43ec547008c023d91ca8981d3aa9956fab))

# [1.31.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.30.0...v1.31.0) (2023-04-24)

### Bug Fixes

-   **plan:** remove required validation from operation ([9586e2c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9586e2cbd643aef6127fc61470b46676b13e561b))
-   **tournament:** adjust routes and parameter names ([8cabb06](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8cabb06648704a81be65bb50e8aec649302ae8c0))
-   **tournament:** get api returns invalid dto (switches between get and forEdit) ([1385176](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1385176500029b7c8ab02fd69373ca5766178fd9))
-   **tournament:** remove old principle controller ([f483dd6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f483dd637986ea2aa8ceb061945f6f4fbca5d65c))

### Features

-   **plan:** added sort by name, from, and to columns ([833b7f9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/833b7f93151cd8aee3f958fc1d83a211037823fe))
-   **plan:** order subtasks according to start time ([0ec0e93](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0ec0e93d45ce7dc5f3f400c2e0cca1d38b3b0de5))
-   **plan:** order tasks according to start time ([09242ec](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/09242ecb19a0ce3ea1f6d43267040947a16486b4))

# [1.30.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.29.2...v1.30.0) (2023-04-15)

### Bug Fixes

-   **backend:** updated plan subsubtask approval types in misc api ([2e24771](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2e247713748c8e52c204d40e64326d0feeb48cb2))
-   **email:** final plan subtask handler implements wrong request model ([15c66d3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/15c66d389e4bb52294227c2258aa2c1450fda883))
-   **email:** wrong view model was passed to plan subtask rejection email ([6fb45c1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6fb45c19ab16e938fb0aa85fed5771bb1311f956))
-   **frontend:** add finalizable subsubtasks link to nav sidebar ([60ae55e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/60ae55e78d8d7d7f83d264781c62edb324accd06))
-   **frontend:** added missing permission ([aeed37b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/aeed37b11e38461868149d83e4cac8909154246e))
-   missing translations ([9b324e8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9b324e8d4fe3f5eb0537c39c5256c0d34e34ac49))
-   missing translations ([7a0195b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7a0195bcb443db8ae68aac092c40d2fa6d69f5b2))
-   missing translations ([befb0e5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/befb0e502b4ac51df54262abe1a7fac0f7e14e70))
-   **plan:** add `submit` type for subsubtask approvals ([97c9dfd](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/97c9dfdf32f6299bf4f63c1eb326eb14e22d4774))
-   **plan:** added progress to subtask dto ([724a276](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/724a276852e6c5eb2f4438fb192f7189f16d75f5))
-   **plan:** change icon for rejection to `fa-ban` ([411e2e1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/411e2e1058e57ee8125e8a92f73d1c6af353daae))
-   **plan:** finalize subsubtask api endpoint url typo ([4b02547](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4b025479927c724b806f752dbad82e45bcc476ef))
-   **plan:** finalize subsubtask endpoint rejects instead of finalizing ([3d0602d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3d0602d9a8eb243aa48f652fb424bf7439aa5367))
-   **plan:** reject button not showing up in the subtask detail page ([c378b12](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c378b127af560162c1028a2a3f2b6692298dee00))
-   **plan:** updated subsubtask types translation and api ([b220646](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b2206461dacfe232d5af31873974d3c444e32adc))
-   **plan:** updated subtask page to accommodate new approval system ([a08921a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a08921aced6c3a98d128d5ade599fbe5f364f898))
-   **plan:** wrong subtask id passed to email notifications ([2ecfb13](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2ecfb13495768873f42ec2b96270a1fb8c2d31ec))
-   translation typo ([cfdddf1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cfdddf17a3c8a2a3831bfb16d1875133d5b3e52a))
-   translation typos ([8304d93](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8304d9309c47aba896e6ce081b0b9274f1ed8775))

### Features

-   **backend:** added a misc api for plan subtasks ([896167c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/896167c89bfff161ba3d7e9106e346356de5d987))
-   **plan:** add ability to finalize/reject subsubtasks ([12725af](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/12725af57cc69d63b1ac4aee250fdd3da6d18cd7))
-   **plan:** add api for listing finalizable subsubtasks ([2cbb0c4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2cbb0c404e3d9763f6f7a1c674f1e95c06649529))
-   **plan:** add app notifications on subsubtask approval ([c8b1fe2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c8b1fe28cc8fd8147ef85312d3ad74b758b09932))
-   **plan:** add app notifications on subsubtask rejections ([f95b86d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f95b86d4ae9e8463502a7433e510b2263ba68d16))
-   **plan:** add app notifications on subsubtask submission ([70760d6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/70760d6bba9d982b83f7897b96b9f62c28c29db2))
-   **plan:** add last approval for finalizable subsubtasks list ([a001e5e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a001e5ea2eeb9399edbdf1fc61b8c165539cad1d))
-   **plan:** added finalizable subsubtasks list page ([5c3fcf1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5c3fcf16942c2ad8eca7f390216f4cdbc611c510))
-   **plan:** added new models for subsubtask approvals ([091714e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/091714e8303ca796359216b8d8cf98a213ae3074))
-   **plan:** added permission for final plan subsubtask approval ([42e48bf](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/42e48bf50b23dd8ac97ed6e7e45267d79243fbcf))
-   **plan:** added plan subsubtask approval dto ([ca9c7a7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ca9c7a799c23334c9dfe8bb700dc558da16008a8))
-   **plan:** added plan subsubtask approval model ([88299a6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/88299a66f30448007838c95ed541f4190e466294))
-   **plan:** added progress to subtask details page ([db67678](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/db6767841b28e8159e0e14f9d12c83fb79f96ee6))
-   **plan:** manual progress update ([511de8b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/511de8b50fa732df84803a7f69cdbc3b59d22dea))
-   **plan:** send email to next approvers when subsubtask is submitted ([d8f1f86](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d8f1f86060896b649b12a469e0518770a45530cb))

## [1.29.2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.29.1...v1.29.2) (2023-04-14)

### Bug Fixes

-   **backend:** added translation apis to the allowed path in mandatory password checker middleware ([c74d138](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c74d138aebac18a3a6994c857ef3534154e1a7f1))
-   **login:** login styling ([2a42add](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2a42add432560decb5a72b15ec4d400891dca7dc))

## [1.29.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.29.0...v1.29.1) (2023-04-13)

### Bug Fixes

-   **login:** changed login page box style ([ae0350a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ae0350a22b5259dcfd87884136ed934266f20bfd))

# [1.29.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.28.3...v1.29.0) (2023-04-12)

### Bug Fixes

-   **plan:** excel generation now converts to client's timezone ([9c63668](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9c63668dbbcdb44447a80290af608382bc087a7e))
-   **plan:** removed free text assigned for all plan entities ([6b88496](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6b8849673a03c7d085675d5733348aabd9f9c4b9))
-   **plan:** updating plans won't work if subtasks had no subsubtasks ([75f9308](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/75f9308c504a005c134b6dbd9ccf4e2bc5808cad))

### Features

-   **backend:** add helper functions for timezone and date conversion ([9355342](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/935534202b4fa7aae7800a286b6bac41fbd0a4b5))
-   **frontend:** added timezone interceptor ([bcadd01](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/bcadd01b59361588798718dec2d13e7a4b6874b4))

## [1.28.3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.28.2...v1.28.3) (2023-04-09)

### Bug Fixes

-   **translation:** move all lazy loaded translations from components to the app module ([3a28a42](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3a28a42cc7e3ae4cba595990ac14f19e9283acbd))

## [1.28.2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.28.1...v1.28.2) (2023-04-09)

### Bug Fixes

-   **frontend-translation:** check in dropdown settings for isHidden links ([2c15cec](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2c15cec3d58fe87b4325916265351a2154f0406f))

## [1.28.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.28.0...v1.28.1) (2023-04-09)

### Bug Fixes

-   **frontend:** add missing packages ([a4cf7dc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a4cf7dc30680e97a04926f6080e23f7f50ab8f51))

# [1.28.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.27.4...v1.28.0) (2023-04-09)

### Bug Fixes

-   **frontend-i18n:** add missing translations and remove unused translations ([86cea02](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/86cea0215345daa8cbc4b464a7e380c7eeecc10b))
-   **frontend-i18n:** remove `i18n` folders from frontend ([649772e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/649772e993c52bb2188ee357e8100f91f21725c8))
-   **frontend-i18n:** remove unused translations ([0a204a0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0a204a0477f653793156695f3af48069d7ebe266))
-   **frontend-setting:** remove unused code ([e34e3fc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e34e3fcbd01f208ac0c94fd8cdbf16b47a49ee12))
-   **frontend-success-factor:** fix on delete issue ([414904a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/414904ad7b3b18a27531550d595ff5ef5b6597d8))
-   **frontend-translation-strings:** update notification message ([a682e1b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a682e1bdf2f40e63c4f54904648dbe91997bd86f))
-   **frontend-translation:** move translations from lazy components to parent module ([e3c37ef](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e3c37ef850bd8d23917d7dadef944eda7c23b529))
-   **frontend-user-request:** remove commented method ([7015fff](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7015fff6bc532c79649fa26ed86aa0c117eb3567))
-   **frontend:** remove unused code ([e861ba1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e861ba1b80407f3edaba09f9c9943db17207530a))
-   **frontend:** remove unused code ([b63c07f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b63c07f6d3167f201f10f7ae5306a02bcb38836f))
-   **frontend:** rename language routing module to the correct one ([cfc4e94](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cfc4e9462b60cc94b4f557e44e6dc6f964047269))
-   **typo:** replace `improvement-opportunity-inputs-source` to `improvement-opportunity-input-source` ([91b6072](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/91b607271bdd11da1be2d786d22f24b28ed7a30f))

### Features

-   **backend-translation:** initialize translations library and i18n folder ([949a353](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/949a353c700ca0e694158392f2383258cf093573))
-   **frontend-languages:** add `languages` module and show it only in dev mode for now ([53a0fcd](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/53a0fcd8e704aaad4fc3229e5e66d70337d8bd1c))
-   **frontend-shared:** add 2 directives for highlight the searched text and for dismiss the dropdown ([6581ce6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6581ce6173b300c926d6ad9b912ce951352a13f9))
-   **frontend-translation-strings:** add `translation-strings` module ([8022559](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8022559de3dbf85bb63b24b05cd6440de91a0e3c))
-   **frontend-translation:** add `@ng-omar/translation` package and setup root configurations ([605d5aa](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/605d5aa2d0f9dd62ad556b4e988d2f1485af8d25))
-   **frontend-translation:** replace loading translation from old way to the new way in all modules ([a8861ff](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a8861ffc8f71ee626e00ec5c88094fcfa9224cd4))
-   **frontend-translation:** use resolver to load translations before initializing the module ([479528a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/479528a78d2aef1eeb4d9490fa49cfc97dcd4e1c))

## [1.27.4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.27.3...v1.27.4) (2023-04-07)

### Bug Fixes

-   **report:** department reporting entry ratio for current year displays wrong values ([348b72b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/348b72b392b5d674142be5d31abb16bfcec65097))

## [1.27.3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.27.2...v1.27.3) (2023-04-05)

### Bug Fixes

-   **translation:** fix Arabic translation ([783e3f6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/783e3f6831435a2dd358a3225176dc5b981a666a))

## [1.27.2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.27.1...v1.27.2) (2023-04-04)

### Bug Fixes

-   **opportunity:** get all list of tournaments, standards and principles ([4b36c3b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4b36c3bd276befe3dd6738faae621f852a7d4cd0))

## [1.27.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.27.0...v1.27.1) (2023-04-04)

### Bug Fixes

-   **plan:** exported plan does not list correct subtask weights ([6c00361](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6c00361ef7eb2f64dbfc3cb7d1bea2428a3e0739))
-   **plan:** show all standards in new/edit page ([f358521](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f358521b0fde626bb35696427f559924ec34c77c))

# [1.27.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.26.0...v1.27.0) (2023-03-29)

### Bug Fixes

-   **backend:** added setting route as an allowed path for the mandatory password change check ([09c4a2b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/09c4a2b735458cadd55bac183b9680374cdbda40))
-   **operation:** removed unneeded broadcast of claims change ([499788c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/499788cb4ba511f116cfe2e134b433a9064b6e74))
-   **plan-category:** exception during creation if no description was passed ([c6dc98e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c6dc98efca5dac61fbe28d411ec206009ca2af6d))
-   **profile:** prevent 365 and ldap users from changing password ([3b482bd](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3b482bdcf8298cdeca48b5b8047fd85611e625ec))
-   **user:** set appropriate status on creation and deletion ([eb70b29](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/eb70b29f476a71ae8afcb31b0b35fd4b74b2ff29))

### Features

-   **backend:** allowed yopmail.com for testing ([54eed23](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/54eed23772d3a7993b512d81770f970faf57007f))
-   **backend:** suspend user api calls if mandatory password change was set ([fbcc5d9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fbcc5d9a628a2ff79ce1ac9f26d7b02c84c61896))
-   **email:** added command and handler for sending emails to new users ([7637727](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/763772755b437485c3ba9aa1d8c46fa894a69f99))
-   **frontend:** added mandatory password change guard ([b8453c9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b8453c990009e6ddbfa3b3595f7842106af4d949))
-   **profile:** added a message to notify user for mandatory password change ([63650f8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/63650f85438ca3541675da0c651ddf60315d2f50))
-   **user:** mark user for mandatory password change when updated by admin ([8d217f1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8d217f14f5a672158f45dc8d5fd1a370c27c90fe))
-   **user:** send emails when new users are created ([d3c10ea](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d3c10ea8578e246d741d9139a03dac10d9be3ed1))

# [1.26.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.25.0...v1.26.0) (2023-03-28)

### Features

-   **npm:** change engines node version to 16.20.0 ([f42893f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f42893f2162916e77ac73a686e1848fa10be6e12))

# [1.25.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.24.0...v1.25.0) (2023-03-28)

### Bug Fixes

-   **frontend-plan:** change typo in plan tasks and sub-tasks ([b33f33c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b33f33c0bc28cf75d5b966bade0b0a7cb3e53b68))

### Features

-   **frontend-kpi:** add copy to clipboard button to copy kpi code to clipboard ([c25da27](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c25da27a2e7bc5ba671f503d7ed97d77b1d730b9))

# [1.24.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.23.2...v1.24.0) (2023-03-27)

### Features

-   **frontend:** added translation for user status items to translate item pipe ([610333d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/610333dd5f90dfb74479be0b315bd2967d4f7cb9))
-   **profile:** added profile page ([9da75d4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9da75d4756e4feb500d0cbe3c951116a945de2d8))
-   **profile:** linked profile image to profile page ([48d23b3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/48d23b30a134f86e80adcb1df7e63100cc1475bb))
-   **user:** added apis to get current user profile and change user password ([5891b4c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5891b4c649fd5f69514761c8bd058d9c1d6ff39e))
-   **user:** added command and handler to change password ([0641cf3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0641cf3009fa7ca05c3408f7d04841145905d24e))
-   **user:** added filter by status support ([acbf1bd](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/acbf1bde907b9014134c46ec469267149f40a635))
-   **user:** added misc api to return user statuses ([81df3c5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/81df3c5e9beacfed53955318f1532f2f97d8fb05))
-   **user:** added query and handler to return user for profile ([76f8466](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/76f8466b535dec8a21c5cd5f31f4c0921bed0d92))
-   **user:** added status field ([ef110bd](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ef110bd56c72b611634faca024a798cfe8fcca2f))
-   **user:** added status to dtos ([ddc08f3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ddc08f3dde743b9eb992870cef38dddaa717a098))
-   **user:** added ui for status filtering (frontend) ([0753c9f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0753c9f6ad8aaa309c9c9bd4c79bf9dd3e6364ae))

## [1.23.2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.23.1...v1.23.2) (2023-03-22)

### Bug Fixes

-   **department-report:** only consider finished periods ([278ee86](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/278ee86e268a6456afc304f58e060555c61036e1))

## [1.23.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.23.0...v1.23.1) (2023-03-21)

### Bug Fixes

-   **frontend-kpi-shared:** move strings from kpi-list to kpi-list-full ([e1f758b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e1f758b6067cec15a0c6574a1b77d88a3ecb0244))
-   **frontend-statistical-report:** set result values to fixed of 2 decimals and fix years issue ([f9e78c7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f9e78c783a1c2d963cd48465a6c7989f923deb40))
-   **statistical-report:** enhance UI and fix table issues with scrolling ([24c3609](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/24c3609cc13752a13cada9ffdee93d67cf3c49e0))
-   **statistical-report:** remove result filter by parent id to prevent one category bug ([ad99c56](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ad99c569af0ae2fc8244746f6cd9397edd5396e1))

# [1.23.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.22.0...v1.23.0) (2023-03-20)

### Bug Fixes

-   typo ([876d875](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/876d875f36f12f9ef9cf5be4c22b99b87d05f5de))

### Features

-   **kpi:** added filter kpi responses buy current assignee ([d308c96](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d308c9611849866d88f31febeb2e10bb69d9a68b))

# [1.22.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.21.5...v1.22.0) (2023-03-20)

### Bug Fixes

-   **backend-migration:** fix db function name ([3802693](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3802693732d07603fe6d36a71c3d343d59adc56c))
-   **backend-statistical-report:** make users how have permission data entry to view only related reports ([98e4899](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/98e4899a43836ae714b8417893391699ee9cb132))
-   **backend-statistical-report-test:** inject mediator to constructor ([35086dd](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/35086dd203f18ff379af5e28d24b76497d7a9c97))
-   **backend:** fix lock and unlock actions ([2b267c5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2b267c5833c8aab0d5e2e7f114f461f2f44f027d))
-   **backend:** model validation runs all validation attributes ([34bc5c0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/34bc5c0dee749d2025ed1050642fbc22b2816da6))
-   **backend:** return only active kpis from misc api ([4554475](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4554475d79725ff3be62b65cae293542194c02ff))
-   change db connection for development ([e4936fc](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e4936fcc1e4f6a35d34f8b69bb99fb94d56979b3))
-   **frontend-statistical-report:** check if the category is exist ([cdea511](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cdea5119cd61368ad4dceeabad20f3a728ccfcf3))
-   **frontend-statistical-report:** pass category id to toggle handler service ([61e4b53](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/61e4b53cd29b9baf22aeef1c2938edaaa230835e))
-   later ([e1ea08e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e1ea08e8480d82f07684cc3145779bf52935b737))
-   **plan:** optimized plan next approving department query ([45f0190](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/45f0190f6b74947fa17a9b12359cb209052efbef))
-   **statistical-report:** added items to cycle select field ([2299f9e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2299f9e0ab627fa5ee8121bfc08ff85b81c72eb4))
-   **statistical-report:** allow full access to enter data ([b8179fe](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b8179fe7b6addb923e7a8bff2e191af5b21d6ecc))
-   **statistical-report:** color table and add dividers ([1a666d4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/1a666d45f8d04f69dc445c849f05a4596e23c14b))
-   **statistical-report:** coloring category names and bolder weight for ease of reading ([6be2cc8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6be2cc8d7707d34177d2eae91b150b6a5883dfed))
-   **statistical-report:** created a command for filling in missing results ([d5b556c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d5b556c9f053ff708788e2fef439ac7f7b980f38))
-   **statistical-report:** fields width ([3e9c3f6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3e9c3f60e630905ad9bd72311a1b7414c0903420))
-   **statistical-report:** getting for edit ([4977f25](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4977f25fd881f8096b0e674527bbc442fd77cd7d))
-   **statistical-report:** handle statistical report result before doing any action ([85ac90b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/85ac90b41762d9b4a68ad6365593f1a981a2062d))
-   **statistical-report:** hide edit button if is statistical report published ([b35f081](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b35f0814e29de89f2a251e67905c25861d3f5aff))
-   **statistical-report:** made categories required ([4f36664](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4f3666468ff2edaec3e86c5cb170a9a3ed23e7f3))
-   **statistical-report:** missing name for create command ([8c4e8ad](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8c4e8adf3cf89c06158a7a2aedfe351491ffa4b2))
-   **statistical-report:** nullable values for yearly result ([842cbd1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/842cbd1fb762d44327cbf80cdea2473772d2f43a))
-   **statistical-report:** optimized execution of get dto ([c8cf297](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c8cf297a635a72a8db55c29bc7293d620f6f46d1))
-   **statistical-report:** pagination ([a1f523f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a1f523f7600aa7521e0a5c5a448336c4a74d625d))
-   **statistical-report:** removed `app-value-loading` component from detail page ([9444238](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9444238636177298a759894d86a41e668bda194b))
-   **statistical-report:** rename misc controller to lock controller ([84a545a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/84a545ab338f12d85427dac2217f9b8a2a5a7320))
-   **statistical-report:** return null as a result to the category if no results were entered ([0d926b2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0d926b26eaba67895c75ff8773fd0a98c9c38355))
-   **statistical-report:** show `N/A` instead of zero in value ([cd4f2e3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/cd4f2e3fd8aa63e40716f333423603b896be3aeb))
-   **statistical-report:** translate strings into arabic ([448219f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/448219f6114b23cebbb584c6c94cab4d6c2446c2))
-   **StatisticalReportGetDtoMappingStrategy:** fix constants names ([e9bbf1d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e9bbf1d03a3b6bd0d6170558916879654c09b194))
-   **user:** linkable departments in user details ([5076357](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5076357265b70d1d97b62cea3378264f67b02f47))
-   **user:** mapping strategy for get dto not fetching permissions ([9e34bdf](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9e34bdf2da2061d8acacd7c3b57749c3cc5a4e33))
-   **user:** return assigned permissions only without overriden permissions ([e3c48a0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e3c48a0899b120c020d03f340cfb0314e2c5ec89))

### Features

-   add some functionalities to list and details components ([3d0b22d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3d0b22d62426d26ae53818929f8388c9c6bdec3d))
-   **backend:** added sql function to generate a range of numbers ([72b1c0b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/72b1c0b4758f87d3be7455929a1c4cb2675f0c51))
-   **department-report-result:** automatic select only results checkbox ([758a923](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/758a92305dcd2ade4cd08c20535f677f9051ec27))
-   **frontend-statistical-report:** add some pages and functionalities in statistical report ([58defa3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/58defa384a3a7a020c4d617fbb5779c36504eb05))
-   **frontend:** add publish button ([2836413](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2836413ab98ba19ed4ee450141da79a53fe67d3a))
-   **plan:** cycle reset type for approvals ([b1e6a1e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b1e6a1ebced53bbdd70578510cb20c2e3ffcdd37))
-   **setting:** added option to reset plan approval cycle ([2dedfa0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2dedfa00ec3058d1bcb2e6b4264312db6f994f12))
-   **statistical-report:** add chart for statistical report with filters ([f541b5b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f541b5bc8b305558c6bee831755a2777abf2af0d))
-   **statistical-report:** add isLocked and isPublished to get dto ([facefc4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/facefc4bbc13d8049c664deedf856b8347c19ada))
-   **statistical-report:** added models to frontend ([fa2bab1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fa2bab112a43d71ba4bfcad09a24cdf29558d2ed))
-   **statistical-report:** added sql function for computing category result ([01d7d56](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/01d7d5654fd8b447533628df3d1c32efe2aaa2d3))
-   **statistical-report:** added yearly results to get dto ([843594f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/843594f53ff7b35ffea30f52ea1696b408feb1fe))
-   **statistical-report:** statistical report module boilerplate ([c477fca](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c477fcab0e4c0c1c2c8f61f612a7293a98c0237e))
-   **user:** added departments and assigned permissions sections in detail page ([87a35fe](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/87a35fed391ab06a854dfa721c5d381347705670))
-   **user:** added user detail page ([a0c6d5e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/a0c6d5e499ec9446e055cefd735426888e9ad503))
-   **user:** added user list dto ([faf6c8e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/faf6c8e21561a748b5f17bd85615ee5e49a644ae))
-   **user:** get api returns information regarding departments and permissions ([5fc8541](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5fc85419efccd92ca99c748aba1576cff591382d))
-   **user:** used user list dto in list handler ([38e0f3e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/38e0f3e6435f1482bc621d4274edccfcb03e6178))

## [1.21.5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.21.4...v1.21.5) (2023-03-03)

### Bug Fixes

-   **plan:** sql function for obtaining next approving department ([57699fd](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/57699fd47962254fabcbf9ab8ced29f867be6ab2))

## [1.21.4](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.21.3...v1.21.4) (2023-02-22)

### Bug Fixes

-   **backend:** return only active kpis from misc api ([9cb0905](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9cb09057b55499ffa972c49710efd7708e1837bb))
-   **plan:** better handling for approval status in plans list ([e3d57da](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e3d57da007af017e20df9af864915d23c827dfab))
-   **plan:** next approving department ([4aac321](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4aac321c4385ff9f13fdb158daadbab7b72b9819))
-   **plan:** remove is approved field in plans detail ([53b2a40](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/53b2a40cc65e42ec4862659e017f2b3f44eb581b))
-   **plan:** users with plan initial approval permission can view all plans ([6774f44](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6774f44aebc31a2cebac0baf5b844fef078b1e36))

### Reverts

-   **frontend:** ⏪️ revert deleted department dashboard component ([b19ff60](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b19ff601170f8d8862296e4f9bc64d396a4e1eea))

## [1.21.3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.21.2...v1.21.3) (2023-02-20)

### Bug Fixes

-   **backend:** added back statistical reporting module ([f8157c2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f8157c26ebd3dad840c5b836b54f1359c114af01))
-   **ci:** publish backend only Injaz project ([5c33aea](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5c33aeaf976295dab39cf6fc5d6a2779bb232497))

## [1.21.2](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.21.1...v1.21.2) (2023-02-17)

### Bug Fixes

-   **backend:** removed query filter for user entity ([4d45699](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/4d456994b480e2b32aec86b47c42a83c89bafcad))
-   **frontend-progress-ring:** use `transform` attribute on circle tag instead of transform property ([e17d0a7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e17d0a742eb68a88e382e34cdc35f56981c3194e))

## [1.21.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.21.0...v1.21.1) (2023-02-16)

### Bug Fixes

-   **backend-kpi-data-entry-result:** simplify conditional ternary expression ([644144a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/644144a9833550fe3bddceff666a9b5e246117d6))
-   **backend-plan-task:** order plan tasks by from date ([c2fe7c0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c2fe7c09ca0b4ac700050000c1b0a2fe6b891571))
-   **backend-plan:** order plan tasks amd subtasks by From date ([124fd82](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/124fd82f720c63ed9e5a586810060256422fe002))
-   **backend-plan:** remove checking from `To` when get plan task progress ([ca42eef](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ca42eef4b8326570f651f794902bb2df31887e20))
-   **backend-plan:** sort plans by from date then by creation time ([2b2d695](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/2b2d6953c0501b831421c0c0239338b1c3565051))
-   **frontend-i18n:** add missing translation strings ([b493002](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b4930021f098eaf29e7b0fee1190924cd7c38a52))
-   **frontend-plan-task:** remove `isApproved` column from detail page and set weight to percentage ([ef73265](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ef73265e9d3322c571fd29235b9cd730393c8dc3))
-   **frontend-plan:** change string from `is-approved` to `is-done` ([fedff7f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/fedff7fec2b7cd0f2205b37edf15f4da7836fec4))
-   **plan:** delete child plan entities when plan is deleted ([279b8c0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/279b8c0444c8990039094a8e7a42d046941a1b4a))

# [1.21.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.20.1...v1.21.0) (2023-02-15)

### Bug Fixes

-   **backend-improvement-opportunity:** remove unused properties from improvement opportunity model ([c7bd393](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/c7bd3934c40b882ae08c99d1d5b1ece96cbc0450))
-   **backend-improvement-opportunity:** use dynamic principles instead of static one ([7dd04e1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7dd04e1fab7848ab2172efbd6cf7bc420982dfbc))
-   **department:** missing navigation property. ([619368d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/619368d9e7841de85989c59df66f52440a1bd556))
-   **frontend-opportunity:** get principles when select standards in the opportunity form ([78e1fb6](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/78e1fb6513e49e85d8113d5f05dc8b1a0bbf7fdf))
-   **frontend-opportunity:** pass tournament id instead of tournament object to get all standards ([e575f01](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/e575f01bf86bc71b66f0d6ff6ff9d4726d5711cd))
-   **frontend:** remove unused properties from loading component ([9c9afae](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9c9afae606d630f5c5e06a919d800424001d8d07))
-   **improvement-opportunity:** set tournament and standards not required in opportunity ([3a2e4ac](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3a2e4ac0554e7b9a3ecd7491be1d28e17b9e8443))
-   **kpi:** added missing navigational property ([d7f1a6a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d7f1a6a509646de4fbcf74fa367d2c1a132ebe72))
-   **notification:** force save at create handler ([24d7889](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/24d7889d801189c113e6deb481c2b6bbe597b276))
-   **statistical-report:** added unit test for create/update handler ([ee7ad87](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/ee7ad87f3a9f4d814e0e2116032f8f51e189247c))
-   **strategic-goals:** fix and refactor strategic goal group and order ([3a88fd8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/3a88fd80245332666be018fc9072a6a32f6f6bdc))
-   **strategic-goals:** fix and refactor strategic goal group and order ([040d8c5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/040d8c5fbdfd8a08d6fb5c76a2fe47dcd4d6c953))

### Features

-   **backend:** abstraction layer for interval based hosted services ([6ecb8ae](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/6ecb8ae8ef6d9dbea7ca81ff1eed9142797326f3))
-   **kpi:** added data entry request notification model ([86b887d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/86b887d43871d725f94ebf2fe869391b2f9c908b))
-   **kpi:** added data entry request notifications on request start time ([b3d9c50](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/b3d9c502390284d6cb8ae652104afbb661402595))
-   **statistical-report:** added commands and queries. ([7fad7f7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7fad7f70a9fe34f6f3da7d4f69747f8eac2871f5))
-   **statistical-report:** added controllers. ([71ce56c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/71ce56cb5f89462a557773a0ba0c87dcc353dfc6))
-   **statistical-report:** added dtos. ([7b51e1a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7b51e1aafea88ee15c50f1f00d139e713fa925e5))
-   **statistical-report:** added handlers. ([43d890e](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/43d890ef50d7c4bcc9ed13cb34413a7577e13a19))
-   **statistical-report:** added models + migrations. ([8c31b2f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/8c31b2f7a04a6cff9f052a673802fe1218bf3688))
-   **statistical-report:** Added module boiler plate. ([64e8fad](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/64e8fad3a0515ac8f2ce60984ace1a9a07a9779d))
-   **statistical-report:** added permissions. ([d9a96c5](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d9a96c5eabefe00a5126f99f8d5c362b9e7d94ef))
-   **statistical-report:** added repository + implementation. ([9e3681f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9e3681f631333efc4ed6fde1407b3a60ad39b668))

## [1.20.1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.20.0...v1.20.1) (2023-02-13)

### Bug Fixes

-   **frontend-carrying-capacity:** enhance carrying capacity pages UI ([0b5aa63](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0b5aa63e3d56acad2ccc92ab7f928f0410ff9fc4))
-   **frontend-carrying-capacity:** remove background blur from cards ([eb0569b](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/eb0569bd9c431629ab74b3611022464b5903af5b))

# [1.20.0](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.19.10...v1.20.0) (2023-02-12)

### Bug Fixes

-   **frontend-style:** remove duplicated property in css ([be3394a](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/be3394a1e060f61fc1916c650bf99d94da7726c1))

### Features

-   **frontend-excel-sheet:** move excel sheet module to inside injaz module ([acea679](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/acea6792c208e6f814996b3b87526f9961ed9f61))

## [1.19.10](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.19.9...v1.19.10) (2023-02-12)

### Bug Fixes

-   split update version command into separate sh file ([5bf0477](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/5bf0477f30247cba826f00e2a0fb51d0bb2f286c))

## [1.19.9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.19.8...v1.19.9) (2023-02-12)

### Bug Fixes

-   add missing backslash to the release exec ([544a781](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/544a7817d3d8e889e20db248da8fae31043890e1))
-   add missing backslash to the release exec ([f902a7d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/f902a7df7eeba6adb7bd99a898e551c128e91b84))

## [1.19.8](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.19.7...v1.19.8) (2023-02-12)

### Bug Fixes

-   add missing project description to package.json ([9722a0c](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9722a0c3275929bbacc0925065fd2eac464b6f42))

## [1.19.7](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/compare/v1.19.6...v1.19.7) (2023-02-12)

### Bug Fixes

-   **backend-goal:** correct a major error caused by a reviewer who was inebriated ([d5ddbc1](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/d5ddbc144b13b42a893df894ad932af027234844))
-   **backend:** fix typo ([95af284](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/95af2842913759804983259c200ad62aa179e2e8))
-   **backend:** fix typo and add migration to fix it in database ([9167a93](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/9167a93f950f59f3bca81bd87185757e7d280881))
-   **backend:** remove commented code ([0d95eb3](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/0d95eb329779656c7803c4044e0fc27c11237000))
-   **frontend-style:** rearrange classes ([10ce35f](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/10ce35fb67a672b75bd0e6fee1ef840142d12d43))
-   **frontend:** remove unused components ([18b53b9](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/18b53b9f84842afd69ffb9e5cdd075c7ebb9cb59))
-   **frontend:** remove unused styles ([116957d](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/116957dfe16a9bdcf36a80270893446a4a86a17d))
-   remove comments ([7e50305](https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz/commit/7e50305e6fcfc1b191427bf0bca0600536a029c3))
