{"name": "injaz", "version": "1.12.0", "description": "Injaz Project", "keywords": ["injaz"], "homepage": "https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/#readme", "bugs": {"url": "https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp/issues"}, "repository": {"type": "git", "url": "https://github.com/SYSCODEIA-IT-SOLUTIONS/injaz-ajp.git"}, "license": "ISC", "author": "Syscodeia IT Solution", "main": "index.js", "scripts": {"check:all": "npm-run-all build check:lint check:cspell check:format check:style", "build": "npm-run-all build:backend build:web", "build:backend": "cd ./backend; dotnet build -c=Release", "build:web": "cd ./web; npm run buildProd", "check:cspell": "npx cspell@7.3.9 --no-progress --show-suggestions --show-context --unique", "check:format": "npx prettier@2.8.8 . --check", "check:lint": "cd ./web; npx eslint@7.30.0 .", "check:style": "npx stylelint@15.9.0 \"web/projects/**/*.{css,scss}\"", "prepare": "husky install"}, "devDependencies": {"@commitlint/cli": "^17.4.2", "@commitlint/config-conventional": "^17.4.2", "cspell": "^6.22.0", "eslint": "^8.33.0", "husky": "^8.0.3", "lint-staged": "^13.1.1", "npm-run-all": "^4.1.5", "prettier": "^2.8.3", "prettier-plugin-tailwindcss": "^0.2.2", "sort-package-json": "^2.4.1", "stylelint-config-idiomatic-order": "^10.0.0", "stylelint-config-prettier-scss": "^1.0.0", "stylelint-config-sass-guidelines": "^11.0.0", "stylelint-config-standard": "^36.0.0", "stylelint-config-standard-scss": "^13.0.0", "stylelint-prettier": "^3.0.0"}, "engines": {"node": "16.10.0", "npm": ">=7"}}