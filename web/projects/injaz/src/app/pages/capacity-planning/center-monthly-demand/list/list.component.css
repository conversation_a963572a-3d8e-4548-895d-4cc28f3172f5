/* Responsive table styles */
.responsive-data-card {
    transition: all 0.2s ease-in-out;
    border: 1px solid #e9ecef !important;
    background-color: #fff;
}

.responsive-data-card:hover {
    border-color: #007bff !important;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.responsive-data-card .fw-bold {
    font-size: 0.75rem;
    color: #007bff !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.responsive-data-card .fs-6 {
    font-size: 0.9rem !important;
    font-weight: 500;
    color: #495057;
}

/* Month column styling */
.month-column {
    background-color: #f8f9fa;
    border-right: 2px solid #007bff;
    font-weight: 600;
    color: #007bff;
}

/* Table row hover effect */
tbody tr:hover {
    background-color: #f8f9fa;
}

/* Responsive grid improvements */
.row.g-2 {
    margin: -0.25rem;
}

.row.g-2 > * {
    padding: 0.25rem;
}

/* Ensure equal height cards */
.h-100 {
    min-height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Improve spacing for small screens */
@media (max-width: 576px) {
    .responsive-data-card {
        margin-bottom: 0.5rem;
    }
    
    .responsive-data-card .fw-bold {
        font-size: 0.7rem;
    }
    
    .responsive-data-card .fs-6 {
        font-size: 0.85rem !important;
    }
}

/* Table caption styling */
.table caption {
    caption-side: top;
    padding: 0.75rem;
    color: #fff;
    text-align: center;
    background-color: #007bff;
    border: 1px solid #007bff;
    border-radius: 0.375rem 0.375rem 0 0;
    font-weight: 600;
    font-size: 1.1rem;
}

/* Table header improvements */
.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #007bff;
    font-weight: 600;
    color: #495057;
    text-align: center;
    vertical-align: middle;
}

/* Actions column */
.actions-column {
    width: 100px;
    min-width: 100px;
}
