/* Responsive table styles */
.responsive-data-card {
    transition: all 0.2s ease-in-out;
    border: 1px solid #e9ecef !important;
    background-color: #fff;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 80px;
    width: fit-content;
    min-width: 120px;
    max-width: 200px;
    margin: 0 auto;
}

.responsive-data-card:hover {
    border-color: #007bff !important;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.responsive-data-card .fw-bold {
    font-size: 0.7rem;
    color: #007bff !important;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    margin-bottom: 0.5rem;
    text-align: center;
    line-height: 1.2;
    word-wrap: break-word;
    hyphens: auto;
}

.responsive-data-card .fs-6 {
    font-size: 0.95rem !important;
    font-weight: 700 !important;
    color: #212529;
    text-align: center;
    word-wrap: break-word;
}

/* Month column styling */
.month-column {
    background-color: #f8f9fa;
    border-right: 2px solid #007bff;
    font-weight: 600;
    color: #007bff;
}

/* Table row hover effect */
tbody tr:hover {
    background-color: #f8f9fa;
}

/* Responsive grid improvements */
.responsive-data-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: flex-start;
    align-items: stretch;
}

.responsive-data-item {
    flex: 0 0 auto;
}

/* Improve spacing for small screens */
@media (max-width: 576px) {
    .responsive-data-container {
        gap: 0.5rem;
        justify-content: center;
    }

    .responsive-data-card {
        min-width: 100px;
        max-width: 150px;
    }

    .responsive-data-card .fw-bold {
        font-size: 0.65rem;
    }

    .responsive-data-card .fs-6 {
        font-size: 0.8rem !important;
    }
}

@media (max-width: 768px) {
    .responsive-data-container {
        justify-content: center;
    }
}

/* Table caption styling */
.table caption {
    caption-side: top;
    padding: 0.75rem;
    color: #fff;
    text-align: center;
    background-color: #007bff;
    border: 1px solid #007bff;
    border-radius: 0.375rem 0.375rem 0 0;
    font-weight: 600;
    font-size: 1.1rem;
}

/* Table header improvements */
.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #007bff;
    font-weight: 600;
    color: #495057;
    text-align: center;
    vertical-align: middle;
}

/* Actions column */
.actions-column {
    width: 100px;
    min-width: 100px;
}
