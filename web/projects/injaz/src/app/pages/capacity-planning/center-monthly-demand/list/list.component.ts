import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { TableController } from '@injaz/common/misc/table/table-controller';
import { NotificationService } from 'mnm-webapp';
import { permissionList } from '@injaz/common/constants';
import { ActivatedRoute, Router } from '@angular/router';
import { Item } from '@injaz/common/models';
import { CenterMonthlyDemandService } from '@injaz/pages/capacity-planning/center-monthly-demand/center-monthly-demand.service';
import { MiscApiService } from '@injaz/core/services';
import { Subscription } from 'rxjs';
import { CenterMonthlyDemand } from '@injaz/pages/capacity-planning/interfaces/center-monthly-demand.interface';
import { TimeDimension } from '@injaz/pages/capacity-planning/interfaces/time-dimension.interface';
import { TimeDimensionService } from '@injaz/pages/capacity-planning/time-dimension/time-dimension.service';

@Component({
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.css']
})
export class ListComponent implements OnDestroy, OnInit {
    public readonly permissionList = permissionList;

    public departments: Item[] = [];
    public years: number[] = [];
    public timeDimensions: TimeDimension[] = [];

    public tableController: TableController<
        CenterMonthlyDemand,
        {
            years?: number[];
            months?: number[];
            department?: Item;
        }
    >;

    public currentlyDeleting: string[] = [];

    public currentlyProcessing: string[] = [];

    // Flag to track if department is selected
    // public isDepartmentSelected = false;

    public readonly months = Array(12)
        .fill(0)
        .map((_, i) => i + 1);

    // Modified structure to group by year
    public groupedItems: {
        year: number;
        items: CenterMonthlyDemand[];
    }[] = [];

    // Subscriptions to clean up
    private subscriptions: Subscription[] = [];

    public constructor(
        private centerMonthlyDemandService: CenterMonthlyDemandService,
        private notificationService: NotificationService,
        private miscApiService: MiscApiService,
        private timeDimensionService: TimeDimensionService,
        router: Router,
        activatedRoute: ActivatedRoute
    ) {
        // Load departments
        this.miscApiService.centerDepartments().subscribe(items => {
            this.departments = items ?? [];
        });

        this.timeDimensionService.list().subscribe(data => {
            this.years = [...new Set(data.items?.map(x => x.year) ?? [])];
            this.timeDimensions = data.items ?? [];
        });

        // Setup TableController
        this.tableController = new TableController<
            CenterMonthlyDemand,
            {
                years?: number[];
                months?: number[];
                department?: Item;
                pageNumber: number;
                pageSize: number;
            }
        >(
            filter =>
                centerMonthlyDemandService.list(
                    filter.data.years,
                    filter.data.months,
                    filter.data.department,
                    filter.pageNumber,
                    filter.pageSize
                ),
            {
                data: {
                    months: null,
                    years: null,
                    department: null,
                    pageNumber: 1,
                    pageSize: 10,
                },
            },
            { routingControls: { router, activatedRoute } }
        );

        // Start the table controller
        this.tableController.start();
    }

    public compareById(item1: any, item2: any): boolean {
        return item1 && item2 && item1.id === item2.id;
    }

    public compareByValue(item1: any, item2: any): boolean {
        return item1 === item2;
    }

    public ngOnInit(): void {
        // Subscribe to filter changes and manual data checks
        this.subscriptions.push(
            this.tableController.items$.subscribe(items => {
                if (items?.length > 0) {
                    this.groupByYear(this.tableController.items);
                } else if (this.tableController.filter.data.department) {
                    // If no items but department is selected, set empty groups
                    this.groupedItems = [];
                }
            })
        );
    }

    public ngOnDestroy(): void {
        this.tableController.stop();

        // Clean up all subscriptions
        this.subscriptions.forEach(sub => sub.unsubscribe());
    }

    public delete(item: CenterMonthlyDemand): void {
        if (!item || !item.id) return;

        // add the id of the item to the being deleted array
        // to disable the delete button in the list.
        this.currentlyDeleting.push(item.id);
        this.centerMonthlyDemandService
            .delete(item.id)
            .pipe(
                finalize(() => {
                    // remove the deleted item id from the being deleted
                    // list when the deletion is complete.
                    this.currentlyDeleting = this.currentlyDeleting.filter(
                        x => x !== item.id
                    );
                })
            )
            .subscribe(message => {
                this.notificationService.notifySuccess(message);
                this.tableController.filter$.next(false);
            });
    }

    // Updated method that groups by year
    private groupByYear(items: CenterMonthlyDemand[]): void {
        if (!items || !Array.isArray(items) || items.length === 0) {
            // Handle the case when items is not an array or empty
            this.groupedItems = [];
            return;
        }

        try {
            // Group by year using type-safe approach
            const yearGroups = new Map<number, CenterMonthlyDemand[]>();

            for (const item of items) {
                if (!item) continue;

                const yearValue =
                    typeof item.time.year === 'number' ? item.time?.year : 0;
                if (!yearGroups.has(yearValue)) {
                    yearGroups.set(yearValue, []);
                }
                yearGroups.get(yearValue)?.push(item);
            }

            // Convert to the required array format with type checking
            this.groupedItems = Array.from(yearGroups.entries()).map(
                ([year, yearItems]) => ({
                    year,
                    items: yearItems.sort((a, b) => {
                        // Type-safe comparison of months
                        const monthA =
                            typeof a.time.monthNumber === 'number'
                                ? a.time.monthNumber
                                : 0;
                        const monthB =
                            typeof b.time.monthNumber === 'number'
                                ? b.time.monthNumber
                                : 0;
                        return monthA - monthB;
                    }),
                })
            );

            // Type-safe sorting of years
            this.groupedItems.sort((a, b) => {
                const yearA = typeof a.year === 'number' ? a.year : 0;
                const yearB = typeof b.year === 'number' ? b.year : 0;
                return yearB - yearA; // Newest first
            });
        } catch (error) {
            console.error('Error in groupByYear:', error);
            this.groupedItems = [];
        }
    }
}
