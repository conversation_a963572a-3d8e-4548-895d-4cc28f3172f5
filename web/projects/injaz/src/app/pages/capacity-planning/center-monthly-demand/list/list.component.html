<app-page pageTitle="{{ 'translate_center_monthly_demands' | translate }}">
    <a
        *appHasPermissionId="permissionList.capacityPlanningWrite"
        tools
        [routerLink]="['', 'capacity-planning', 'center-monthly-demand', 'new']"
        class="btn btn-sm btn-outline-white"
    >
        <i class="fa-light fa-plus me-2"></i>
        <span>{{ 'translate_add_new' | translate }}</span>
    </a>

    <ng-container content>
        <!-- Filter -->
        <app-filter-result-box>
            <!-- Years -->
            <ng-select
                [items]="years"
                [compareWith]="compareByValue"
                multiple="true"
                placeholder="{{ 'translate_search_by_years' | translate }}"
                [(ngModel)]="tableController.filter.data.years"
                (change)="tableController.filter$.next(true)"
            >
            </ng-select>

            <!-- Months -->
            <ng-select
                [items]="months"
                [compareWith]="compareByValue"
                multiple="true"
                placeholder="{{ 'translate_search_by_month' | translate }}"
                [(ngModel)]="tableController.filter.data.months"
                (change)="tableController.filter$.next(true)"
            >
            </ng-select>

            <!-- Departments - Required -->
            <ng-select
                [items]="departments"
                bindLabel="name"
                [compareWith]="compareById"
                placeholder="{{ 'translate_police_station' | translate }}"
                [(ngModel)]="tableController.filter.data.department"
                (change)="tableController.filter$.next(true)"
                [ngClass]="{
                    'is-invalid': !tableController.filter.data.department
                }"
            >
            </ng-select>
        </app-filter-result-box>

        <div
            class="row-fluid my-3"
            *ngIf="!tableController.filter.data.department"
        >
            <div class="border-red-100 bg-yellow-100 p-5">
                <i class="fa fa-x2 fa-exclamation-triangle mx-2"></i>
                {{ 'translate_select_the_police_station_first' | translate }}
            </div>
        </div>

        <!-- Table - Only shown when department is selected -->
        <app-list-loading
            [items]="tableController.items"
            *ngIf="tableController.filter.data.department"
        >
            <div class="table-responsive">
                <!-- Department header - Shows the selected department name -->
                <div class="department-header mb-2 bg-primary p-2 text-white">
                    <h4 class="mb-0 text-lg font-bold">
                        {{
                            tableController.filter.data.department.name ||
                                'Department'
                        }}
                    </h4>
                </div>

                <!-- Year groups -->
                <ng-container *ngFor="let yearGroup of groupedItems">
                    <!-- Year groups -->
                    <table class="table-striped table-bordered mb-4 table">
                        <caption
                            class="table-secondary border-1 my-1 border-primary bg-primary-200 text-right text-lg font-bold"
                        >
                            <div class="m-3">
                                {{ 'translate_year' | translate }} :
                                {{ yearGroup.year }}
                            </div>
                        </caption>
                        <thead>
                            <tr>
                                <!-- Month Column -->
                                <th class="text-center" style="width: 150px;">{{ 'translate_month' | translate }}</th>
                                <!-- Data Column -->
                                <th>{{ 'translate_details' | translate }}</th>
                                <!-- Actions Column -->
                                <th class="text-center" style="width: 100px;">
                                    <i class="fa-light fa-gear"></i>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of yearGroup.items">
                                <!-- Month Column -->
                                <td class="text-center align-top month-column">
                                    <strong>{{ item.time.monthNumber | monthName }}</strong>
                                </td>

                                <!-- Data Column with responsive layout -->
                                <td class="p-3">
                                    <div class="row g-2">
                                        <!-- Employee Data -->
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_employees_available' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.employeesAvailable }}</div>
                                            </div>
                                        </div>

                                        <!-- Fast Services -->
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_fast_services_count' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.fastServicesCount }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_fast_service_time' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.fastServiceTime }}</div>
                                            </div>
                                        </div>

                                        <!-- Regular Services -->
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_regular_services_count' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.regularServicesCount }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_regular_service_time' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.regularServiceTime }}</div>
                                            </div>
                                        </div>

                                        <!-- Complex Services -->
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_complex_services_count' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.complexServicesCount }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_complex_service_time' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.complexServiceTime }}</div>
                                            </div>
                                        </div>

                                        <!-- Work Time Parameters -->
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_daily_work_hours_per_employee' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.dailyWorkHoursPerEmployee }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_work_days_per_month' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.workDaysPerMonth }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_work_hours_per_day' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.workHoursPerDay }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_religious_and_national_occasions_days_per_month' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.religiousAndNationalOccasionsDaysPerMonth }}</div>
                                            </div>
                                        </div>

                                        <!-- Actual Usage Data -->
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_actual_transactions_per_month' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.actualTransactionsPerMonth }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_actual_customers_per_month' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.actualCustomersPerMonth }}</div>
                                            </div>
                                        </div>

                                        <!-- Facilities Data -->
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_available_seats_in_center' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.availableSeatsInCenter }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_available_parking_at_center' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.availableParkingAtCenter }}</div>
                                            </div>
                                        </div>

                                        <!-- Notes -->
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_notes' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.notes || '-' }}</div>
                                            </div>
                                        </div>

                                        <!-- Computed Service Metrics -->
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_total_service_count' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.totalServiceCount }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_total_service_time_minutes' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.totalServiceTimeMinutes }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_average_service_time_minutes' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.averageServiceTimeMinutes }}</div>
                                            </div>
                                        </div>

                                        <!-- Capacity Metrics -->
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_total_working_minutes_per_month' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.totalWorkingMinutesPerMonth }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_capacity_utilization_minutes' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.capacityUtilizationMinutes }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_capacity_utilization_percentage' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.capacityUtilizationPercentage }}%</div>
                                            </div>
                                        </div>

                                        <!-- Efficiency Metrics -->
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_services_per_employee' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.servicesPerEmployee }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_services_per_working_day' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.servicesPerWorkingDay }}</div>
                                            </div>
                                        </div>

                                        <!-- Customer Metrics -->
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_customers_per_service' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.customersPerService }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_transactions_per_service' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.transactionsPerService }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_transactions_per_customer' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.transactionsPerCustomer }}</div>
                                            </div>
                                        </div>

                                        <!-- Service Channels -->
                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_happiness_center_volume' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.happinessCenterVolume }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_moi_app_volume' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.moiAppVolume }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_ajp_app_volume' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.ajpAppVolume }}</div>
                                            </div>
                                        </div>

                                        <div class="col-12 col-sm-6 col-md-4 col-lg-3">
                                            <div class="responsive-data-card rounded p-2 h-100">
                                                <div class="fw-bold text-primary small mb-1">
                                                    {{ 'translate_website_volume' | translate }}
                                                </div>
                                                <div class="fs-6">{{ item.websiteVolume }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </td>

                                <!-- Actions Column -->
                                <td class="text-center align-top">
                                    <app-dropdown>
                                        <ng-container
                                            *appHasPermissionId="
                                                permissionList.capacityPlanningWrite
                                            "
                                        >
                                            <!-- Edit -->
                                            <a
                                                [routerLink]="[
                                                    '',
                                                    'capacity-planning',
                                                    'center-monthly-demand',
                                                    'edit',
                                                    item.id
                                                ]"
                                                class="btn btn-sm btn-info"
                                                [appTooltip]="
                                                    'translate_edit' | translate
                                                "
                                            >
                                                <i
                                                    class="fa-light fa-edit fa-fw"
                                                ></i>
                                            </a>
                                        </ng-container>

                                        <ng-container
                                            *appHasPermissionId="
                                                permissionList.capacityPlanningDelete
                                            "
                                        >
                                            <button
                                                [disabled]="
                                                    currentlyDeleting.includes(
                                                        item.id
                                                    )
                                                "
                                                (confirm)="delete(item)"
                                                [swal]="{
                                                    title:
                                                        'translate_delete_this_item_question_mark'
                                                        | translate,
                                                    confirmButtonText:
                                                        'translate_yes'
                                                        | translate,
                                                    cancelButtonText:
                                                        'translate_cancel'
                                                        | translate,
                                                    showCancelButton: true,
                                                    showCloseButton: true
                                                }"
                                                class="btn btn-sm btn-danger"
                                                [appTooltip]="
                                                    'translate_delete'
                                                        | translate
                                                "
                                            >
                                                <i
                                                    class="fas fa-trash fa-fw"
                                                ></i>
                                            </button>
                                        </ng-container>
                                    </app-dropdown>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </ng-container>
            </div>

            <app-table-pagination
                [tableController]="tableController"
            ></app-table-pagination>
        </app-list-loading>
    </ng-container>
</app-page>
